<?php

namespace Modules\Core\Models\Institutions;

use Modules\Core\Models\AcademicYearModel;
use Modules\Core\Models\Person;
use Modules\Core\Models\Settings\AcademicYear;
use Modules\Core\Models\Settings\AcademicYearTeachingPeriod;
use Modules\Core\Models\Settings\EnrolmentType;
use Modules\Core\Models\Settings\EntryMode;
use Modules\Core\Models\Settings\FamiliarLanguage;
use Modules\Core\Models\Settings\LearnerSponsor;
use Modules\Core\Models\Settings\PostPrimaryInstitutionCourse;
use Modules\Core\Models\Settings\ReturneeReason;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\Core\Models\Settings\SettingInterSchCurriculum;
use Modules\Core\Models\Settings\SettingInterSchEducationGrade;
use Modules\Core\Models\Settings\TeachingPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\LearnerManagement\Models\PrimaryLearnerPerformance;
use Modules\LearnerManagement\Models\SecondaryLearnerPerformance;

class LearnerEnrolment extends AcademicYearModel
{
    use HasFactory;

    protected $guarded = [];

    // protected $table = 'year_2023.learner_enrolments';
    protected $table = 'learner_enrolments';
    //protected $hidden = ['date_created', 'date_updated'];

    const CREATED_AT = 'date_created';

    const UPDATED_AT = 'date_updated';

    // public function __construct(array $attributes = array())
    // {
    //     parent::__construct($attributes);
    //     // $this->setConnection('pgsql-period');
    //     // dd(333);
    // }

    public function learner(): BelongsTo
    {
        return $this->belongsTo(Learner::class, 'learner_id');
    }

    public function enrolment_type(): BelongsTo
    {
        return $this->belongsTo(EnrolmentType::class, 'enrolment_type_id');
    }

    public function person(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'learner_id');
    }

    public function familiar_language(): BelongsTo
    {
        return $this->belongsTo(FamiliarLanguage::class, 'familiar_language_id');
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class, 'school_id');
    }

    public function sponsor(): BelongsTo
    {
        return $this->belongsTo(LearnerSponsor::class, 'sponsor_id');
    }

    public function entry_mode(): BelongsTo
    {
        return $this->belongsTo(EntryMode::class, 'entry_mode_id');
    }

    public function school_course(): BelongsTo
    {
        return $this->belongsTo(SchoolCourse::class, 'course_id');
    }

    public function post_primary_institution_course(): BelongsTo
    {
        return $this->belongsTo(PostPrimaryInstitutionCourse::class, 'post_primary_institution_course_id');
    }

    public function institution_examined_course(): BelongsTo
    {
        return $this->belongsTo(InstitutionExaminedCourse::class, 'institution_examined_course_id');
    }

    public function scopeSchoolType($query, $schoolTypeId)
    {
        return $query->whereHas('school', function (Builder $query) use ($schoolTypeId) {
            $query->where('school_type_id', $schoolTypeId);
        });
    }

    public function education_grade(): BelongsTo
    {
        return $this->belongsTo(SchoolEducationGrade::class, 'education_grade_id');
    }

    public function international_curriculum(): BelongsTo
    {
        return $this->belongsTo(SettingInterSchCurriculum::class, 'inter_sch_curriculum_id');
    }

    public function international_education_grade(): BelongsTo
    {
        return $this->belongsTo(SettingInterSchEducationGrade::class, 'inter_sch_education_grade_id');
    }

    public function returnee_reason(): BelongsTo
    {
        return $this->belongsTo(ReturneeReason::class, 'returnee_reason_id');
    }

    public function academic_year(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class, 'academic_year_id');
    }

    public function teaching_period(): BelongsTo
    {
        return $this->belongsTo(TeachingPeriod::class, 'teaching_period_id');
    }

    public function academic_year_teaching_period(): BelongsTo
    {
        return $this->belongsTo(AcademicYearTeachingPeriod::class, 'teaching_period_id');
    }

    public function school_survey(): BelongsTo
    {
        return $this->belongsTo(SchoolSurvey::class, 'school_survey_id');
    }

    public function transfer(): HasOne
    {
        return $this->hasOne(LearnerTransfer::class, 'enrolment_id');
    }

    public function claim(): HasOne
    {
        return $this->hasOne(LearnerClaim::class, 'enrolment_id');
    }

    public function promotion(): HasOne
    {
        return $this->hasOne(LearnerPromotion::class, 'enrolment_id');
    }

    public function transition(): HasOne
    {
        return $this->hasOne(LearnerTransition::class, 'enrolment_id');
    }

    public function completion(): HasOne
    {
        return $this->hasOne(LearnerCompletion::class, 'enrolment_id');
    }

    public function primary_performances(): HasMany
    {
        return $this->hasMany(PrimaryLearnerPerformance::class, 'learner_id');
    }

    public function secondary_performances(): HasMany
    {
        return $this->hasMany(SecondaryLearnerPerformance::class, 'learner_id');
    }
    
}
