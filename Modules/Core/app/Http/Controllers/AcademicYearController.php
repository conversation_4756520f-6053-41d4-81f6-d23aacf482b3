<?php

namespace Modules\Core\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Core\Models\Settings\AcademicYear as SettingsAcademicYear;
use Illuminate\Support\Facades\Cache;


class AcademicYearController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function changeAcademicYear(Request $request)
    {
        // return view('core::index');
        if ($request->has('year')) {
            // session(['year' => $request->year]);
            $year = null;
            try {
                $year_id = $request->year;
                // dd($year_id);
                $year = SettingsAcademicYear::findOrFail($year_id);
                $selected_cache_key = 'selected-year-user-'.$request->user()->id;
                $active_year = get_active_academic_year();
                $active_cache_key = 'active-year';
                Cache::put($active_cache_key, $active_year->id);

                if ($year->status == 'archived') {
                    Cache::put($selected_cache_key, $year->id);
                } else {
                    Cache::forget($selected_cache_key);
                }
            } catch (\Exception $e) {

            }
            return redirect()->back();
        }
    }
}
