<?php

namespace Modules\Core\Http\Controllers\Api\V1\District\Approvals;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Core\Traits\InteractsWithPerson;
use Modules\Core\Traits\LearnerEnrolmentApplicationsFilter;
use Modules\EmisReturns\Livewire\Traits\Learners\StoreLearnerDataTrait;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;

class DistrictLearnerEnrolmentApplicationsApiController extends Controller
{
    use LearnerEnrolmentApplicationsFilter, StoreLearnerDataTrait, InteractsWithPerson;

    protected int $perPage = 15;

    public function __construct()
    {
        $this->middleware('upperLocalGovernment');
    }
    /**
     * Get all Learner Enrolment Applications
     */
    public function index(Request $request, string $type): LengthAwarePaginator
    {
        $perPage = $request->filled('per_page') ? intval($request->input('per_page')) : $this->perPage;

        return $this->filter($request->all(), $type)->ofLocalGovernment(auth()->user()->upper_local_government_id)->paginate($perPage);
    }

    /**
     * Approve the Learner Enrolment Application by Ministry User
     */
    public function approve($applicationNumber): LearnerEnrolmentApplication
    {
        try {
            $application = LearnerEnrolmentApplication::query()
                ->pending()
                ->ofLocalGovernment(auth()->user()->upper_local_government_id)
                ->where('application_number', Str::upper($applicationNumber))
                ->with([
                    'user_account',
                    'school.school_type',
                    'education_grade',
                    'country',
                    'parent_country',
                    'district_of_birth',
                    'post_primary_institution_course',
                    'institution_examined_course',
                    'international_calendar',
                    'international_curriculum',
                    'international_education_grade',
                    'international_education_level',
                    'familiar_language',
                    'deo.person',
                    'moes_staff',
                ])
                ->firstOrFail();

            DB::transaction(function () use ($application) {
                //create learner
                $this->saveLearnerEnrolment($application);
                $application->update([
                    'approved_by' => auth()->id(),
                    'date_approved' => now(),
                    'approval_status' => 'approved',
                ]);
            });

            $application->refresh();
            return $application;
        } catch (ModelNotFoundException $exception) {
            abort(500, 'Cannot find the Learner Enrolment Application you are trying to approve');
        }
    }

    /**
     * Reject the Learner Enrolment Application by DEO
     *
     * @return void
     */
    public function reject(Request $request, $applicationNumber)
    {
        $request->validate([
            'reason' => 'required|string|max:5000',
        ], [
            'reason.required' => 'Give a reason why this application is being rejected',
            'reason.max' => 'Text should not be more than 5000 characters',
        ]);

        try {
            $application = LearnerEnrolmentApplication::with([
                    'user_account',
                    'school.school_type',
                    'education_grade',
                    'country',
                    'district_of_birth',
                    'post_primary_institution_course',
                    'institution_examined_course',
                    'international_calendar',
                    'international_curriculum',
                    'international_education_grade',
                    'international_education_level',
                    'familiar_language',
                    'deo.person',
                    'moes_staff',
                ])
                ->where('application_number', Str::upper($applicationNumber))
                ->ofLocalGovernment(auth()->user()->upper_local_government_id)
                ->firstOrFail();

            // Update application with approval details
            $application->update([
                'approved_by' => auth()->id(),
                'date_approved' => now(),
                'reject_reason' => $request->reason,
                'approval_status' => 'rejected',
            ]);

            $application->refresh();
            return $application;
        }
        catch (ModelNotFoundException $exception) {
            abort(500, 'Cannot find the Learner Enrolment Application you are trying to reject');
        }
    }
}
