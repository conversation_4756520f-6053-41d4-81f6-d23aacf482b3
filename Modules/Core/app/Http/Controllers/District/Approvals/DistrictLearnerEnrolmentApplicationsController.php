<?php

namespace Modules\Core\Http\Controllers\District\Approvals;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Str;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Settings\SchoolType;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;

class DistrictLearnerEnrolmentApplicationsController extends Controller
{
    protected int $perPage = 15;

    public function __construct()
    {
        $this->middleware('upperLocalGovernment');
    }

    public function pending(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Enrolment Applications', '1', 'View', 'User checked Pending Learner Enrolment applications list', 'district');

        $applications = LearnerEnrolmentApplication::query()
            ->with(['deo', 'moes_staff', 'school.school_type'])
            ->ofLocalGovernment(auth()->user()->upper_local_government_id)
            ->pending()
            ->latest()
            ->paginate($this->perPage)
            ->toJson();
        $schoolTypes = SchoolType::query()->select('id', 'name', 'display_name')->get();

        return view('core::district.approvals.learner-enrolment-applications.index')
            ->with('applications', $applications)
            ->with('schoolTypes', $schoolTypes)
            ->with('type', 'Pending');
    }

    public function approved(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Enrolment Applications', '1', 'View', 'User checked Approved Learner Enrolment applications list', 'district');

        $applications = LearnerEnrolmentApplication::query()
            ->with(['deo', 'moes_staff', 'school.school_type'])
            ->ofLocalGovernment(auth()->user()->upper_local_government_id)
            ->approved()
            ->latest()
            ->paginate($this->perPage)
            ->toJson();

        $schoolTypes = SchoolType::query()->select('id', 'name', 'display_name')->get();

        return view('core::district.approvals.learner-enrolment-applications.index')
            ->with('applications', $applications)
            ->with('schoolTypes', $schoolTypes)
            ->with('type', 'Approved');
    }

    public function rejected(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Enrolment Applications', '1', 'View', 'User checked Rejected Learner Enrolment applications list', 'district');

        $applications = LearnerEnrolmentApplication::query()
            ->with(['deo', 'moes_staff', 'school.school_type'])
            ->ofLocalGovernment(auth()->user()->upper_local_government_id)
            ->rejected()
            ->latest()
            ->paginate($this->perPage)
            ->toJson();

        $schoolTypes = SchoolType::query()->select('id', 'name', 'display_name')->get();

        return view('core::district.approvals.learner-enrolment-applications.index')
            ->with('applications', $applications)
            ->with('schoolTypes', $schoolTypes)
            ->with('type', 'Rejected');
    }

    public function show($status, $applicationNumber): View
    {
        try {
            $application = LearnerEnrolmentApplication::query()
                ->where('application_number', Str::upper($applicationNumber))
                ->ofLocalGovernment(auth()->user()->upper_local_government_id)
                ->with([
                    'user_account',
                    'school.school_type',
                    'education_grade',
                    'country',
                    'parent_country',
                    'district_of_birth',
                    'post_primary_institution_course',
                    'institution_examined_course',
                    'international_calendar',
                    'international_curriculum',
                    'international_education_grade',
                    'international_education_level',
                    'familiar_language',
                    'deo',
                    'moes_staff',
                ])
                ->firstOrFail();

            //Add to logs
            AdminLogActivity::addToLog('Learner Enrolment Application', '1', 'View', "User checked Learner Enrolment application details - Ref: #{$applicationNumber}", 'district');

            return view('core::district.approvals.learner-enrolment-applications.show')
                ->with('application', $application);
        } catch (ModelNotFoundException $exception) {
            abort(500, 'We cannot find the application you are trying to view');
        }
    }
}
