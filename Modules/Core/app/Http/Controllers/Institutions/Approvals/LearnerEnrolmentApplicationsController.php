<?php

namespace Modules\Core\Http\Controllers\Institutions\Approvals;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Str;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Settings\SchoolType;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;

class LearnerEnrolmentApplicationsController extends Controller
{
    protected int $perPage = 15;

    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (auth()->check()) {
                if (auth()->user()->hasRole('institution-admin')) {
                    $this->institution = auth()->user()->school->load(['school_type']);
                } elseif (auth()->user()->hasRole('institution-user')) {
                    $this->institution = auth()->user()->school_contact->school->load(['school_type']);
                } else {
                    abort(401, 'You do not have the rights to perform this action');
                }
            }

            return $next($request);
        });
    }

    public function draft(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Pre-Registration Applications', '1', 'View', 'User checked Draft Learner Pre-Registration applications list', 'school');

        $applications = LearnerEnrolmentApplication::query()
            ->with(['education_grade'])
            ->draft()
            ->where('school_id', $this->institution->id)
            ->paginate($this->perPage)
            ->toJson();

        $schoolTypes = SchoolType::query()->select('id', 'name', 'display_name')->get();

        return view('core::institution.learner-enrolment-applications.index')
            ->with('applications', $applications)
            ->with('schoolTypes', $schoolTypes)
            ->with('type', 'Draft');
    }

    public function pending(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Pre-Registration Applications', '1', 'View', 'User checked Pending Learner Pre-Registration applications list', 'school');

        $applications = LearnerEnrolmentApplication::query()
            ->with(['education_grade'])
            ->pending()
            ->where('is_draft_yn', false)
            ->where('school_id', $this->institution->id)
            ->paginate($this->perPage)
            ->toJson();
        $schoolTypes = SchoolType::query()->select('id', 'name', 'display_name')->get();

        return view('core::institution.learner-enrolment-applications.index')
            ->with('applications', $applications)
            ->with('schoolTypes', $schoolTypes)
            ->with('type', 'Pending');
    }

    public function approved(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Pre-Registration Applications', '1', 'View', 'User checked Approved Learner Pre-Registration applications list', 'school');

        $applications = LearnerEnrolmentApplication::query()
            ->with(['education_grade'])
            ->approved()
            ->where('is_draft_yn', false)
            ->where('school_id', $this->institution->id)
            ->paginate($this->perPage)
            ->toJson();

        $schoolTypes = SchoolType::query()->select('id', 'name', 'display_name')->get();

        return view('core::institution.learner-enrolment-applications.index')
            ->with('applications', $applications)
            ->with('schoolTypes', $schoolTypes)
            ->with('type', 'Approved');
    }

    public function rejected(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Pre-Registration Applications', '1', 'View', 'User checked Rejected Learner Pre-Registration applications list', 'school');

        $applications = LearnerEnrolmentApplication::query()
            ->with(['education_grade'])
            ->rejected()
            ->where('is_draft_yn', false)
            ->where('school_id', $this->institution->id)
            ->paginate($this->perPage)
            ->toJson();

        $schoolTypes = SchoolType::query()->select('id', 'name', 'display_name')->get();

        return view('core::institution.learner-enrolment-applications.index')
            ->with('applications', $applications)
            ->with('schoolTypes', $schoolTypes)
            ->with('type', 'Rejected');
    }

    public function show($status, $applicationNumber): View
    {
        try {
            $application = LearnerEnrolmentApplication::query()
                ->where('application_number', Str::upper($applicationNumber))
                ->where('school_id', $this->institution->id)
                ->with([
                    'user_account',
                    'school.school_type',
                    'education_grade',
                    'country',
                    'parent_country',
                    'district_of_birth',
                    'post_primary_institution_course',
                    'institution_examined_course',
                    'international_calendar',
                    'international_curriculum',
                    'international_education_grade',
                    'international_education_level',
                    'familiar_language',
                    'deo.person',
                    'moes_staff',
                ])
                ->firstOrFail();

            //Add to logs
            AdminLogActivity::addToLog('Learner Pre-Registration Application', '1', 'View', "User checked Learner Pre-Registration application details - Ref: #{$applicationNumber}", 'school');

            return view('core::institution.learner-enrolment-applications.show')->with('application', $application);
        } catch (ModelNotFoundException $exception) {
            abort(500, 'We cannot find the application you are trying to view');
        }
    }
}
