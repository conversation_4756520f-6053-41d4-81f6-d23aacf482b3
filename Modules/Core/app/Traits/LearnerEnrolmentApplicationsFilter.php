<?php

namespace Modules\Core\Traits;

use Illuminate\Database\Eloquent\Builder;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;

trait LearnerEnrolmentApplicationsFilter
{
    protected $type;

    protected function filter(array $request, $type): Builder
    {
        $applications = LearnerEnrolmentApplication::query()
            ->latest()
            ->with([
                'school.school_type',
                'education_grade',
            ]);

        if ($type === 'pending') {
            $applications = $applications->pending();
        }  elseif ($type === 'approved') {
            $applications = $applications->approved();
        } elseif ($type === 'rejected') {
            $applications = $applications->rejected();
        } elseif ($type === 'draft') {
            $applications = $applications->draft();
        }

        if ($this->filled($request, 'application_number')) {
            $applications = $applications->where('application_number', $request['application_number']);
        }

        if ($this->filled($request, 'sex')) {
            $applications = $applications->where('gender', $request['sex']);
        }

        if ($this->filled($request, 'search_term')) {
            $applications = $applications->where('first_name', 'iLike', '%'.$request['search_term'].'%')
                            ->orWhere('surname', 'iLike', '%'.$request['search_term'].'%')
                            ->orWhere('other_names', 'iLike', '%'.$request['search_term'].'%');
        }

        if ($this->filled($request, 'local_government_id')) {
            $applications = $applications->where('local_government_id', $request['local_government_id']);
        }

        if ($this->filled($request, 'school_type_id')) {
            $applications = $applications->whereRelation('school', 'school_type_id', $request['school_type_id']);
        }

        return $applications;
    }

    private function filled(array $payload, string $field): bool
    {
        return array_key_exists($field, $payload) and $payload[$field] !== null and ! empty($payload[$field]);
    }
}
