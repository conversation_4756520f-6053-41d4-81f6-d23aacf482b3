<?php

namespace Modules\Core\View\Components;

use Illuminate\View\Component;
use Illuminate\View\View;
use Modules\Core\Models\Settings\AcademicYear as SettingsAcademicYear;

class AcademicYear extends Component
{
    public $academicYearData = [];
    public $selectedAcademicYear = ['name' => 'Current Academic Year', 'id' => 0, 'start_date' => '', 'end_date' => '', 'status' => 'active'];
    /**
     * Create a new component instance.
     */


    public function __construct()
    {
        $this->academicYearData = academicYearData();
         foreach($this->academicYearData as $year) {
            if(@$year['selected']){
                $this->selectedAcademicYear = $year;
            }
        }

    }
    /**
     * Get the view/contents that represent the component.
     */
    public function render(): View|string
    {
        return view('core::components.academicyear');
    }
}
