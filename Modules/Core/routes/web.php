<?php

use App\Http\Middleware\RedirectIfNoPermission;
use Illuminate\Support\Facades\Route;
use Modules\Core\Http\Controllers\Admin\AdminController;
use Modules\Core\Http\Controllers\Admin\AdminManageActivityLogsController;
use Modules\Core\Http\Controllers\Admin\AdminManageEmisNoticesController;
use Modules\Core\Http\Controllers\Admin\AdminManagePublicationsController;
use Modules\Core\Http\Controllers\Admin\AdminManageTicketsController;
use Modules\Core\Http\Controllers\Admin\Approvals\AdminManageEmisUserApplicationsController;
use Modules\Core\Http\Controllers\Admin\Approvals\AdminManageFlaggedInstitutionController;
use Modules\Core\Http\Controllers\Admin\Approvals\AdminRegistrationApprovalsController;
use Modules\Core\Http\Controllers\Admin\Approvals\EmisNumberApplicationController;
use Modules\Core\Http\Controllers\Admin\Approvals\AdminLearnerEnrolmentApplicationsController;
use Modules\Core\Http\Controllers\Admin\Auth\AdminForgotPasswordResetController;
use Modules\Core\Http\Controllers\Admin\Auth\AdminTwoFactorController;
use Modules\Core\Http\Controllers\Admin\Auth\DeveloperModeController;
use Modules\Core\Http\Controllers\Admin\DataUpdate\AdminLearnersDataUpdateController;
use Modules\Core\Http\Controllers\Admin\Hr\AdminManageSupportStaffController;
use Modules\Core\Http\Controllers\Admin\Hr\AdminManageTeachingStaffController;
use Modules\Core\Http\Controllers\Admin\Learners\AdminManageLearnersCertificateController;
use Modules\Core\Http\Controllers\Admin\Learners\AdminManageLearnersDegreeController;
use Modules\Core\Http\Controllers\Admin\Learners\AdminManageLearnersDiplomaController;
use Modules\Core\Http\Controllers\Admin\Learners\AdminManageLearnersInternationalController;
use Modules\Core\Http\Controllers\Admin\Learners\AdminManageLearnersPrePrimaryController;
use Modules\Core\Http\Controllers\Admin\Learners\AdminManageLearnersPrimaryController;
use Modules\Core\Http\Controllers\Admin\Learners\AdminManageLearnersSecondaryController;
use Modules\Core\Http\Controllers\Admin\Learners\AdminManageLearnerTransfersClaimsController;
use Modules\Core\Http\Controllers\Admin\Projects\AdminManageProjectsController;
use Modules\Core\Http\Controllers\Admin\Reports\AdminReportsController;
use Modules\Core\Http\Controllers\Admin\Reports\Facilities\AdminManageInfrastructureController;
use Modules\Core\Http\Controllers\Admin\Reports\Promotions\AdminManageInstitutionsCertificatePromotionsController;
use Modules\Core\Http\Controllers\Admin\Reports\Promotions\AdminManageInstitutionsDegreePromotionsController;
use Modules\Core\Http\Controllers\Admin\Reports\Promotions\AdminManageInstitutionsDiplomaPromotionsController;
use Modules\Core\Http\Controllers\Admin\Reports\Promotions\AdminManageInstitutionsInternationalPromotionsController;
use Modules\Core\Http\Controllers\Admin\Reports\Promotions\AdminManageInstitutionsPrePrimaryPromotionsController;
use Modules\Core\Http\Controllers\Admin\Reports\Promotions\AdminManageInstitutionsPrimaryPromotionsController;
use Modules\Core\Http\Controllers\Admin\Reports\Promotions\AdminManageInstitutionsSecondaryPromotionsController;
use Modules\Core\Http\Controllers\Admin\RolesPermissions\AdminPermissionsController;
use Modules\Core\Http\Controllers\Admin\Schools\AdminManageInstitutionsCertificateController;
use Modules\Core\Http\Controllers\Admin\Schools\AdminManageInstitutionsDegreeController;
use Modules\Core\Http\Controllers\Admin\Schools\AdminManageInstitutionsDiplomaController;
use Modules\Core\Http\Controllers\Admin\Schools\AdminManageInstitutionsInternationalController;
use Modules\Core\Http\Controllers\Admin\Schools\AdminManageInstitutionsPrePrimaryController;
use Modules\Core\Http\Controllers\Admin\Schools\AdminManageInstitutionsPrimaryController;
use Modules\Core\Http\Controllers\Admin\Schools\AdminManageInstitutionsSecondaryController;
use Modules\Core\Http\Controllers\Admin\Schools\Enrolments\AdminManageInstitutionsCertificateEnrolmentsController;
use Modules\Core\Http\Controllers\Admin\Schools\Enrolments\AdminManageInstitutionsDegreeEnrolmentsController;
use Modules\Core\Http\Controllers\Admin\Schools\Enrolments\AdminManageInstitutionsDiplomaEnrolmentsController;
use Modules\Core\Http\Controllers\Admin\Schools\Enrolments\AdminManageInstitutionsInternationalEnrolmentsController;
use Modules\Core\Http\Controllers\Admin\Schools\Enrolments\AdminManageInstitutionsPrePrimaryEnrolmentsController;
use Modules\Core\Http\Controllers\Admin\Schools\Enrolments\AdminManageInstitutionsPrimaryEnrolmentsController;
use Modules\Core\Http\Controllers\Admin\Schools\Enrolments\AdminManageInstitutionsSecondaryEnrolmentsController;
use Modules\Core\Http\Controllers\Admin\Settings\AmendSchoolDetailsReasonsController;
use Modules\Core\Http\Controllers\Admin\Settings\Clusters\AdminManageClustersController;
use Modules\Core\Http\Controllers\Admin\Settings\LearnerFlaggingReasonsController;
use Modules\Core\Http\Controllers\Admin\Settings\LearnerTransferReasonController;
use Modules\Core\Http\Controllers\Admin\Settings\LicensingAndRegistrationRequirementController;
use Modules\Core\Http\Controllers\Admin\Settings\Projects\ProjectSettingsController;
use Modules\Core\Http\Controllers\Admin\Settings\ReligionController;
use Modules\Core\Http\Controllers\Admin\Settings\SchoolSuspensionReasonController;
use Modules\Core\Http\Controllers\Admin\Settings\SchoolTypeRequirementController;
use Modules\Core\Http\Controllers\Admin\Settings\TeacherProfessionalQualificationController;
use Modules\Core\Http\Controllers\Admin\Settings\TeacherResponsibilityController;
use Modules\Core\Http\Controllers\Admin\Settings\Tickets\TicketSettingsController;
use Modules\Core\Http\Controllers\Admin\UserManagerController;
use Modules\Core\Http\Controllers\AdministrationController;
use Modules\Core\Http\Controllers\Api\V1\NIRA\NiraApiController;
use Modules\Core\Http\Controllers\Auth\ForgotPasswordController;
use Modules\Core\Http\Controllers\Auth\LoginController;
use Modules\Core\Http\Controllers\Auth\RegistrationController;
use Modules\Core\Http\Controllers\Auth\ResetPasswordController;
use Modules\Core\Http\Controllers\Auth\TwoFactorController;
use Modules\Core\Http\Controllers\ClusterUser\Approvals\ClusterEmisNumberApplicationController;
use Modules\Core\Http\Controllers\ClusterUser\Approvals\ClusterUserAccountApprovalsController;
use Modules\Core\Http\Controllers\ClusterUser\ClusterRegionalManagerController;
use Modules\Core\Http\Controllers\ClusterUser\ClusterUserController;
use Modules\Core\Http\Controllers\ClusterUser\ClusterUserNoticesController;
use Modules\Core\Http\Controllers\ClusterUser\ClusterUserPublicationsController;
use Modules\Core\Http\Controllers\ClusterUser\DataUpdate\LearnersEsoDataUpdateController;
use Modules\Core\Http\Controllers\ClusterUser\Hr\ClusterManageSupportStaffController;
use Modules\Core\Http\Controllers\ClusterUser\Hr\ClusterManageTeachingStaffController;
use Modules\Core\Http\Controllers\ClusterUser\Learners\ClusterLearnersController;
use Modules\Core\Http\Controllers\ClusterUser\Learners\ClusterLearnersFilterController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\ClusterManageInstitutionsCertificateController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\ClusterManageInstitutionsDegreeController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\ClusterManageInstitutionsDiplomaController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\ClusterManageInstitutionsInternationalController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\ClusterManageInstitutionsPrePrimaryController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\ClusterManageInstitutionsPrimaryController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\ClusterManageInstitutionsSecondaryController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\Enrolments\ClusterManageInstitutionsCertificateEnrolmentsController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\Enrolments\ClusterManageInstitutionsDegreeEnrolmentsController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\Enrolments\ClusterManageInstitutionsDiplomaEnrolmentsController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\Enrolments\ClusterManageInstitutionsInternationalEnrolmentsController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\Enrolments\ClusterManageInstitutionsPrePrimaryEnrolmentsController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\Enrolments\ClusterManageInstitutionsPrimaryEnrolmentsController;
use Modules\Core\Http\Controllers\ClusterUser\Schools\Enrolments\ClusterManageInstitutionsSecondaryEnrolmentsController;
use Modules\Core\Http\Controllers\CoreController;
use Modules\Core\Http\Controllers\District\Approvals\DistrictLearnerEnrolmentApplicationsController;
use Modules\Core\Http\Controllers\District\Approvals\DistrictRegistrationApprovalsCertificateController;
use Modules\Core\Http\Controllers\District\Approvals\DistrictRegistrationApprovalsDegreeController;
use Modules\Core\Http\Controllers\District\Approvals\DistrictRegistrationApprovalsDiplomaController;
use Modules\Core\Http\Controllers\District\Approvals\DistrictRegistrationApprovalsInternationalController;
use Modules\Core\Http\Controllers\District\Approvals\DistrictRegistrationApprovalsPrePrimaryController;
use Modules\Core\Http\Controllers\District\Approvals\DistrictRegistrationApprovalsPrimaryController;
use Modules\Core\Http\Controllers\District\Approvals\DistrictRegistrationApprovalsSecondaryController;
use Modules\Core\Http\Controllers\District\Approvals\DistrictUserAccountApprovalsController;
use Modules\Core\Http\Controllers\District\DistrictNoticesController;
use Modules\Core\Http\Controllers\District\DistrictPublicationsController;
use Modules\Core\Http\Controllers\District\DistrictUserController;
use Modules\Core\Http\Controllers\District\Hr\DistrictManageSupportStaffController;
use Modules\Core\Http\Controllers\District\Hr\DistrictManageTeachingStaffController;
use Modules\Core\Http\Controllers\District\Learners\DistrictLearnersController;
use Modules\Core\Http\Controllers\District\Learners\DistrictLearnersFilterController;
use Modules\Core\Http\Controllers\District\Schools\DistrictManageInstitutionsCertificateController;
use Modules\Core\Http\Controllers\District\Schools\DistrictManageInstitutionsDegreeController;
use Modules\Core\Http\Controllers\District\Schools\DistrictManageInstitutionsDiplomaController;
use Modules\Core\Http\Controllers\District\Schools\DistrictManageInstitutionsInternationalController;
use Modules\Core\Http\Controllers\District\Schools\DistrictManageInstitutionsPrePrimaryController;
use Modules\Core\Http\Controllers\District\Schools\DistrictManageInstitutionsPrimaryController;
use Modules\Core\Http\Controllers\District\Schools\DistrictManageInstitutionsSecondaryController;
use Modules\Core\Http\Controllers\District\Schools\Enrolments\DistrictManageInstitutionsCertificateEnrolmentsController;
use Modules\Core\Http\Controllers\District\Schools\Enrolments\DistrictManageInstitutionsDegreeEnrolmentsController;
use Modules\Core\Http\Controllers\District\Schools\Enrolments\DistrictManageInstitutionsDiplomaEnrolmentsController;
use Modules\Core\Http\Controllers\District\Schools\Enrolments\DistrictManageInstitutionsInternationalEnrolmentsController;
use Modules\Core\Http\Controllers\District\Schools\Enrolments\DistrictManageInstitutionsPrePrimaryEnrolmentsController;
use Modules\Core\Http\Controllers\District\Schools\Enrolments\DistrictManageInstitutionsPrimaryEnrolmentsController;
use Modules\Core\Http\Controllers\District\Schools\Enrolments\DistrictManageInstitutionsSecondaryEnrolmentsController;
use Modules\Core\Http\Controllers\DownloadCentreController;
use Modules\Core\Http\Controllers\EmisSearchController;
use Modules\Core\Http\Controllers\Institutions\Approvals\LearnerEnrolmentApplicationsController;
use Modules\Core\Http\Controllers\Institutions\EmisReturnController;
use Modules\Core\Http\Controllers\Institutions\Facilities\InstitutionsFacilityController;
use Modules\Core\Http\Controllers\Institutions\Finance\SchoolBudgetController;
use Modules\Core\Http\Controllers\Institutions\Finance\SchoolExpensesController;
use Modules\Core\Http\Controllers\Institutions\Finance\SchoolIncomeController;
use Modules\Core\Http\Controllers\Institutions\Infrastructure\InstitutionInfrastructureController;
use Modules\Core\Http\Controllers\Institutions\InstitutionController;
use Modules\Core\Http\Controllers\Institutions\InstructionalMaterials\InstitutionInstructionalMaterialsController;
use Modules\Core\Http\Controllers\Institutions\InstructionalMaterials\InstitutionLabEquipmentChemicalsController;
use Modules\Core\Http\Controllers\Institutions\Learners\CandidatesController;
use Modules\Core\Http\Controllers\Institutions\Learners\LearnerController;
use Modules\Core\Http\Controllers\Institutions\Learners\LearnersDataUpdateController;
use Modules\Core\Http\Controllers\Institutions\Notices\InstitutionEmisNoticesController;
use Modules\Core\Http\Controllers\Institutions\Parents\ParentsController;
use Modules\Core\Http\Controllers\Institutions\PeSports\InstitutionPhysicalEducationSportsController;
use Modules\Core\Http\Controllers\Institutions\Publications\InstitutionPublicationsController;
use Modules\Core\Http\Controllers\Institutions\Staff\HrController;
use Modules\Core\Http\Controllers\Institutions\SurveyController;
use Modules\Core\Http\Controllers\Institutions\Tickets\InstitutionTicketsController;
use Modules\Core\Http\Controllers\Institutions\UserManager\InstitutionUserController;
use Modules\Core\Http\Controllers\LandingPageController;
use Modules\Core\Http\Controllers\LicenceAndRegistration\InstitutionLicenceController;
use Modules\Core\Http\Controllers\SubCounty\Approvals\SubCountyRegistrationApprovalsCertificateController;
use Modules\Core\Http\Controllers\SubCounty\Approvals\SubCountyRegistrationApprovalsDegreeController;
use Modules\Core\Http\Controllers\SubCounty\Approvals\SubCountyRegistrationApprovalsDiplomaController;
use Modules\Core\Http\Controllers\SubCounty\Approvals\SubCountyRegistrationApprovalsInternationalController;
use Modules\Core\Http\Controllers\SubCounty\Approvals\SubCountyRegistrationApprovalsPrePrimaryController;
use Modules\Core\Http\Controllers\SubCounty\Approvals\SubCountyRegistrationApprovalsPrimaryController;
use Modules\Core\Http\Controllers\SubCounty\Approvals\SubCountyRegistrationApprovalsSecondaryController;
use Modules\Core\Http\Controllers\SubCounty\Approvals\SubCountyUserAccountApprovalsController;
use Modules\Core\Http\Controllers\SubCounty\Hr\SubCountyManageSupportStaffController;
use Modules\Core\Http\Controllers\SubCounty\Hr\SubCountyManageTeachingStaffController;
use Modules\Core\Http\Controllers\SubCounty\Learners\SubCountyLearnersController;
use Modules\Core\Http\Controllers\SubCounty\Learners\SubCountyLearnersFilterController;
use Modules\Core\Http\Controllers\SubCounty\Schools\Enrolments\SubCountyManageInstitutionsCertificateEnrolmentsController;
use Modules\Core\Http\Controllers\SubCounty\Schools\Enrolments\SubCountyManageInstitutionsDegreeEnrolmentsController;
use Modules\Core\Http\Controllers\SubCounty\Schools\Enrolments\SubCountyManageInstitutionsDiplomaEnrolmentsController;
use Modules\Core\Http\Controllers\SubCounty\Schools\Enrolments\SubCountyManageInstitutionsInternationalEnrolmentsController;
use Modules\Core\Http\Controllers\SubCounty\Schools\Enrolments\SubCountyManageInstitutionsPrePrimaryEnrolmentsController;
use Modules\Core\Http\Controllers\SubCounty\Schools\Enrolments\SubCountyManageInstitutionsPrimaryEnrolmentsController;
use Modules\Core\Http\Controllers\SubCounty\Schools\Enrolments\SubCountyManageInstitutionsSecondaryEnrolmentsController;
use Modules\Core\Http\Controllers\SubCounty\Schools\SubCountyManageInstitutionsCertificateController;
use Modules\Core\Http\Controllers\SubCounty\Schools\SubCountyManageInstitutionsDegreeController;
use Modules\Core\Http\Controllers\SubCounty\Schools\SubCountyManageInstitutionsDiplomaController;
use Modules\Core\Http\Controllers\SubCounty\Schools\SubCountyManageInstitutionsInternationalController;
use Modules\Core\Http\Controllers\SubCounty\Schools\SubCountyManageInstitutionsPrePrimaryController;
use Modules\Core\Http\Controllers\SubCounty\Schools\SubCountyManageInstitutionsPrimaryController;
use Modules\Core\Http\Controllers\SubCounty\Schools\SubCountyManageInstitutionsSecondaryController;
use Modules\Core\Http\Controllers\SubCounty\SubCountyNoticesController;
use Modules\Core\Http\Controllers\SubCounty\SubCountyPublicationsController;
use Modules\Core\Http\Controllers\SubCounty\SubCountyUserController;
use Modules\Core\Http\Controllers\Admin\Hr\NonTeachingStaffPostingController as AdminNonTeachingStaffPostingController;
use Modules\Core\Http\Controllers\Admin\Hr\NonTeachingStaffTransferController as AdminNonTeachingStaffTransferController;
use Modules\Core\Http\Controllers\Admin\Hr\TeacherPostingController as AdminTeacherPostingController;
use Modules\Core\Http\Controllers\Admin\Hr\TeacherTransferController as AdminTeacherTransferController;
use Modules\Core\Http\Controllers\Institutions\Staff\NonTeachingStaffPostingController as SchoolNonTeachingStaffPostingController;
use Modules\Core\Http\Controllers\Institutions\Staff\NonTeachingStaffTransferController as SchoolNonTeachingStaffTransferController;
use Modules\Core\Http\Controllers\Institutions\Staff\TeacherPostingController as SchoolTeacherPostingController;
use Modules\Core\Http\Controllers\Institutions\Staff\TeacherTransferController as SchoolTeacherTransferController;
use Modules\Core\Http\Controllers\District\Hr\NonTeachingStaffPostingController as DistrictNonTeachingStaffPostingController;
use Modules\Core\Http\Controllers\District\Hr\NonTeachingStaffTransferController as DistrictNonTeachingStaffTransferController;
use Modules\Core\Http\Controllers\District\Hr\TeacherPostingController as DistrictTeacherPostingController;
use Modules\Core\Http\Controllers\District\Hr\TeacherTransferController as DistrictTeacherTransferController;
use Modules\Core\Http\Controllers\District\Approvals\EmisNumberApplicationController as DistrictEmisNumberApplicationController;
use Modules\Core\Http\Controllers\Admin\Approvals\EmisNumberApplicationController as AdminEmisNumberApplicationController;
use Modules\Core\Http\Controllers\SubCounty\Approvals\EmisNumberApplicationController as SubCountyEmisNumberApplicationController;
use Modules\Core\Http\Controllers\AcademicYearController;
use Modules\Core\Http\Controllers\Admin\SystemSettingsController;
use Modules\HumanResource\Livewire\{TeachingStaffAttendanceComponent, TeachingStaffAttendanceRegistryComponent};

Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('cores', CoreController::class)->names('core');
});

//Landing page
Route::get('/', [LandingPageController::class, 'landingPage'])->name('landing-page');
Route::get('terms-and-conditions', [LandingPageController::class, 'termsAndConditions'])->name('terms-and-conditions');
Route::get('privacy-policy', [LandingPageController::class, 'termsAndConditions'])->name('privacy-policy');
Route::get('download-learner-EMIS-registration-form', [LandingPageController::class, 'downloadLearnerEmisRegForm'])->name('download-reg-form');
Route::get('download-refugee-id-EMIS-registration-form', [LandingPageController::class, 'downloadRefugeIdEmisRegForm'])->name('download-reg-form-refugee-id');
Route::get('download-international-EMIS-registration-form', [LandingPageController::class, 'downloadInternationalEmisRegForm'])->name('download-reg-form-international');
Route::get('download-EMIS-Staff-registration-form', [LandingPageController::class, 'downloadEmisStaffRegForm'])->name('download-reg-form-teachers');

//User Manuals
Route::get('download-EMIS-user-manual-pre-primary', [LandingPageController::class, 'downloadPrePrimaryUserManual'])->name('download-emis-user-manual-pre-primary');
Route::get('download-EMIS-user-manual-primary', [LandingPageController::class, 'downloadPrimaryUserManual'])->name('download-emis-user-manual-primary');
Route::get('download-EMIS-user-manual-secondary', [LandingPageController::class, 'downloadSecondaryUserManual'])->name('download-emis-user-manual-secondary');
Route::get('download-EMIS-user-manual-certificate', [LandingPageController::class, 'downloadCertificateUserManual'])->name('download-emis-user-manual-certificate');
Route::get('download-EMIS-user-manual-diploma', [LandingPageController::class, 'downloadDiplomaUserManual'])->name('download-emis-user-manual-diploma');
Route::get('download-EMIS-user-manual-international', [LandingPageController::class, 'downloadInternationalUserManual'])->name('download-emis-user-manual-international');

Route::get('school-licence-print/{id}', [InstitutionLicenceController::class, 'public_school_licence_view']);
Route::get('institution-licence-status-update', [InstitutionLicenceController::class, 'check_licence_status']);

//

// Search Emis
Route::any('/emis/search', [EmisSearchController::class, 'index'])->name('emis-search');
Route::any('/emis-number-status/{emisNumber}', [EmisSearchController::class, 'status'])->name('emis-number-status');

Route::prefix('institution')->group(function () {
    //Auth
    Route::get('login', [LoginController::class, 'showLoginForm'])->name('institution-login');
    Route::get('register', [LoginController::class, 'showRegisterForm'])->name('institution-register');
    Route::post('login', [LoginController::class, 'login'])->name('institution-login-user');
    Route::get('maintenance', [LoginController::class, 'maintenance'])->name('maintenance-page');

    Route::get('register-with-emis-number', [RegistrationController::class, 'emisRegistration'])->name('institution-emis-registration');
    Route::get('register-without-emis-number', [RegistrationController::class, 'formRegistration'])->name('institution-form-registration');
    Route::get('registration-complete', [RegistrationController::class, 'registrationComplete'])->name('institution-registration-complete');
    Route::post('verify-email-code', [RegistrationController::class, 'emailVerification']);
    Route::post('application-receipt-acknowledgement', [RegistrationController::class, 'sendApplicationAcknowledgement']);
    Route::get('login-otp-verify/resend', [TwoFactorController::class, 'resend'])->name('institution.verify.resend');
    Route::resource('login-otp-verify', TwoFactorController::class)->only(['index', 'store']);

    //Password reset
    Route::get('user-forgot-password', [ForgotPasswordController::class, 'forgotPassword'])->name('institution-forgot-password-fix');
    Route::get('forgot-password/{token}', [ForgotPasswordController::class, 'forgotPasswordValidate']);
    Route::post('forgot-password-reset', [ResetPasswordController::class, 'resetPassword'])->name('institution-forgot-password-reset');
    Route::put('reset-password', [ResetPasswordController::class, 'updatePassword'])->name('institution-reset-password');

    Route::middleware(['auth', 'school'])->group(function () {

        Route::match(['post', 'get'], 'logout', [LoginController::class, 'logout'])->name('institution-logout-user');

        Route::middleware(['schoolTwoFactor'])->group(function () {

            Route::get('licence-and-registration/r', [InstitutionLicenceController::class,'registration'])->name('institution.registration');
            Route::get('licence-and-registration/existing', [InstitutionLicenceController::class,'existing_licence_form'])->name('institution.existing-form');
            Route::get('licence-and-registration/applications', [InstitutionLicenceController::class,'pending_licence_applications'])->name('institute_applications');
            Route::get('licence-and-registration/applications/{id}', [InstitutionLicenceController::class,'licence_application_details'])->name('institute_applications_details');
            Route::resource('licence-and-registration', InstitutionLicenceController::class,['as'=>'institution.licence-and-registration']);

            // Learner Enrolment Applications
            Route::prefix('learner-pre-registration-applications')->name('learner-enrolment-applications.')->group(function () {
                Route::get('draft', [LearnerEnrolmentApplicationsController::class, 'draft'])->name('draft');
                Route::get('pending', [LearnerEnrolmentApplicationsController::class, 'pending'])->name('pending');
                Route::get('approved', [LearnerEnrolmentApplicationsController::class, 'approved'])->name('approved');
                Route::get('rejected', [LearnerEnrolmentApplicationsController::class, 'rejected'])->name('rejected');
                Route::get('{status}/{referenceNumber}', [LearnerEnrolmentApplicationsController::class, 'show'])->name('show');
            });

            Route::get('survey', [SurveyController::class, 'survey'])->name('institution-survey');

            Route::middleware(['pendingSurveys'])->group(function () {
                Route::get('dashboard', [InstitutionController::class, 'dashboard'])->name('institution-dashboard');
                Route::get('profile', [InstitutionController::class, 'profile'])->name('institution-profile');

                // Learners
                Route::prefix('learners')->group(function () {
                    Route::get('overview', [LearnerController::class, 'overview'])->name('institution-learners-overview');
                    Route::get('/all', [LearnerController::class, 'index'])->name('institution-learners-index');
                    Route::get('expected-enrolment', [LearnerController::class, 'expected_enrolment'])->name('learner-expected-enrolment');
                    Route::get('promotions', [LearnerController::class, 'promotions'])->name('institution-learners-promotions');
                    Route::get('enrolments', [LearnerController::class, 'enrolments'])->name('institution-learners-enrolments');
                    Route::get('transitions', [LearnerController::class, 'transitions'])->name('institution-learners-transitions');
                    Route::get('transfers', [LearnerController::class, 'transfers'])->name('institution-learners-transfers');
                    Route::get('claims', [LearnerController::class, 'claims'])->name('institution-learners-claims');
                    Route::get('claim-form', [LearnerController::class, 'form_learner_claim'])->name('institution-learners-claim-form');
                    Route::get('profile/{lin}', [LearnerController::class, 'profile'])->name('learner-profile');
//                    Route::get('/redirect-learner/{id}', [LearnerController::class, 'redirectToProfile'])->name('redirect-learner');
                    Route::get('bulk-image-upload', [LearnerController::class, 'bulk_image_upload'])->name('institution-learners-image-upload');
                    Route::post('bulk-image-upload', [LearnerController::class, 'upload_bulk_images'])->name('upload-bulk-images');
                    Route::get('search-lin', [LearnerController::class, 'searchLin'])->name('institution-learner-search-lin');

                    //International schools
                    //                    Route::get('promotions-international-school', [LearnerController::class, 'promotionsInternationalSchool'])->name('institution-learners-promotions-international-school');
                });

                //Parents
                Route::get('manage', [ParentsController::class, 'getParents'])->name('institution-fetch-parents');

                // Human Resource
                Route::prefix('hr')->group(function () {
                    Route::get('all-teachers', [HrController::class, 'allTeachers'])->name('institution-hr-all-teachers');
                    Route::get('teacher/{hrin}', [HrController::class, 'teacherProfile'])->name('institution-hr-teacher-profile');
                    Route::get('non-teaching-staff', [HrController::class, 'nonTeachingStaff'])->name('institution-hr-non-teaching-staff');
                    Route::get('non-teaching-staff-profile/{hrin}', [HrController::class, 'nonTeachingStaffProfile'])->name('institution-hr-non-teaching-staff-profile');
                    Route::prefix('postings')->group(function () {
                        Route::get('teachers', [SchoolTeacherPostingController::class, 'index'])->name('institution-hr-postings-teachers');
                        Route::get('non-teaching-staff', [SchoolNonTeachingStaffPostingController::class, 'index'])->name('institution-hr-postings-non-teaching-staff');
                    });
                    Route::prefix('transfers')->group(function () {
                        Route::get('teachers', [SchoolTeacherTransferController::class, 'index'])->name('institution-hr-transfers-teachers');
                        Route::get('non-teaching-staff', [SchoolNonTeachingStaffTransferController::class, 'index'])->name('institution-hr-transfers-non-teaching-staff');
                    });
                    Route::get('/teaching-staff-attendance', TeachingStaffAttendanceComponent::class)->name('teaching-staff-attendance');
                    Route::get('/attendance-registry', TeachingStaffAttendanceRegistryComponent::class)->name('attendance-registry');
                });

                // Infrastructure
                Route::prefix('infrastructure')->group(function () {
                    Route::get('/', [InstitutionInfrastructureController::class, 'index'])->name('institution-infrastructure');
                    Route::get('sanitation', [InstitutionInfrastructureController::class, 'sanitation'])->name('institution-sanitation');
                    Route::get('classrooms', [InstitutionInfrastructureController::class, 'classrooms'])->name('institution-classrooms');
                    Route::get('classrooms-with-ramps-for-sne', [InstitutionInfrastructureController::class, 'classroomsWithRampsForSne'])->name('institution-classrooms-with-ramps-for-sne');
                    Route::get('kitchen', [InstitutionInfrastructureController::class, 'kitchen'])->name('institution-kitchen');
                    Route::get('teacher-houses', [InstitutionInfrastructureController::class, 'teacherHouses'])->name('institution-teacher-houses');
                    Route::get('latrine-stances', [InstitutionInfrastructureController::class, 'latrineStances'])->name('institution-latrine-stances');
                    Route::get('library-book-store', [InstitutionInfrastructureController::class, 'libraryBookStore'])->name('institution-library-book-store');
                    Route::get('administration-block-office', [InstitutionInfrastructureController::class, 'administrationBlockOffice'])->name('institution-administration-block-office');
                    Route::get('staff-rooms', [InstitutionInfrastructureController::class, 'staffRooms'])->name('institution-staff-rooms');
                    Route::get('resting-rooms', [InstitutionInfrastructureController::class, 'restingRooms'])->name('institution-resting-rooms');
                    Route::get('computer-lab', [InstitutionInfrastructureController::class, 'computerLab'])->name('institution-computer-lab');
                    Route::get('sick-bay', [InstitutionInfrastructureController::class, 'sickBay'])->name('institution-sick-bay');
                    Route::get('store-rooms', [InstitutionInfrastructureController::class, 'storeRooms'])->name('institution-store-rooms');
                    Route::get('changing-rooms', [InstitutionInfrastructureController::class, 'changingRooms'])->name('institution-changing-rooms');
                    Route::get('urinals', [InstitutionInfrastructureController::class, 'urinals'])->name('institution-urinals');
                    Route::get('main-hall', [InstitutionInfrastructureController::class, 'mainHall'])->name('institution-main-hall');
                    Route::get('counseling-rooms', [InstitutionInfrastructureController::class, 'counseling_rooms'])->name('institution-counseling-rooms');
                    Route::get('water-bourne-toilets', [InstitutionInfrastructureController::class, 'water_bourne_toilets'])->name('institution-water-bourne-toilets');
                    Route::get('incinerators', [InstitutionInfrastructureController::class, 'incinerators'])->name('institution-incinerators');
                });

                // Facilities
                Route::prefix('facilities')->group(function () {
                    Route::get('source-and-access-to-water', [InstitutionsFacilityController::class, 'sourceOfWater'])->name('institution-facilities-water-source');
                    Route::get('hand-washing-facilities', [InstitutionsFacilityController::class, 'handWashingFacilities'])->name('institution-facilities-hand-washing-facilities');
                    Route::get('garbage-final-disposal', [InstitutionsFacilityController::class, 'garbageFinalDisposal'])->name('institution-facilities-garbage-final-disposal');
                    Route::get('source-of-energy', [InstitutionsFacilityController::class, 'sourceOfEnergy'])->name('institution-facilities-source-of-energy');
                    Route::get('ict-facilities', [InstitutionsFacilityController::class, 'ictFacilities'])->name('institution-facilities-ict-facilities');
                    Route::get('other-facilities', [InstitutionsFacilityController::class, 'otherFacilities'])->name('institution-facilities-other-facilities');
                });

                // PE & Sports
                Route::prefix('physical-education-and-sports')->group(function () {
                    Route::get('activities', [InstitutionPhysicalEducationSportsController::class, 'sportsActivities'])->name('institution-pe-sports-activities');
                    Route::get('facilities', [InstitutionPhysicalEducationSportsController::class, 'sportsFacilities'])->name('institution-pe-sports-facilities');
                    Route::post('facilities-update/{id}', [InstitutionPhysicalEducationSportsController::class, 'updateSportsFacilities'])->name('institution-pe-sports-facilities-update');
                    Route::get('equipment', [InstitutionPhysicalEducationSportsController::class, 'sportsEquipment'])->name('institution-pe-sports-equipment');
                    Route::post('equipment-update/{id}', [InstitutionPhysicalEducationSportsController::class, 'updateSportsEquipment'])->name('institution-pe-sports-equipment-update');

                    Route::get('extra-curricular-activities', [InstitutionPhysicalEducationSportsController::class, 'extraCurricularActivities'])->name('institution-pe-sports-extra-curricular-activities');
                    Route::get('practical-skills', [InstitutionPhysicalEducationSportsController::class, 'practicalSkills'])->name('institution-pe-practical-skills');
                });

                //Instructional Materials
                Route::prefix('instructional-materials')->group(function () {
                    Route::get('learning-and-playing-materials', [InstitutionInstructionalMaterialsController::class, 'learningAndPlayingMaterials'])->name('institution-learning-and-playing-materials');
                    Route::get('textbooks', [InstitutionInstructionalMaterialsController::class, 'textbooks'])->name('institution-textbooks-manage');
                    Route::get('filter-textbooks', [InstitutionInstructionalMaterialsController::class, 'filterTextbooks'])->name('institution-filter-textbooks');
                    Route::post('textbooks-store', [InstitutionInstructionalMaterialsController::class, 'storeTextbooks'])->name('institution-textbook-store');
                    Route::post('textbooks-edit/{id}', [InstitutionInstructionalMaterialsController::class, 'editTextbooks'])->name('institution-textbook-edit');
                    //Reference Books
                    Route::get('reference-books', [InstitutionInstructionalMaterialsController::class, 'referenceBook'])->name('institution-reference-books-manage');
                    Route::get('filter-reference-books', [InstitutionInstructionalMaterialsController::class, 'filterReferenceBook'])->name('institution-filter-reference-books');
                    Route::post('reference-books-store', [InstitutionInstructionalMaterialsController::class, 'storeReferenceBook'])->name('institution-reference-book-store');
                    Route::post('reference-books-edit/{id}', [InstitutionInstructionalMaterialsController::class, 'editReferenceBook'])->name('institution-reference-book-edit');
                    //SNE kITS
                    Route::get('sne-kits', [InstitutionInstructionalMaterialsController::class, 'sneKits'])->name('institution-sne-kits-manage');
                    Route::get('filter-sne-kits', [InstitutionInstructionalMaterialsController::class, 'filterSneKits'])->name('institution-filter-sne-kits');
                    Route::post('sne-kits-store', [InstitutionInstructionalMaterialsController::class, 'storeSneKits'])->name('institution-sne-kit-store');
                    Route::post('sne-kits-edit/{id}', [InstitutionInstructionalMaterialsController::class, 'editSneKits'])->name('institution-sne-kit-edit');
                    // Lab Equipment Chemicals
                    Route::get('lab-equipment', [InstitutionLabEquipmentChemicalsController::class, 'labEquipmentChemicals'])->name('institution-lab-equipment');
                    Route::get('filter-lab-equipment', [InstitutionLabEquipmentChemicalsController::class, 'filterLabEquipmentChemicals'])->name('institution-filter-lab-equipment');
                    Route::post('lab-equipment-store', [InstitutionLabEquipmentChemicalsController::class, 'storeLabEquipmentChemicals'])->name('institution-lab-equipment-store');
                    Route::post('lab-equipment-edit/{id}', [InstitutionLabEquipmentChemicalsController::class, 'editLabEquipmentChemicals'])->name('institution-lab-equipment-edit');
                    // Reagents
                    Route::get('reagents', [InstitutionLabEquipmentChemicalsController::class, 'reagents'])->name('institution-reagents-manage');
                    Route::get('filter-reagents', [InstitutionLabEquipmentChemicalsController::class, 'filterReagents'])->name('institution-filter-reagents');
                    Route::post('reagents-store', [InstitutionLabEquipmentChemicalsController::class, 'storeReagents'])->name('institution-reagents-store');
                    Route::post('reagents-edit/{id}', [InstitutionLabEquipmentChemicalsController::class, 'editReagents'])->name('institution-reagents-edit');
                });

                Route::prefix('finance')->group(function () {
                    Route::get('income', [SchoolIncomeController::class, 'index'])->name('institution-income');
                    Route::get('expenses', [SchoolExpensesController::class, 'index'])->name('institution-expenses');
                    Route::get('budgets', [SchoolBudgetController::class, 'index'])->name('institution-budgets');
                });

                //Learners Data update
                Route::prefix('data-update')->group(function () {
                    Route::get('learners-index-numbers', [LearnersDataUpdateController::class, 'learnersIndexNumbers'])->name('institution-learners-index-numbers');
                    Route::get('candidates-index-numbers', [CandidatesController::class, 'candidatesIndexNumbers'])->name('institution-candidates-index-numbers');
                    Route::get('flagged-learners-for-deleting', [LearnersDataUpdateController::class, 'flaggedLearnersForDeleting'])->name('institution-learners-for-deleting');
                    Route::get('flagged-learner-view-application/{applicationId}', [LearnersDataUpdateController::class, 'viewApplicationDetails']);
                });

                // Publications
                Route::prefix('publications')->group(function () {
                    Route::get('/manage', [InstitutionPublicationsController::class, 'managePublications'])->name('institution-publications-index');
                    Route::get('/download-file/{id}', [InstitutionPublicationsController::class, 'downloadFile'])->name('institution-download-pdf-file');
                    Route::get('emis-calendar', [InstitutionPublicationsController::class, 'emisCalendar'])->name('institution-publications-emis-calendar');
                });

                //Notices
                Route::prefix('emis-notices')->group(function () {
                    Route::get('manage', [InstitutionEmisNoticesController::class, 'manageNotices'])->name('institution-notices.manage');
                    Route::get('filter', [InstitutionEmisNoticesController::class, 'filterNotices'])->name('institution-notices.filter');
                    Route::get('view/{id}', [InstitutionEmisNoticesController::class, 'viewSingleNotice'])->name('institution-notices.view-single');
                    Route::get('download-pdf/{id}', [InstitutionEmisNoticesController::class, 'downloadPdf'])->name('institution-notice.download-pdf');
                });

                // Tickets
                Route::prefix('tickets')->group(function () {
                    Route::get('/create', [InstitutionTicketsController::class, 'create'])->name('institution-tickets-create');
                    Route::post('/store', [InstitutionTicketsController::class, 'store'])->name('institution-tickets-store');
                    Route::get('/single/{slug}/{ticket_id}', [InstitutionTicketsController::class, 'singleTicket'])->name('institution-tickets-single');
                    Route::post('/save-ticket-comment/{id}', [InstitutionTicketsController::class, 'saveTicketComment'])->name('institution-ticket.save-comment');
                    //Route::post('/save-ticket-reply/{comment_id}', [InstitutionTicketsController::class, 'saveTicketReply'])->name('institution-ticket.save-reply');
                    Route::get('/new', [InstitutionTicketsController::class, 'new'])->name('institution-tickets-new');
                    Route::get('/all', [InstitutionTicketsController::class, 'all'])->name('institution-tickets-all');
                    Route::get('/closed', [InstitutionTicketsController::class, 'closed'])->name('institution-tickets-closed');
                });

                // EMIS Returns
                Route::prefix('emis-returns')->group(function () {
                    Route::get('/', [EmisReturnController::class, 'index'])->name('institution-emis-returns');
                    Route::get('details/{survey_id}', [EmisReturnController::class, 'show'])->name('institution-emis-return');
                });

                //Uploaded excels for institution
                Route::get('uploaded-excels', [InstitutionController::class, 'uploadedExcels'])->name('institution-uploaded-excels');

                Route::get('file-uploads', [EmisReturnController::class, 'fileUploads'])->name('institution-emis-return-file-uploads');
                Route::get('file-uploads/details/{file_upload_id}', [EmisReturnController::class, 'fileUploadDetails'])->name('institution-emis-return-file-upload-details');
                // Institution Users
                Route::prefix('user-manager')->group(function () {
                    Route::get('/', [InstitutionUserController::class, 'index'])->name('institution-users');
                });
                Route::get('download-centre', [DownloadCentreController::class, 'index'])->name('download-centre');
            });
        });
    });
});

Route::middleware(['auth'])->group(function () {
    Route::get('admin/logout', [LoginController::class, 'logoutUser'])->name('admin-sign-out');
    Route::get('/licence-and-registration/print/{id}',[InstitutionLicenceController::class,'exportLicenceApplicationData']);
});
// District User
//Route::middleware(['upperLocalGovernment','adminTwoFactor'])->prefix('district')->group(function () {
Route::middleware(['upperLocalGovernment', 'adminTwoFactor'])->prefix('district')->group(function () {
    Route::get('dashboard', [DistrictUserController::class, 'dashboard'])->name('district-dashboard');
    Route::get('profile', [DistrictUserController::class, 'profile'])->name('district-user-profile');

    Route::get('d-licence-applications-list',[InstitutionLicenceController::class,'pending_licence_applications']);

    Route::get('d-licence-applications-list/letter-of-intent',[InstitutionLicenceController::class,'pending_letter_of_intent_applications']);
    Route::get('d-licence-applications-list/letter-of-intent/{id}',[InstitutionLicenceController::class,'letter_of_intent_details']);

    Route::get('d-licence-and-registration/{id}',[InstitutionLicenceController::class,'licence_application_details']);
    Route::resource('d-licence-and-registration', InstitutionLicenceController::class, ['as'=>'district']);

    Route::post('licence-applications',[InstitutionLicenceController::class,'store'])->name('district.licence-and-registration');


    Route::get('licence-applications-existing',[InstitutionLicenceController::class,'existing_licence_applications']);
    Route::get('licence-applications-existing/{id}',[InstitutionLicenceController::class,'existing_licence_details']);

    // User account approvals
    Route::prefix('user-account-approval')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view'])->group(function () {
        //Pre-primary
        Route::get('pre-primary-school', [DistrictUserAccountApprovalsController::class, 'prePrimaryUserAccountApproval'])->name('district.user-account-approvals.pre-primary-school');
        Route::get('pre-primary-school/{approval_id}/approve-confirmation', [DistrictUserAccountApprovalsController::class, 'approvePrePrimarySchoolUserAccountConfirmation'])->name('district.user-account-approvals.pre-primary-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('pre-primary-school/{approval_id}/approve', [DistrictRegistrationApprovalsPrePrimaryController::class, 'approvePrePrimarySchoolRegistration'])->name('district.user-account-approvals.pre-primary-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('pre-primary-school/{approval_id}/reject-page', [DistrictRegistrationApprovalsPrePrimaryController::class, 'rejectPrePrimarySchoolRegPage'])->name('district.user-account-approvals.pre-primary-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('pre-primary-school/{approval_id}/reject', [DistrictRegistrationApprovalsPrePrimaryController::class, 'rejectPrePrimarySchoolRegistration'])->name('district.user-account-approvals.pre-primary-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Primary
        Route::get('primary-school', [DistrictUserAccountApprovalsController::class, 'primaryUserAccountApproval'])->name('district.user-account-approvals.primary-school');
        Route::get('primary-school/{approval_id}/approve-confirmation', [DistrictUserAccountApprovalsController::class, 'approvePrimarySchoolUserAccountConfirmation'])->name('district.user-account-approvals.primary-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('primary-school/{approval_id}/approve', [DistrictRegistrationApprovalsPrimaryController::class, 'approvePrimarySchoolRegistration'])->name('district.user-account-approvals.primary-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('primary-school/{approval_id}/reject-page', [DistrictRegistrationApprovalsPrimaryController::class, 'rejectPrimarySchoolRegPage'])->name('district.user-account-approvals.primary-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('primary-school/{approval_id}/reject', [DistrictRegistrationApprovalsPrimaryController::class, 'rejectPrimarySchoolRegistration'])->name('district.user-account-approvals.primary-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Secondary
        Route::get('secondary-school', [DistrictUserAccountApprovalsController::class, 'secondaryUserAccountApproval'])->name('district.user-account-approvals.secondary-school');
        Route::get('secondary-school/{approval_id}/approve-confirmation', [DistrictUserAccountApprovalsController::class, 'approveSecondarySchoolUserAccountConfirmation'])->name('district.user-account-approvals.secondary-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('secondary-school/{approval_id}/approve', [DistrictRegistrationApprovalsSecondaryController::class, 'approveSecondarySchoolRegistration'])->name('district.user-account-approvals.secondary-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('secondary-school/{approval_id}/reject-page', [DistrictRegistrationApprovalsSecondaryController::class, 'rejectSecondarySchoolRegPage'])->name('district.user-account-approvals.secondary-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('secondary-school/{approval_id}/reject', [DistrictRegistrationApprovalsSecondaryController::class, 'rejectSecondarySchoolRegistration'])->name('district.user-account-approvals.secondary-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Certificate
        Route::get('certificate-awarding-school', [DistrictUserAccountApprovalsController::class, 'certificateUserAccountApproval'])->name('district.user-account-approvals.certificate-awarding-school');
        Route::get('certificate-awarding-school/{approval_id}/approve-confirmation', [DistrictUserAccountApprovalsController::class, 'approveCertificateSchoolUserAccountConfirmation'])->name('district.user-account-approvals.certificate-awarding-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('certificate-awarding-school/{approval_id}/approve', [DistrictRegistrationApprovalsCertificateController::class, 'approveCertificateSchoolRegistration'])->name('district.user-account-approvals.certificate-awarding-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('certificate-awarding-school/{approval_id}/reject-page', [DistrictRegistrationApprovalsCertificateController::class, 'rejectCertificateSchoolRegPage'])->name('district.user-account-approvals.certificate-awarding-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('certificate-awarding-school/{approval_id}/reject', [DistrictRegistrationApprovalsCertificateController::class, 'rejectCertificateSchoolRegistration'])->name('district.user-account-approvals.certificate-awarding-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Diploma
        Route::get('diploma-awarding-school', [DistrictUserAccountApprovalsController::class, 'diplomaUserAccountApproval'])->name('district.user-account-approvals.diploma-awarding-school');
        Route::get('diploma-awarding-school/{approval_id}/approve-confirmation', [DistrictUserAccountApprovalsController::class, 'approveDiplomaSchoolUserAccountConfirmation'])->name('district.user-account-approvals.diploma-awarding-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('diploma-awarding-school/{approval_id}/approve', [DistrictRegistrationApprovalsDiplomaController::class, 'approveDiplomaSchoolRegistration'])->name('district.user-account-approvals.diploma-awarding-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('diploma-awarding-school/{approval_id}/reject-page', [DistrictRegistrationApprovalsDiplomaController::class, 'rejectDiplomaSchoolRegPage'])->name('district.user-account-approvals.diploma-awarding-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('diploma-awarding-school/{approval_id}/reject', [DistrictRegistrationApprovalsDiplomaController::class, 'rejectDiplomaSchoolRegistration'])->name('district.user-account-approvals.diploma-awarding-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Degree
        Route::get('degree-awarding-school', [DistrictUserAccountApprovalsController::class, 'degreeUserAccountApproval'])->name('district.user-account-approvals.degree-awarding-school');
        Route::get('degree-awarding-school/{approval_id}/approve-confirmation', [DistrictUserAccountApprovalsController::class, 'approveDegreeSchoolUserAccountConfirmation'])->name('district.user-account-approvals.degree-awarding-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('degree-awarding-school/{approval_id}/approve', [DistrictRegistrationApprovalsDegreeController::class, 'approveDegreeSchoolRegistration'])->name('district.user-account-approvals.degree-awarding-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('degree-awarding-school/{approval_id}/reject-page', [DistrictRegistrationApprovalsDegreeController::class, 'rejectDegreeSchoolRegPage'])->name('district.user-account-approvals.degree-awarding-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('degree-awarding-school/{approval_id}/reject', [DistrictRegistrationApprovalsDegreeController::class, 'rejectDegreeSchoolRegistration'])->name('district.user-account-approvals.degree-awarding-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //International
        Route::get('international-school', [DistrictUserAccountApprovalsController::class, 'internationalUserAccountApproval'])->name('district.user-account-approvals.international-school');
        Route::get('international-school/{approval_id}/approve-confirmation', [DistrictUserAccountApprovalsController::class, 'approveInternationalSchoolUserAccountConfirmation'])->name('district.user-account-approvals.international-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('international-school/{approval_id}/approve', [DistrictRegistrationApprovalsInternationalController::class, 'approveInternationalSchoolRegistration'])->name('district.user-account-approvals.international-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('international-school/{approval_id}/reject-page', [DistrictRegistrationApprovalsInternationalController::class, 'rejectInternationalSchoolRegPage'])->name('district.user-account-approvals.international-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('international-school/{approval_id}/reject', [DistrictRegistrationApprovalsInternationalController::class, 'rejectInternationalSchoolRegistration'])->name('district.user-account-approvals.international-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
    });

    // EMIS number applications
    Route::prefix('emis-number-applications')->middleware([RedirectIfNoPermission::class.':emis-number-applications-view'])->name('district-emis-number-applications.')->group(function () {
        Route::get('pre-primary-school', [DistrictEmisNumberApplicationController::class, 'prePrimaryIndex'])->name('pre-primary.index');
        Route::get('primary-school', [DistrictEmisNumberApplicationController::class, 'primaryIndex'])->name('primary.index');
        Route::get('secondary-school', [DistrictEmisNumberApplicationController::class, 'secondaryIndex'])->name('secondary.index');
        Route::get('certificate-awarding-institution', [DistrictEmisNumberApplicationController::class, 'certificateIndex'])->name('certificate.index');
        Route::get('diploma-awarding-institution', [DistrictEmisNumberApplicationController::class, 'diplomaIndex'])->name('diploma.index');
        Route::get('degree-awarding-institution', [DistrictEmisNumberApplicationController::class, 'degreeIndex'])->name('degree.index');
        Route::get('international-school', [DistrictEmisNumberApplicationController::class, 'internationalIndex'])->name('international.index');

        Route::get('{referenceNumber}', [DistrictEmisNumberApplicationController::class, 'show'])->name('show');
    });

    // Learner Enrolment Applications
    Route::prefix('learner-pre-registration-applications')->middleware([RedirectIfNoPermission::class.':enrolments-applications-view'])->name('district-learner-enrolment-applications.')->group(function () {
        Route::get('pending', [DistrictLearnerEnrolmentApplicationsController::class, 'pending'])->name('pending');
        Route::get('approved', [DistrictLearnerEnrolmentApplicationsController::class, 'approved'])->name('approved');
        Route::get('rejected', [DistrictLearnerEnrolmentApplicationsController::class, 'rejected'])->name('rejected');
        Route::get('{status}/{referenceNumber}', [DistrictLearnerEnrolmentApplicationsController::class, 'show'])->name('show');
    });

    // HR Postings
    Route::prefix('postings')->middleware([RedirectIfNoPermission::class.':teachers-postings-view|non-teaching-staff-postings-view'])->name('district-hr-postings.')->group(function () {
        Route::prefix('teachers')->name('teachers.')->group(function () {
            Route::get('{primary}', [DistrictTeacherPostingController::class, 'index'])->name('primary');
            Route::get('{secondary}', [DistrictTeacherPostingController::class, 'index'])->name('secondary');
            Route::get('{certificate}', [DistrictTeacherPostingController::class, 'index'])->name('certificate');
            Route::get('{diploma}', [DistrictTeacherPostingController::class, 'index'])->name('diploma');
        });
        Route::prefix('non-teaching-staff')->name('non-teaching-staff.')->group(function () {
            Route::get('{certificate}', [DistrictNonTeachingStaffPostingController::class, 'index'])->name('certificate');
            Route::get('{diploma}', [DistrictNonTeachingStaffPostingController::class, 'index'])->name('diploma');
        });
    });

    // HR Transfers
    Route::prefix('transfers')->middleware([RedirectIfNoPermission::class.':teachers-transfers-view|non-teaching-staff-transfers-view'])->name('district-hr-transfers.')->group(function () {
        Route::prefix('teachers')->name('teachers.')->group(function () {
            Route::get('{primary}', [DistrictTeacherTransferController::class, 'index'])->name('primary');
            Route::get('{secondary}', [DistrictTeacherTransferController::class, 'index'])->name('secondary');
            Route::get('{certificate}', [DistrictTeacherTransferController::class, 'index'])->name('certificate');
            Route::get('{diploma}', [DistrictTeacherTransferController::class, 'index'])->name('diploma');

            Route::get('{level}/create', [DistrictTeacherTransferController::class, 'create'])->name('create');
        });
        Route::prefix('non-teaching-staff')->name('non-teaching-staff.')->group(function () {
            Route::get('{certificate}', [DistrictNonTeachingStaffTransferController::class, 'index'])->name('certificate');
            Route::get('{diploma}', [DistrictNonTeachingStaffTransferController::class, 'index'])->name('diploma');

            Route::get('{level}/create', [DistrictNonTeachingStaffTransferController::class, 'create'])->name('create');
        });
    });

    // Manage schools
    Route::prefix('manage-institutions')->middleware([RedirectIfNoPermission::class.':institutions-view'])->group(function () {
        // Pre-primary
        Route::get('pre-primary', [DistrictManageInstitutionsPrePrimaryController::class, 'managePrePrimary'])->name('district-manage-preprimary-schools');
        Route::get('view-pre-primary-institution/{id}', [DistrictManageInstitutionsPrePrimaryController::class, 'viewPrePrimaryInstitution'])->name('district-view-pre-primary-schools');

        //Primary
        Route::get('primary', [DistrictManageInstitutionsPrimaryController::class, 'managePrimary'])->name('district-manage-primary-schools');
        Route::get('view-primary-institution/{id}', [DistrictManageInstitutionsPrimaryController::class, 'viewPrimaryInstitution'])->name('district-view-primary-schools');
        Route::post('update-primary-institution-location-details/{id}', [DistrictManageInstitutionsPrimaryController::class, 'updatePrimaryLocationDetails'])->name('district-update-primary-location-details');

        //Secondary
        Route::get('secondary', [DistrictManageInstitutionsSecondaryController::class, 'manageSecondary'])->name('district-manage-secondary-schools');
        Route::get('view-secondary-institution/{id}', [DistrictManageInstitutionsSecondaryController::class, 'viewSecondaryInstitution'])->name('district-view-secondary-schools');
        Route::post('update-secondary-institution-location-details/{id}', [DistrictManageInstitutionsSecondaryController::class, 'updateSecondaryLocationDetails'])->name('district-update-secondary-location-details');

        //Certificate
        Route::get('certificate', [DistrictManageInstitutionsCertificateController::class, 'manageCertificate'])->name('district-manage-certificate-schools');
        Route::get('view-certificate-institution/{id}', [DistrictManageInstitutionsCertificateController::class, 'viewCertificateInstitution'])->name('district-view-certificate-schools');
        Route::post('update-certificate-institution-location-details/{id}', [DistrictManageInstitutionsCertificateController::class, 'updateCertificateLocationDetails'])->name('district-update-certificate-location-details');

        //Diploma
        Route::get('diploma', [DistrictManageInstitutionsDiplomaController::class, 'manageDiploma'])->name('district-manage-diploma-schools');
        Route::get('view-diploma-institution/{id}', [DistrictManageInstitutionsDiplomaController::class, 'viewDiplomaInstitution'])->name('district-view-diploma-schools');
        Route::post('update-diploma-institution-location-details/{id}', [DistrictManageInstitutionsDiplomaController::class, 'updateDiplomaLocationDetails'])->name('district-update-diploma-location-details');

        //Degree
        Route::get('degree', [DistrictManageInstitutionsDegreeController::class, 'manageDegree'])->name('district-manage-degree-schools');
        Route::get('view-degree-institution/{id}', [DistrictManageInstitutionsDegreeController::class, 'viewDegreeInstitution'])->name('district-view-degree-schools');
        Route::post('update-degree-institution-location-details/{id}', [DistrictManageInstitutionsDegreeController::class, 'updateDegreeLocationDetails'])->name('district-update-degree-location-details');

        //International
        Route::get('international', [DistrictManageInstitutionsInternationalController::class, 'manageInternational'])->name('district-manage-international-schools');
        Route::get('view-international-institution/{id}', [DistrictManageInstitutionsInternationalController::class, 'viewInternationalInstitution'])->name('district-view-international-schools');
    });

    // Manage learners
    Route::prefix('manage-learners')->middleware([RedirectIfNoPermission::class.':learners-view'])->group(function () {
        //pre-primary learners
        Route::get('manage-pre-primary', [DistrictLearnersController::class, 'managePrePrimaryLearners'])->name('district.manage-pre-primary-learners');
        Route::get('filter-pre-primary-learners', [DistrictLearnersFilterController::class, 'searchPrePrimaryLearners'])->name('district-search-pre-primary-learner-filter');
        Route::get('view-preprimary-learner-profile/{lin}', [DistrictLearnersController::class, 'viewSinglePrePrimaryLearnerProfile'])->name('district-pre-primary-learner-profile-view');

        //primary learners
        Route::get('manage-primary', [DistrictLearnersController::class, 'managePrimaryLearners'])->name('district.manage-primary-learners');
        Route::get('filter-primary-learners', [DistrictLearnersFilterController::class, 'searchPrimaryLearners'])->name('district-search-primary-learner-filter');
        Route::get('view-primary-learner-profile/{lin}', [DistrictLearnersController::class, 'viewSinglePrimaryLearnerProfile'])->name('district-primary-learner-profile-view');

        //secondary learners
        Route::get('manage-secondary', [DistrictLearnersController::class, 'manageSecondaryLearners'])->name('district.manage-secondary-learners');
        Route::get('filter-secondary-learners', [DistrictLearnersFilterController::class, 'searchSecondaryLearners'])->name('district-search-secondary-learner-filter');
        Route::get('view-secondary-learner-profile/{lin}', [DistrictLearnersController::class, 'viewSingleSecondaryLearnerProfile'])->name('district-secondary-learner-profile-view');

        //certificate students
        Route::get('manage-certificate', [DistrictLearnersController::class, 'manageCertificateLearners'])->name('district.manage-certificate-learners');
        Route::get('filter-certificate-students', [DistrictLearnersFilterController::class, 'searchCertificateLearners'])->name('district-search-certificate-learner-filter');
        Route::get('view-certificate-student-profile/{lin}', [DistrictLearnersController::class, 'viewSingleCertificateLearnerProfile'])->name('district-certificate-learner-profile-view');

        //diploma students
        Route::get('manage-diploma', [DistrictLearnersController::class, 'manageDiplomaLearners'])->name('district.manage-diploma-learners');
        Route::get('filter-diploma-students', [DistrictLearnersFilterController::class, 'searchDiplomaLearners'])->name('district-search-diploma-learner-filter');
        Route::get('view-diploma-student-profile/{lin}', [DistrictLearnersController::class, 'viewSingleDiplomaLearnerProfile'])->name('district-diploma-learner-profile-view');

        //degree students
        Route::get('manage-degree', [DistrictLearnersController::class, 'manageDegreeLearners'])->name('district.manage-degree-learners');
        Route::get('filter-degree-students', [DistrictLearnersFilterController::class, 'searchDegreeLearners'])->name('district-search-degree-learner-filter');
        Route::get('view-degree-student-profile/{lin}', [DistrictLearnersController::class, 'viewSingleDegreeLearnerProfile'])->name('district-degree-learner-profile-view');
    });

    //Enrolments stats
    Route::prefix('manage-enrolments')->middleware([RedirectIfNoPermission::class.':reports-view'])->group(function () {
        //Pre-primary
        Route::get('view-pre-primary-enrolments', [DistrictManageInstitutionsPrePrimaryEnrolmentsController::class, 'manageEnrolments'])->name('district-view-pre-primary-enrolments');
        //Primary
        Route::get('view-primary-enrolments', [DistrictManageInstitutionsPrimaryEnrolmentsController::class, 'manageEnrolments'])->name('district-view-primary-enrolments');
        //Secondary
        Route::get('view-secondary-enrolments', [DistrictManageInstitutionsSecondaryEnrolmentsController::class, 'manageEnrolments'])->name('district-view-secondary-enrolments');
        //Certificate
        Route::get('view-certificate-enrolments', [DistrictManageInstitutionsCertificateEnrolmentsController::class, 'manageEnrolments'])->name('district-view-certificate-enrolments');
        //Diploma
        Route::get('view-diploma-enrolments', [DistrictManageInstitutionsDiplomaEnrolmentsController::class, 'manageEnrolments'])->name('district-view-diploma-enrolments');
        //Degree
        Route::get('view-degree-enrolments', [DistrictManageInstitutionsDegreeEnrolmentsController::class, 'manageEnrolments'])->name('district-view-degree-enrolments');
        //International
        Route::get('view-international-enrolments', [DistrictManageInstitutionsInternationalEnrolmentsController::class, 'manageEnrolments'])->name('district-view-international-enrolments');
    });

    // Human Resource
    Route::prefix('manage-human-resource')->middleware([RedirectIfNoPermission::class.':teachers-view|non-teaching-staff-view'])->group(function () {
        // Pre-primary
        // Caregivers
        Route::get('view-pre-primary-teaching-staff', [DistrictManageTeachingStaffController::class, 'prePrimaryTeachingStaff'])->name('district-view-pre-primary-teaching-staff');
        Route::get('filter-pre-primary-teaching-staff', [DistrictManageTeachingStaffController::class, 'searchPrePrimaryTeachingStaff'])->name('district-search-pre-primary-teaching-staff');
        Route::get('view-pre-primary-teaching-staff-profile/{hrin}', [DistrictManageTeachingStaffController::class, 'viewSinglePrePrimaryTeachingStaffProfile'])->name('district-view-pre-primary-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-pre-primary-non-teaching-staff', [DistrictManageSupportStaffController::class, 'prePrimaryNonTeachingStaff'])->name('district-view-pre-primary-non-teaching-staff');
        Route::get('filter-pre-primary-non-teaching-staff', [DistrictManageSupportStaffController::class, 'searchPrePrimaryNonTeachingStaff'])->name('district-search-pre-primary-non-teaching-staff');
        Route::get('view-pre-primary-non-teaching-staff-profile/{hrin}', [DistrictManageSupportStaffController::class, 'viewSinglePrePrimaryNonTeachingStaffProfile'])->name('district-view-pre-primary-non-teaching-staff-profile');

        //Primary
        // Teachers
        Route::get('view-primary-teaching-staff', [DistrictManageTeachingStaffController::class, 'primaryTeachingStaff'])->name('district-view-primary-teaching-staff');
        Route::get('filter-primary-teaching-staff', [DistrictManageTeachingStaffController::class, 'searchPrimaryTeachingStaff'])->name('district-search-primary-teaching-staff');
        Route::get('view-primary-teaching-staff-profile/{hrin}', [DistrictManageTeachingStaffController::class, 'viewSinglePrimaryTeachingStaffProfile'])->name('district-view-primary-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-primary-non-teaching-staff', [DistrictManageSupportStaffController::class, 'primaryNonTeachingStaff'])->name('district-view-primary-non-teaching-staff');
        Route::get('filter-primary-non-teaching-staff', [DistrictManageSupportStaffController::class, 'searchPrimaryNonTeachingStaff'])->name('district-search-primary-non-teaching-staff');
        Route::get('view-primary-non-teaching-staff-profile/{hrin}', [DistrictManageSupportStaffController::class, 'viewSinglePrimaryNonTeachingStaffProfile'])->name('district-view-primary-non-teaching-staff-profile');

        //Secondary
        // Teachers
        Route::get('view-secondary-teaching-staff', [DistrictManageTeachingStaffController::class, 'secondaryTeachingStaff'])->name('district-view-secondary-teaching-staff');
        Route::get('filter-secondary-teaching-staff', [DistrictManageTeachingStaffController::class, 'searchSecondaryTeachingStaff'])->name('district-search-secondary-teaching-staff');
        Route::get('view-secondary-teaching-staff-profile/{hrin}', [DistrictManageTeachingStaffController::class, 'viewSingleSecondaryTeachingStaffProfile'])->name('district-view-secondary-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-secondary-non-teaching-staff', [DistrictManageSupportStaffController::class, 'secondaryNonTeachingStaff'])->name('district-view-secondary-non-teaching-staff');
        Route::get('filter-secondary-non-teaching-staff', [DistrictManageSupportStaffController::class, 'searchSecondaryNonTeachingStaff'])->name('district-search-secondary-non-teaching-staff');
        Route::get('view-secondary-non-teaching-staff-profile/{hrin}', [DistrictManageSupportStaffController::class, 'viewSingleSecondaryNonTeachingStaffProfile'])->name('district-view-secondary-non-teaching-staff-profile');

        //Certificate
        // Tutors
        Route::get('view-certificate-teaching-staff', [DistrictManageTeachingStaffController::class, 'certificateTeachingStaff'])->name('district-view-certificate-teaching-staff');
        Route::get('filter-certificate-teaching-staff', [DistrictManageTeachingStaffController::class, 'searchCertificateTeachingStaff'])->name('district-search-certificate-teaching-staff');
        Route::get('view-certificate-teaching-staff-profile/{hrin}', [DistrictManageTeachingStaffController::class, 'viewSingleCertificateTeachingStaffProfile'])->name('district-view-certificate-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-certificate-non-teaching-staff', [DistrictManageSupportStaffController::class, 'certificateNonTeachingStaff'])->name('district-view-certificate-non-teaching-staff');
        Route::get('filter-certificate-non-teaching-staff', [DistrictManageSupportStaffController::class, 'searchCertificateNonTeachingStaff'])->name('district-search-certificate-non-teaching-staff');
        Route::get('view-certificate-non-teaching-staff-profile/{hrin}', [DistrictManageSupportStaffController::class, 'viewSingleCertificateNonTeachingStaffProfile'])->name('district-view-certificate-non-teaching-staff-profile');

        //Diploma
        // Lecturers
        Route::get('view-diploma-teaching-staff', [DistrictManageTeachingStaffController::class, 'diplomaTeachingStaff'])->name('district-view-diploma-teaching-staff');
        Route::get('filter-diploma-teaching-staff', [DistrictManageTeachingStaffController::class, 'searchDiplomaTeachingStaff'])->name('district-search-diploma-teaching-staff');
        Route::get('view-diploma-teaching-staff-profile/{hrin}', [DistrictManageTeachingStaffController::class, 'viewSingleDiplomaTeachingStaffProfile'])->name('district-view-diploma-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-diploma-non-teaching-staff', [DistrictManageSupportStaffController::class, 'diplomaNonTeachingStaff'])->name('district-view-diploma-non-teaching-staff');
        Route::get('filter-diploma-non-teaching-staff', [DistrictManageSupportStaffController::class, 'searchDiplomaNonTeachingStaff'])->name('district-search-diploma-non-teaching-staff');
        Route::get('view-diploma-non-teaching-staff-profile/{hrin}', [DistrictManageSupportStaffController::class, 'viewSingleDiplomaNonTeachingStaffProfile'])->name('district-view-diploma-non-teaching-staff-profile');

        //Degree
        // Lecturers
        Route::get('view-degree-teaching-staff', [DistrictManageTeachingStaffController::class, 'degreeTeachingStaff'])->name('district-view-degree-teaching-staff');
        Route::get('filter-degree-teaching-staff', [DistrictManageTeachingStaffController::class, 'searchDegreeTeachingStaff'])->name('district-search-degree-teaching-staff');
        Route::get('view-degree-teaching-staff-profile/{hrin}', [DistrictManageTeachingStaffController::class, 'viewSingleDegreeTeachingStaffProfile'])->name('district-view-degree-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-degree-non-teaching-staff', [DistrictManageSupportStaffController::class, 'degreeNonTeachingStaff'])->name('district-view-degree-non-teaching-staff');
        Route::get('filter-degree-non-teaching-staff', [DistrictManageSupportStaffController::class, 'searchDegreeNonTeachingStaff'])->name('district-search-degree-non-teaching-staff');
        Route::get('view-degree-non-teaching-staff-profile/{hrin}', [DistrictManageSupportStaffController::class, 'viewSingleDegreeNonTeachingStaffProfile'])->name('district-view-degree-non-teaching-staff-profile');
    });

    // EMIS Notices
    Route::prefix('emis-notices')->middleware([RedirectIfNoPermission::class.':notices-view'])->group(function () {
        Route::get('manage', [DistrictNoticesController::class, 'manageNotices'])->name('district-user.notices.manage');
        Route::get('filter', [DistrictNoticesController::class, 'filterNotices'])->name('district-user-notices.filter');
        Route::get('view/{id}', [DistrictNoticesController::class, 'viewSingleNotice'])->name('district-user.notice.view-single');
        Route::get('download-pdf/{id}', [DistrictNoticesController::class, 'downloadPdf'])->name('district-user.notice.download-pdf');
    });

    //Publications
    Route::prefix('publications')->middleware([RedirectIfNoPermission::class.':publications-view'])->group(function () {
        Route::get('/manage', [DistrictPublicationsController::class, 'managePublications'])->name('district-publication-manage');
        Route::get('/download-file/{id}', [DistrictPublicationsController::class, 'downloadFile'])->name('district-download-pdf-file')->middleware([RedirectIfNoPermission::class.':publications-download']);
    });

});

// SubCounty User
Route::middleware(['lowerLocalGovernment', 'adminTwoFactor'])->prefix('sub-county')->group(function () {
    Route::get('dashboard', [SubCountyUserController::class, 'dashboard'])->name('sub-county-dashboard');
    Route::get('profile', [SubCountyUserController::class, 'profile'])->name('sub-county-user-profile');

    //    Route::get('licence-applications-list',[InstitutionLicenceController::class,'pending_licence_applications']);
    //    Route::get('licence-and-registration/{id}',[InstitutionLicenceController::class,'licence_application_details']);
    //    Route::resource('licence-and-registration', InstitutionLicenceController::class, ['as'=>'district']);

    // User account approvals
    Route::prefix('user-account-approval')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view'])->group(function () {
        //Pre-primary
        Route::get('pre-primary-school', [SubCountyUserAccountApprovalsController::class, 'prePrimaryUserAccountApproval'])->name('sub-county.user-account-approvals.pre-primary-school');
        Route::get('pre-primary-school/{approval_id}/approve-confirmation', [SubCountyUserAccountApprovalsController::class, 'approvePrePrimarySchoolUserAccountConfirmation'])->name('sub-county.user-account-approvals.pre-primary-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view']);
        Route::get('pre-primary-school/{approval_id}/approve', [SubCountyRegistrationApprovalsPrePrimaryController::class, 'approvePrePrimarySchoolRegistration'])->name('sub-county.user-account-approvals.pre-primary-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('pre-primary-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsPrePrimaryController::class, 'rejectPrePrimarySchoolRegPage'])->name('sub-county.user-account-approvals.pre-primary-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('pre-primary-school/{approval_id}/reject', [SubCountyRegistrationApprovalsPrePrimaryController::class, 'rejectPrePrimarySchoolRegistration'])->name('sub-county.user-account-approvals.pre-primary-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Primary
        Route::get('primary-school', [SubCountyUserAccountApprovalsController::class, 'primaryUserAccountApproval'])->name('sub-county.user-account-approvals.primary-school');
        Route::get('primary-school/{approval_id}/approve-confirmation', [SubCountyUserAccountApprovalsController::class, 'approvePrimarySchoolUserAccountConfirmation'])->name('sub-county.user-account-approvals.primary-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view']);
        Route::get('primary-school/{approval_id}/approve', [SubCountyRegistrationApprovalsPrimaryController::class, 'approvePrimarySchoolRegistration'])->name('sub-county.user-account-approvals.primary-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('primary-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsPrimaryController::class, 'rejectPrimarySchoolRegPage'])->name('sub-county.user-account-approvals.primary-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('primary-school/{approval_id}/reject', [SubCountyRegistrationApprovalsPrimaryController::class, 'rejectPrimarySchoolRegistration'])->name('sub-county.user-account-approvals.primary-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Secondary
        Route::get('secondary-school', [SubCountyUserAccountApprovalsController::class, 'secondaryUserAccountApproval'])->name('sub-county.user-account-approvals.secondary-school');
        Route::get('secondary-school/{approval_id}/approve-confirmation', [SubCountyUserAccountApprovalsController::class, 'approveSecondarySchoolUserAccountConfirmation'])->name('sub-county.user-account-approvals.secondary-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view']);
        Route::get('secondary-school/{approval_id}/approve', [SubCountyRegistrationApprovalsSecondaryController::class, 'approveSecondarySchoolRegistration'])->name('sub-county.user-account-approvals.secondary-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('secondary-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsSecondaryController::class, 'rejectSecondarySchoolRegPage'])->name('sub-county.user-account-approvals.secondary-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('secondary-school/{approval_id}/reject', [SubCountyRegistrationApprovalsSecondaryController::class, 'rejectSecondarySchoolRegistration'])->name('sub-county.user-account-approvals.secondary-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Certificate
        Route::get('certificate-awarding-school', [SubCountyUserAccountApprovalsController::class, 'certificateUserAccountApproval'])->name('sub-county.user-account-approvals.certificate-awarding-school');
        Route::get('certificate-awarding-school/{approval_id}/approve-confirmation', [SubCountyUserAccountApprovalsController::class, 'approveCertificateSchoolUserAccountConfirmation'])->name('sub-county.user-account-approvals.certificate-awarding-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view']);
        Route::get('certificate-awarding-school/{approval_id}/approve', [SubCountyRegistrationApprovalsCertificateController::class, 'approveCertificateSchoolRegistration'])->name('sub-county.user-account-approvals.certificate-awarding-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('certificate-awarding-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsCertificateController::class, 'rejectCertificateSchoolRegPage'])->name('sub-county.user-account-approvals.certificate-awarding-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('certificate-awarding-school/{approval_id}/reject', [SubCountyRegistrationApprovalsCertificateController::class, 'rejectCertificateSchoolRegistration'])->name('sub-county.user-account-approvals.certificate-awarding-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Diploma
        Route::get('diploma-awarding-school', [SubCountyUserAccountApprovalsController::class, 'diplomaUserAccountApproval'])->name('sub-county.user-account-approvals.diploma-awarding-school');
        Route::get('diploma-awarding-school/{approval_id}/approve-confirmation', [SubCountyUserAccountApprovalsController::class, 'approveDiplomaSchoolUserAccountConfirmation'])->name('sub-county.user-account-approvals.diploma-awarding-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view']);
        Route::get('diploma-awarding-school/{approval_id}/approve', [SubCountyRegistrationApprovalsDiplomaController::class, 'approveDiplomaSchoolRegistration'])->name('sub-county.user-account-approvals.diploma-awarding-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('diploma-awarding-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsDiplomaController::class, 'rejectDiplomaSchoolRegPage'])->name('sub-county.user-account-approvals.diploma-awarding-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('diploma-awarding-school/{approval_id}/reject', [SubCountyRegistrationApprovalsDiplomaController::class, 'rejectDiplomaSchoolRegistration'])->name('sub-county.user-account-approvals.diploma-awarding-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //Degree
        Route::get('degree-awarding-school', [SubCountyUserAccountApprovalsController::class, 'degreeUserAccountApproval'])->name('sub-county.user-account-approvals.degree-awarding-school');
        Route::get('degree-awarding-school/{approval_id}/approve-confirmation', [SubCountyUserAccountApprovalsController::class, 'approveDegreeSchoolUserAccountConfirmation'])->name('sub-county.user-account-approvals.degree-awarding-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view']);
        Route::get('degree-awarding-school/{approval_id}/approve', [SubCountyRegistrationApprovalsDegreeController::class, 'approveDegreeSchoolRegistration'])->name('sub-county.user-account-approvals.degree-awarding-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('degree-awarding-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsDegreeController::class, 'rejectDegreeSchoolRegPage'])->name('sub-county.user-account-approvals.degree-awarding-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('degree-awarding-school/{approval_id}/reject', [SubCountyRegistrationApprovalsDegreeController::class, 'rejectDegreeSchoolRegistration'])->name('sub-county.user-account-approvals.degree-awarding-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);

        //International
        Route::get('international-school', [SubCountyUserAccountApprovalsController::class, 'internationalUserAccountApproval'])->name('sub-county.user-account-approvals.international-school');
        Route::get('international-school/{approval_id}/approve-confirmation', [SubCountyUserAccountApprovalsController::class, 'approveInternationalSchoolUserAccountConfirmation'])->name('sub-county.user-account-approvals.international-school.approve-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view']);
        Route::get('international-school/{approval_id}/approve', [SubCountyRegistrationApprovalsInternationalController::class, 'approveInternationalSchoolRegistration'])->name('sub-county.user-account-approvals.international-school.approve')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-approve']);
        Route::get('international-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsInternationalController::class, 'rejectInternationalSchoolRegPage'])->name('sub-county.user-account-approvals.international-school.reject-page')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
        Route::get('international-school/{approval_id}/reject', [SubCountyRegistrationApprovalsInternationalController::class, 'rejectInternationalSchoolRegistration'])->name('sub-county.user-account-approvals.international-school.reject')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-reject']);
    });

    // EMIS number applications
    Route::prefix('emis-number-applications')->middleware([RedirectIfNoPermission::class.':emis-number-applications-view'])->name('sub-county-emis-number-applications.')->group(function () {
        Route::get('pre-primary-school', [SubCountyEmisNumberApplicationController::class, 'prePrimaryIndex'])->name('pre-primary.index');
        Route::get('primary-school', [SubCountyEmisNumberApplicationController::class, 'primaryIndex'])->name('primary.index');
        Route::get('secondary-school', [SubCountyEmisNumberApplicationController::class, 'secondaryIndex'])->name('secondary.index');
        Route::get('certificate-awarding-institution', [SubCountyEmisNumberApplicationController::class, 'certificateIndex'])->name('certificate.index');
        Route::get('diploma-awarding-institution', [SubCountyEmisNumberApplicationController::class, 'diplomaIndex'])->name('diploma.index');
        Route::get('degree-awarding-institution', [SubCountyEmisNumberApplicationController::class, 'degreeIndex'])->name('degree.index');
        Route::get('international-school', [SubCountyEmisNumberApplicationController::class, 'internationalIndex'])->name('international.index');

        Route::get('{referenceNumber}', [SubCountyEmisNumberApplicationController::class, 'show'])->name('show');
    });

    // Manage schools
    Route::prefix('manage-institutions')->middleware([RedirectIfNoPermission::class.':institutions-view'])->group(function () {
        // Pre-primary
        Route::get('pre-primary', [SubCountyManageInstitutionsPrePrimaryController::class, 'managePrePrimary'])->name('sub-county-manage-preprimary-schools');
        Route::get('view-pre-primary-institution/{id}', [SubCountyManageInstitutionsPrePrimaryController::class, 'viewPrePrimaryInstitution'])->name('sub-county-view-pre-primary-schools');

        //Primary
        Route::get('primary', [SubCountyManageInstitutionsPrimaryController::class, 'managePrimary'])->name('sub-county-manage-primary-schools');
        Route::get('view-primary-institution/{id}', [SubCountyManageInstitutionsPrimaryController::class, 'viewPrimaryInstitution'])->name('sub-county-view-primary-schools');
        Route::post('update-primary-institution-location-details/{id}', [SubCountyManageInstitutionsPrimaryController::class, 'updatePrimaryLocationDetails'])->name('sub-county-update-primary-location-details');

        //Secondary
        Route::get('secondary', [SubCountyManageInstitutionsSecondaryController::class, 'manageSecondary'])->name('sub-county-manage-secondary-schools');
        Route::get('view-secondary-institution/{id}', [SubCountyManageInstitutionsSecondaryController::class, 'viewSecondaryInstitution'])->name('sub-county-view-secondary-schools');
        Route::post('update-secondary-institution-location-details/{id}', [SubCountyManageInstitutionsSecondaryController::class, 'updateSecondaryLocationDetails'])->name('sub-county-update-secondary-location-details');

        //Certificate
        Route::get('certificate', [SubCountyManageInstitutionsCertificateController::class, 'manageCertificate'])->name('sub-county-manage-certificate-schools');
        Route::get('view-certificate-institution/{id}', [SubCountyManageInstitutionsCertificateController::class, 'viewCertificateInstitution'])->name('sub-county-view-certificate-schools');
        Route::post('update-certificate-institution-location-details/{id}', [SubCountyManageInstitutionsCertificateController::class, 'updateCertificateLocationDetails'])->name('sub-county-update-certificate-location-details');

        //Diploma
        Route::get('diploma', [SubCountyManageInstitutionsDiplomaController::class, 'manageDiploma'])->name('sub-county-manage-diploma-schools');
        Route::get('view-diploma-institution/{id}', [SubCountyManageInstitutionsDiplomaController::class, 'viewDiplomaInstitution'])->name('sub-county-view-diploma-schools');
        Route::post('update-diploma-institution-location-details/{id}', [SubCountyManageInstitutionsDiplomaController::class, 'updateDiplomaLocationDetails'])->name('sub-county-update-diploma-location-details');

        //Degree
        Route::get('degree', [SubCountyManageInstitutionsDegreeController::class, 'manageDegree'])->name('sub-county-manage-degree-schools');
        Route::get('view-degree-institution/{id}', [SubCountyManageInstitutionsDegreeController::class, 'viewDegreeInstitution'])->name('sub-county-view-degree-schools');
        Route::post('update-degree-institution-location-details/{id}', [SubCountyManageInstitutionsDegreeController::class, 'updateDegreeLocationDetails'])->name('sub-county-update-degree-location-details');

        //International
        Route::get('international', [SubCountyManageInstitutionsInternationalController::class, 'manageInternational'])->name('sub-county-manage-international-schools');
        Route::get('view-international-institution/{id}', [SubCountyManageInstitutionsInternationalController::class, 'viewInternationalInstitution'])->name('sub-county-view-international-schools');
    });

    // Manage learners
    Route::prefix('manage-learners')->middleware([RedirectIfNoPermission::class.':learners-view'])->group(function () {
        //pre-primary learners
        Route::get('manage-pre-primary', [SubCountyLearnersController::class, 'managePrePrimaryLearners'])->name('sub-county.manage-pre-primary-learners');
        Route::get('filter-pre-primary-learners', [SubCountyLearnersFilterController::class, 'searchPrePrimaryLearners'])->name('sub-county-search-pre-primary-learner-filter');
        Route::get('view-pre-primary-learner-profile/{lin}', [SubCountyLearnersController::class, 'viewSinglePrePrimaryLearnerProfile'])->name('sub-county-pre-primary-learner-profile-view');

        //primary learners
        Route::get('manage-primary', [SubCountyLearnersController::class, 'managePrimaryLearners'])->name('sub-county.manage-primary-learners');
        Route::get('filter-primary-learners', [SubCountyLearnersFilterController::class, 'searchPrimaryLearners'])->name('sub-county-search-primary-learner-filter');
        Route::get('view-primary-learner-profile/{lin}', [SubCountyLearnersController::class, 'viewSinglePrimaryLearnerProfile'])->name('sub-county-primary-learner-profile-view');

        //secondary learners
        Route::get('manage-secondary', [SubCountyLearnersController::class, 'manageSecondaryLearners'])->name('sub-county.manage-secondary-learners');
        Route::get('filter-secondary-learners', [SubCountyLearnersFilterController::class, 'searchSecondaryLearners'])->name('sub-county-search-secondary-learner-filter');
        Route::get('view-secondary-learner-profile/{lin}', [SubCountyLearnersController::class, 'viewSingleSecondaryLearnerProfile'])->name('sub-county-secondary-learner-profile-view');

        //certificate students
        Route::get('manage-certificate', [SubCountyLearnersController::class, 'manageCertificateLearners'])->name('sub-county.manage-certificate-learners');
        Route::get('filter-certificate-students', [SubCountyLearnersFilterController::class, 'searchCertificateLearners'])->name('sub-county-search-certificate-learner-filter');
        Route::get('view-certificate-student-profile/{lin}', [SubCountyLearnersController::class, 'viewSingleCertificateLearnerProfile'])->name('sub-county-certificate-learner-profile-view');

        //diploma students
        Route::get('manage-diploma', [SubCountyLearnersController::class, 'manageDiplomaLearners'])->name('sub-county.manage-diploma-learners');
        Route::get('filter-diploma-students', [SubCountyLearnersFilterController::class, 'searchDiplomaLearners'])->name('sub-county-search-diploma-learner-filter');
        Route::get('view-diploma-student-profile/{lin}', [SubCountyLearnersController::class, 'viewSingleDiplomaLearnerProfile'])->name('sub-county-diploma-learner-profile-view');

        //degree students
        Route::get('manage-degree', [SubCountyLearnersController::class, 'manageDegreeLearners'])->name('sub-county.manage-degree-learners');
        Route::get('filter-degree-students', [SubCountyLearnersFilterController::class, 'searchDegreeLearners'])->name('sub-county-search-degree-learner-filter');
        Route::get('view-degree-student-profile/{lin}', [SubCountyLearnersController::class, 'viewSingleDegreeLearnerProfile'])->name('sub-county-degree-learner-profile-view');
    });

    //Enrolments stats
    Route::prefix('manage-enrolments')->middleware([RedirectIfNoPermission::class.':reports-view'])->group(function () {
        //Pre-primary
        Route::get('view-pre-primary-enrolments', [SubCountyManageInstitutionsPrePrimaryEnrolmentsController::class, 'manageEnrolments'])->name('sub-county-view-pre-primary-enrolments');
        //Primary
        Route::get('view-primary-enrolments', [SubCountyManageInstitutionsPrimaryEnrolmentsController::class, 'manageEnrolments'])->name('sub-county-view-primary-enrolments');
        //Secondary
        Route::get('view-secondary-enrolments', [SubCountyManageInstitutionsSecondaryEnrolmentsController::class, 'manageEnrolments'])->name('sub-county-view-secondary-enrolments');
        //Certificate
        Route::get('view-certificate-enrolments', [SubCountyManageInstitutionsCertificateEnrolmentsController::class, 'manageEnrolments'])->name('sub-county-view-certificate-enrolments');
        //Diploma
        Route::get('view-diploma-enrolments', [SubCountyManageInstitutionsDiplomaEnrolmentsController::class, 'manageEnrolments'])->name('sub-county-view-diploma-enrolments');
        //Degree
        Route::get('view-degree-enrolments', [SubCountyManageInstitutionsDegreeEnrolmentsController::class, 'manageEnrolments'])->name('sub-county-view-degree-enrolments');
        //International
        Route::get('view-international-enrolments', [SubCountyManageInstitutionsInternationalEnrolmentsController::class, 'manageEnrolments'])->name('sub-county-view-international-enrolments');
    });

    // Human Resource
    Route::prefix('manage-human-resource')->middleware([RedirectIfNoPermission::class.':teachers-view|non-teaching-staff-view'])->group(function () {
        // Pre-primary
        // Caregivers
        Route::get('view-pre-primary-teaching-staff', [SubCountyManageTeachingStaffController::class, 'prePrimaryTeachingStaff'])->name('sub-county-view-pre-primary-teaching-staff');
        Route::get('filter-pre-primary-teaching-staff', [SubCountyManageTeachingStaffController::class, 'searchPrePrimaryTeachingStaff'])->name('sub-county-search-pre-primary-teaching-staff');
        Route::get('view-pre-primary-teaching-staff-profile/{hrin}', [SubCountyManageTeachingStaffController::class, 'viewSinglePrePrimaryTeachingStaffProfile'])->name('sub-county-view-pre-primary-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-pre-primary-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'prePrimaryNonTeachingStaff'])->name('sub-county-view-pre-primary-non-teaching-staff');
        Route::get('filter-pre-primary-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'searchPrePrimaryNonTeachingStaff'])->name('sub-county-search-pre-primary-non-teaching-staff');
        Route::get('view-pre-primary-non-teaching-staff-profile/{hrin}', [SubCountyManageSupportStaffController::class, 'viewSinglePrePrimaryNonTeachingStaffProfile'])->name('sub-county-view-pre-primary-non-teaching-staff-profile');

        //Primary
        // Teachers
        Route::get('view-primary-teaching-staff', [SubCountyManageTeachingStaffController::class, 'primaryTeachingStaff'])->name('sub-county-view-primary-teaching-staff');
        Route::get('filter-primary-teaching-staff', [SubCountyManageTeachingStaffController::class, 'searchPrimaryTeachingStaff'])->name('sub-county-search-primary-teaching-staff');
        Route::get('view-primary-teaching-staff-profile/{hrin}', [SubCountyManageTeachingStaffController::class, 'viewSinglePrimaryTeachingStaffProfile'])->name('sub-county-view-primary-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-primary-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'primaryNonTeachingStaff'])->name('sub-county-view-primary-non-teaching-staff');
        Route::get('filter-primary-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'searchPrimaryNonTeachingStaff'])->name('sub-county-search-primary-non-teaching-staff');
        Route::get('view-primary-non-teaching-staff-profile/{hrin}', [SubCountyManageSupportStaffController::class, 'viewSinglePrimaryNonTeachingStaffProfile'])->name('sub-county-view-primary-non-teaching-staff-profile');

        //Secondary
        // Teachers
        Route::get('view-secondary-teaching-staff', [SubCountyManageTeachingStaffController::class, 'secondaryTeachingStaff'])->name('sub-county-view-secondary-teaching-staff');
        Route::get('filter-secondary-teaching-staff', [SubCountyManageTeachingStaffController::class, 'searchSecondaryTeachingStaff'])->name('sub-county-search-secondary-teaching-staff');
        Route::get('view-secondary-teaching-staff-profile/{hrin}', [SubCountyManageTeachingStaffController::class, 'viewSingleSecondaryTeachingStaffProfile'])->name('sub-county-view-secondary-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-secondary-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'secondaryNonTeachingStaff'])->name('sub-county-view-secondary-non-teaching-staff');
        Route::get('filter-secondary-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'searchSecondaryNonTeachingStaff'])->name('sub-county-search-secondary-non-teaching-staff');
        Route::get('view-secondary-non-teaching-staff-profile/{hrin}', [SubCountyManageSupportStaffController::class, 'viewSingleSecondaryNonTeachingStaffProfile'])->name('sub-county-view-secondary-non-teaching-staff-profile');

        //Certificate
        // Tutors
        Route::get('view-certificate-teaching-staff', [SubCountyManageTeachingStaffController::class, 'certificateTeachingStaff'])->name('sub-county-view-certificate-teaching-staff');
        Route::get('filter-certificate-teaching-staff', [SubCountyManageTeachingStaffController::class, 'searchCertificateTeachingStaff'])->name('sub-county-search-certificate-teaching-staff');
        Route::get('view-certificate-teaching-staff-profile/{hrin}', [SubCountyManageTeachingStaffController::class, 'viewSingleCertificateTeachingStaffProfile'])->name('sub-county-view-certificate-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-certificate-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'certificateNonTeachingStaff'])->name('sub-county-view-certificate-non-teaching-staff');
        Route::get('filter-certificate-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'searchCertificateNonTeachingStaff'])->name('sub-county-search-certificate-non-teaching-staff');
        Route::get('view-certificate-non-teaching-staff-profile/{hrin}', [SubCountyManageSupportStaffController::class, 'viewSingleCertificateNonTeachingStaffProfile'])->name('sub-county-view-certificate-non-teaching-staff-profile');

        //Diploma
        // Lecturers
        Route::get('view-diploma-teaching-staff', [SubCountyManageTeachingStaffController::class, 'diplomaTeachingStaff'])->name('sub-county-view-diploma-teaching-staff');
        Route::get('filter-diploma-teaching-staff', [SubCountyManageTeachingStaffController::class, 'searchDiplomaTeachingStaff'])->name('sub-county-search-diploma-teaching-staff');
        Route::get('view-diploma-teaching-staff-profile/{hrin}', [SubCountyManageTeachingStaffController::class, 'viewSingleDiplomaTeachingStaffProfile'])->name('sub-county-view-diploma-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-diploma-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'diplomaNonTeachingStaff'])->name('sub-county-view-diploma-non-teaching-staff');
        Route::get('filter-diploma-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'searchDiplomaNonTeachingStaff'])->name('sub-county-search-diploma-non-teaching-staff');
        Route::get('view-diploma-non-teaching-staff-profile/{hrin}', [SubCountyManageSupportStaffController::class, 'viewSingleDiplomaNonTeachingStaffProfile'])->name('sub-county-view-diploma-non-teaching-staff-profile');

        //Degree
        // Lecturers
        Route::get('view-degree-teaching-staff', [SubCountyManageTeachingStaffController::class, 'degreeTeachingStaff'])->name('sub-county-view-degree-teaching-staff');
        Route::get('filter-degree-teaching-staff', [SubCountyManageTeachingStaffController::class, 'searchDegreeTeachingStaff'])->name('sub-county-search-degree-teaching-staff');
        Route::get('view-degree-teaching-staff-profile/{hrin}', [SubCountyManageTeachingStaffController::class, 'viewSingleDegreeTeachingStaffProfile'])->name('sub-county-view-degree-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-degree-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'degreeNonTeachingStaff'])->name('sub-county-view-degree-non-teaching-staff');
        Route::get('filter-degree-non-teaching-staff', [SubCountyManageSupportStaffController::class, 'searchDegreeNonTeachingStaff'])->name('sub-county-search-degree-non-teaching-staff');
        Route::get('view-degree-non-teaching-staff-profile/{hrin}', [SubCountyManageSupportStaffController::class, 'viewSingleDegreeNonTeachingStaffProfile'])->name('sub-county-view-degree-non-teaching-staff-profile');
    });

    // EMIS Notices
    Route::prefix('emis-notices')->middleware([RedirectIfNoPermission::class.':notices-view'])->group(function () {
        Route::get('manage', [SubCountyNoticesController::class, 'manageNotices'])->name('sub-county-user.notices.manage');
        Route::get('filter', [SubCountyNoticesController::class, 'filterNotices'])->name('sub-county-user-notices.filter');
        Route::get('view/{id}', [SubCountyNoticesController::class, 'viewSingleNotice'])->name('sub-county-user.notice.view-single');
        Route::get('download-pdf/{id}', [SubCountyNoticesController::class, 'downloadPdf'])->name('sub-county-user.notice.download-pdf');
    });

    //Publications
    Route::prefix('publications')->group(function () {
        Route::get('/manage', [SubCountyPublicationsController::class, 'managePublications'])->name('sub-county-publication-manage')->middleware([RedirectIfNoPermission::class.':publications-view']);
        Route::get('/download-file/{id}', [SubCountyPublicationsController::class, 'downloadFile'])->name('sub-county-download-pdf-file')->middleware([RedirectIfNoPermission::class.':publications-download']);
    });

});

// Admin User
Route::prefix('admin')->group(function () {
    //Verify OTP
    Route::prefix('login-otp-verify')->group(function () {
        Route::get('resend', [AdminTwoFactorController::class, 'resend'])->name('admin.login-otp-verify.resend');
        Route::get('/', [AdminTwoFactorController::class, 'index'])->name('admin.login-otp-verify.index');
        Route::post('store', [AdminTwoFactorController::class, 'store'])->name('admin.login-otp-verify.store');
    });

    Route::get('/', [LoginController::class, 'showAdminLoginForm'])->name('admin-login-page');
    Route::match(['get', 'post'], 'login', [LoginController::class, 'login'])->name('admin-login-user');

    Route::middleware(['admin'])->group(function () {
        Route::middleware(['adminTwoFactor'])->group(function () {

            //Licence and Registration
            Route::get('licence-and-registration-settings', [InstitutionLicenceController::class, 'lr_settings'])->name('licence-and-registration-settings')->middleware([RedirectIfNoPermission::class.':settings-view']);
            Route::post('licence-applications-settings', [InstitutionLicenceController::class, 'store'])->middleware([RedirectIfNoPermission::class.':settings-create']);

            Route::middleware([RedirectIfNoPermission::class.':licensing-view|registration-view'])->group(function () {
                Route::get('licence-applications-existing', [InstitutionLicenceController::class, 'existing_licence_applications']);
                Route::get('licence-applications-existing/{id}', [InstitutionLicenceController::class, 'existing_licence_details']);
                Route::get('licence-applications-pending', [InstitutionLicenceController::class, 'pending_licence_applications']);
                Route::get('licence-applications-summary', [InstitutionLicenceController::class, 'dashboard']);

                Route::get('licence-applications/letter-of-intent',[InstitutionLicenceController::class,'pending_letter_of_intent_applications']);
                Route::get('licence-applications/letter-of-intent/{id}',[InstitutionLicenceController::class,'letter_of_intent_details']);

                Route::get('licence-applications/{id}',[InstitutionLicenceController::class,'licence_application_details']);

                Route::resource('licence-applications', InstitutionLicenceController::class);
            });

            Route::get('dashboard', [AdminController::class, 'dashboard'])->name('admin-dashboard');
            Route::get('profile', [AdminController::class, 'profile'])->name('admin-user-profile');
            Route::get('login-as-district/{local_government_id}', [AdminController::class, 'loginAsDistrict'])->middleware([RedirectIfNoPermission::class.':users-login-as']);
            Route::get('login-as-sub-county/{local_government_id}', [AdminController::class, 'loginAsSubCounty'])->middleware([RedirectIfNoPermission::class.':users-login-as']);
            Route::get('map/{shape_type?}', [AdminController::class, 'map'])->name('admin-map')->middleware([RedirectIfNoPermission::class.':maps-view']);

            Route::prefix('emis-user-accounts')->middleware([RedirectIfNoPermission::class.':emis-user-accounts-view'])->group(function () {
                Route::get('new', [AdminManageEmisUserApplicationsController::class, 'newApplications'])->name('admin-new-emis-application');
                Route::get('registration-details/{id}', [AdminManageEmisUserApplicationsController::class, 'viewRegistrationApprovalDetails'])->name('admin-registration-approval-details');
                Route::get('approved', [AdminManageEmisUserApplicationsController::class, 'approvedApplications'])->name('admin-approved-emis-application');
                Route::get('rejected', [AdminManageEmisUserApplicationsController::class, 'rejectedApplications'])->name('admin-rejected-emis-application');
                Route::get('search-new', [AdminManageEmisUserApplicationsController::class, 'searchNewEmisApplications'])->name('admin-search-new-emis-application');
                Route::get('search-approved', [AdminManageEmisUserApplicationsController::class, 'searchApprovedEmisApplications'])->name('admin-search-approved-emis-application');
                Route::get('search-rejected', [AdminManageEmisUserApplicationsController::class, 'searchRejectedEmisApplications'])->name('admin-search-rejected-emis-application');

                // Master Deo approvals
                Route::get('school/{approval_id}/approve', [AdminRegistrationApprovalsController::class, 'approveSchoolRegistration'])->name('admin.user-account-approvals.school.approve');
                Route::get('school/{approval_id}/reject-page', [AdminRegistrationApprovalsController::class, 'rejectSchoolRegPage'])->name('admin.user-account-approvals.school.reject-page');
                Route::get('school/{approval_id}/reject', [AdminRegistrationApprovalsController::class, 'rejectSchoolRegistration'])->name('admin.user-account-approvals.school.reject');

                /*Route::get('export-new', [AdminManageEmisUserApplicationsExportController::class, 'exportNewEmisApplications'])->name('admin-export-emis-user-new-applications');
                Route::get('export-approved', [AdminManageEmisUserApplicationsExportController::class, 'exportApprovedEmisApplications'])->name('admin-export-emis-user-approved-applications');
                Route::get('export-rejected', [AdminManageEmisUserApplicationsExportController::class, 'exportRejectedEmisApplications'])->name('admin-export-emis-user-rejected-applications');*/
            });

            // EMIS number applications
            Route::prefix('emis-number-applications')->middleware([RedirectIfNoPermission::class.':emis-number-applications-view'])->name('admin-emis-number-applications.')->group(function () {
                Route::get('pending', [EmisNumberApplicationController::class, 'pending'])->name('pending');
                Route::get('endorsed', [EmisNumberApplicationController::class, 'endorsed'])->name('endorsed');
                Route::get('approved', [EmisNumberApplicationController::class, 'approved'])->name('approved');
                Route::get('rejected', [EmisNumberApplicationController::class, 'rejected'])->name('rejected');
                Route::get('preview/{id}', [EmisNumberApplicationController::class, 'preview'])->name('preview');
                Route::get('pdf/{id}', [EmisNumberApplicationController::class, 'generatePdf'])->name('pdf');

                Route::get('{status}/{referenceNumber}', [EmisNumberApplicationController::class, 'show'])->name('show');
            });

            // Learner Enrolment Applications
            Route::prefix('learner-pre-registration-applications')->middleware([RedirectIfNoPermission::class.':enrolments-applications-view'])->name('admin-learner-enrolment-applications.')->group(function () {
                Route::get('pending', [AdminLearnerEnrolmentApplicationsController::class, 'pending'])->name('pending');
                Route::get('approved', [AdminLearnerEnrolmentApplicationsController::class, 'approved'])->name('approved');
                Route::get('rejected', [AdminLearnerEnrolmentApplicationsController::class, 'rejected'])->name('rejected');
                Route::get('{status}/{applicationNumber}', [AdminLearnerEnrolmentApplicationsController::class, 'show'])->name('show');
            });

            // Manage schools
            Route::prefix('manage-institutions')->middleware([RedirectIfNoPermission::class.':institutions-view'])->group(function () {
                // Pre-primary
                Route::get('pre-primary', [AdminManageInstitutionsPrePrimaryController::class, 'managePrePrimary'])->name('admin-manage-preprimary');
                Route::get('view-pre-primary-institution/{emis_number}', [AdminManageInstitutionsPrePrimaryController::class, 'viewPrePrimaryInstitution'])->name('admin-view-pre-primary');

                //Primary
                Route::get('primary', [AdminManageInstitutionsPrimaryController::class, 'managePrimary'])->name('admin-manage-primary');
                Route::get('view-primary-institution/{emis_number}', [AdminManageInstitutionsPrimaryController::class, 'viewPrimaryInstitution'])->name('admin-view-primary');
                Route::post('update-primary-institution-location-details/{id}', [AdminManageInstitutionsPrimaryController::class, 'updatePrimaryLocationDetails'])->name('admin-update-primary-location-details');

                //Secondary
                Route::get('secondary', [AdminManageInstitutionsSecondaryController::class, 'manageSecondary'])->name('admin-manage-secondary');
                Route::get('view-secondary-institution/{emis_number}', [AdminManageInstitutionsSecondaryController::class, 'viewSecondaryInstitution'])->name('admin-view-secondary');
                Route::post('update-secondary-institution-location-details/{id}', [AdminManageInstitutionsSecondaryController::class, 'updateSecondaryLocationDetails'])->name('admin-update-secondary-location-details');

                //Certificate
                Route::get('certificate', [AdminManageInstitutionsCertificateController::class, 'manageCertificate'])->name('admin-manage-certificate');
                Route::get('view-certificate-institution/{emis_number}', [AdminManageInstitutionsCertificateController::class, 'viewCertificateInstitution'])->name('admin-view-certificate');

                //Diploma
                Route::get('diploma', [AdminManageInstitutionsDiplomaController::class, 'manageDiploma'])->name('admin-manage-diploma');
                Route::get('view-diploma-institution/{emis_number}', [AdminManageInstitutionsDiplomaController::class, 'viewDiplomaInstitution'])->name('admin-view-diploma');

                //Degree
                Route::get('degree', [AdminManageInstitutionsDegreeController::class, 'manageDegree'])->name('admin-manage-degree');
                Route::get('view-degree-institution/{emis_number}', [AdminManageInstitutionsDegreeController::class, 'viewDegreeInstitution'])->name('admin-view-degree');

                //International
                Route::get('international', [AdminManageInstitutionsInternationalController::class, 'manageInternational'])->name('admin-manage-international');
                Route::get('view-international-institution/{emis_number}', [AdminManageInstitutionsInternationalController::class, 'viewInternationalInstitution'])->name('admin-view-international');
            });

            Route::prefix('manage-enrolments')->middleware([RedirectIfNoPermission::class.':reports-view'])->group(function () {
                //Pre-primary
                Route::get('view-pre-primary-enrolments', [AdminManageInstitutionsPrePrimaryEnrolmentsController::class, 'manageEnrolments'])->name('admin-view-pre-primary-enrolments');
                //Primary
                Route::get('view-primary-enrolments', [AdminManageInstitutionsPrimaryEnrolmentsController::class, 'manageEnrolments'])->name('admin-view-primary-enrolments');
                //Secondary
                Route::get('view-secondary-enrolments', [AdminManageInstitutionsSecondaryEnrolmentsController::class, 'manageEnrolments'])->name('admin-view-secondary-enrolments');
                //Certificate
                Route::get('view-certificate-enrolments', [AdminManageInstitutionsCertificateEnrolmentsController::class, 'manageEnrolments'])->name('admin-view-certificate-enrolments');
                //Diploma
                Route::get('view-diploma-enrolments', [AdminManageInstitutionsDiplomaEnrolmentsController::class, 'manageEnrolments'])->name('admin-view-diploma-enrolments');
                //Degree
                Route::get('view-degree-enrolments', [AdminManageInstitutionsDegreeEnrolmentsController::class, 'manageEnrolments'])->name('admin-view-degree-enrolments');
                //International
                Route::get('view-international-enrolments', [AdminManageInstitutionsInternationalEnrolmentsController::class, 'manageEnrolments'])->name('admin-view-international-enrolments');
            });

            // Learners
            Route::prefix('manage-learners')->middleware([RedirectIfNoPermission::class.':learners-view'])->group(function () {
                //Pre-primary
                Route::get('view-pre-primary-learners', [AdminManageLearnersPrePrimaryController::class, 'prePrimaryLearners'])->name('admin-view-preprimary-learners');
                Route::get('view-preprimary-learner-profile/{lin}', [AdminManageLearnersPrePrimaryController::class, 'viewSinglePrePrimaryLearnerProfile'])->name('admin-learner-view-pre-primary');

                //Primary
                Route::get('view-primary-learners', [AdminManageLearnersPrimaryController::class, 'primaryLearners'])->name('admin-view-primary-learners');
                Route::get('view-primary-learner-profile/{lin}', [AdminManageLearnersPrimaryController::class, 'viewSinglePrimaryLearnerProfile'])->name('admin-learner-view-primary');

                //Secondary
                Route::get('view-secondary-learners', [AdminManageLearnersSecondaryController::class, 'secondaryLearners'])->name('admin-view-secondary-learners');
                Route::get('view-secondary-learner-profile/{lin}', [AdminManageLearnersSecondaryController::class, 'viewSingleSecondaryLearnerProfile'])->name('admin-learner-view-secondary');

                //Certificate
                Route::get('view-certificate-learners', [AdminManageLearnersCertificateController::class, 'certificateLearners'])->name('admin-view-certificate-learners');
                Route::get('view-certificate-learner-profile/{lin}', [AdminManageLearnersCertificateController::class, 'viewSingleCertificateLearnerProfile'])->name('admin-learner-view-certificate');

                //Diploma
                Route::get('view-diploma-learners', [AdminManageLearnersDiplomaController::class, 'diplomaLearners'])->name('admin-view-diploma-learners');
                Route::get('view-diploma-learner-profile/{lin}', [AdminManageLearnersDiplomaController::class, 'viewSingleDiplomaLearnerProfile'])->name('admin-learner-view-diploma');

                //Degree
                Route::get('view-degree-learners', [AdminManageLearnersDegreeController::class, 'degreeLearners'])->name('admin-view-degree-learners');
                Route::get('view-degree-learner-profile/{lin}', [AdminManageLearnersDegreeController::class, 'viewSingleDegreeLearnerProfile'])->name('admin-learner-view-degree');

                //International
                Route::get('view-international-learners', [AdminManageLearnersInternationalController::class, 'internationalLearners'])->name('admin-view-international-learners');
                Route::get('view-international-learner-profile/{lin}', [AdminManageLearnersInternationalController::class, 'viewSingleInternationalLearnerProfile'])->name('admin-learner-view-international');
            });

            // Human Resource
            Route::prefix('manage-human-resource')->middleware([RedirectIfNoPermission::class.':teachers-view|non-teaching-staff-view'])->group(function () {
                //transfers
                Route::prefix('transfers')->middleware([RedirectIfNoPermission::class.':teachers-transfers-view|non-teaching-staff-transfers-view'])->name('admin-hr-transfers.')->group(function () {
                    // teachers
                    Route::prefix('teachers')->name('teachers.')->group(function () {
                        Route::get('{primary}', [AdminTeacherTransferController::class, 'index'])->name('primary');
                        Route::get('{secondary}', [AdminTeacherTransferController::class, 'index'])->name('secondary');
                        Route::get('{certificate}', [AdminTeacherTransferController::class, 'index'])->name('certificate');
                        Route::get('{diploma}', [AdminTeacherTransferController::class, 'index'])->name('diploma');

                        Route::get('{level}/create', [AdminTeacherTransferController::class, 'create'])->name('create');
                    });

                    // non teaching staff
                    Route::prefix('non-teaching-staff')->name('non-teaching-staff.')->group(function () {
                        Route::get('{certificate}', [AdminNonTeachingStaffTransferController::class, 'index'])->name('certificate');
                        Route::get('{diploma}', [AdminNonTeachingStaffTransferController::class, 'index'])->name('diploma');

                        Route::get('{level}/create', [AdminNonTeachingStaffTransferController::class, 'create'])->name('create');
                    });
                });

                //postings
                Route::prefix('postings')->middleware([RedirectIfNoPermission::class.':teachers-postings-view|non-teaching-staff-postings-view'])->name('admin-hr-postings.')->group(function () {
                    // teachers
                    Route::prefix('teachers')->name('teachers.')->group(function () {
                        Route::get('{primary}', [AdminTeacherPostingController::class, 'index'])->name('primary');
                        Route::get('{secondary}', [AdminTeacherPostingController::class, 'index'])->name('secondary');
                        Route::get('{certificate}', [AdminTeacherPostingController::class, 'index'])->name('certificate');
                        Route::get('{diploma}', [AdminTeacherPostingController::class, 'index'])->name('diploma');
                    });

                    // non teaching staff
                    Route::prefix('non-teaching-staff')->name('non-teaching-staff.')->group(function () {
                        Route::get('{certificate}', [AdminNonTeachingStaffPostingController::class, 'index'])->name('certificate');
                        Route::get('{diploma}', [AdminNonTeachingStaffPostingController::class, 'index'])->name('diploma');
                    });
                });

                // Pre-primary
                // Caregivers
                Route::get('view-pre-primary-teaching-staff', [AdminManageTeachingStaffController::class, 'prePrimaryTeachingStaff'])->name('admin-view-pre-primary-teaching-staff');
                Route::get('filter-pre-primary-teaching-staff', [AdminManageTeachingStaffController::class, 'searchPrePrimaryTeachingStaff'])->name('admin-search-pre-primary-teaching-staff');
                Route::get('view-pre-primary-teaching-staff-profile/{hrin}', [AdminManageTeachingStaffController::class, 'viewSinglePrePrimaryTeachingStaffProfile'])->name('admin-view-pre-primary-teaching-staff-profile');
                // Non teaching staff
                Route::get('view-pre-primary-non-teaching-staff', [AdminManageSupportStaffController::class, 'prePrimaryNonTeachingStaff'])->name('admin-view-pre-primary-non-teaching-staff');
                Route::get('filter-pre-primary-non-teaching-staff', [AdminManageSupportStaffController::class, 'searchPrePrimaryNonTeachingStaff'])->name('admin-search-pre-primary-non-teaching-staff');
                Route::get('view-pre-primary-non-teaching-staff-profile/{hrin}', [AdminManageSupportStaffController::class, 'viewSinglePrePrimaryNonTeachingStaffProfile'])->name('admin-view-pre-primary-non-teaching-staff-profile');

                //Primary
                // Teachers
                Route::get('view-primary-teaching-staff', [AdminManageTeachingStaffController::class, 'primaryTeachingStaff'])->name('admin-view-primary-teaching-staff');
                Route::get('filter-primary-teaching-staff', [AdminManageTeachingStaffController::class, 'searchPrimaryTeachingStaff'])->name('admin-search-primary-teaching-staff');
                Route::get('view-primary-teaching-staff-profile/{hrin}', [AdminManageTeachingStaffController::class, 'viewSinglePrimaryTeachingStaffProfile'])->name('admin-view-primary-teaching-staff-profile');
                // Non teaching staff
                Route::get('view-primary-non-teaching-staff', [AdminManageSupportStaffController::class, 'primaryNonTeachingStaff'])->name('admin-view-primary-non-teaching-staff');
                Route::get('filter-primary-non-teaching-staff', [AdminManageSupportStaffController::class, 'searchPrimaryNonTeachingStaff'])->name('admin-search-primary-non-teaching-staff');
                Route::get('view-primary-non-teaching-staff-profile/{hrin}', [AdminManageSupportStaffController::class, 'viewSinglePrimaryNonTeachingStaffProfile'])->name('admin-view-primary-non-teaching-staff-profile');

                //Secondary
                // Teachers
                Route::get('view-secondary-teaching-staff', [AdminManageTeachingStaffController::class, 'secondaryTeachingStaff'])->name('admin-view-secondary-teaching-staff');
                Route::get('filter-secondary-teaching-staff', [AdminManageTeachingStaffController::class, 'searchSecondaryTeachingStaff'])->name('admin-search-secondary-teaching-staff');
                Route::get('view-secondary-teaching-staff-profile/{hrin}', [AdminManageTeachingStaffController::class, 'viewSingleSecondaryTeachingStaffProfile'])->name('admin-view-secondary-teaching-staff-profile');
                // Non teaching staff
                Route::get('view-secondary-non-teaching-staff', [AdminManageSupportStaffController::class, 'secondaryNonTeachingStaff'])->name('admin-view-secondary-non-teaching-staff');
                Route::get('filter-secondary-non-teaching-staff', [AdminManageSupportStaffController::class, 'searchSecondaryNonTeachingStaff'])->name('admin-search-secondary-non-teaching-staff');
                Route::get('view-secondary-non-teaching-staff-profile/{hrin}', [AdminManageSupportStaffController::class, 'viewSingleSecondaryNonTeachingStaffProfile'])->name('admin-view-secondary-non-teaching-staff-profile');

                //Certificate
                // Tutors
                Route::get('view-certificate-teaching-staff', [AdminManageTeachingStaffController::class, 'certificateTeachingStaff'])->name('admin-view-certificate-teaching-staff');
                Route::get('filter-certificate-teaching-staff', [AdminManageTeachingStaffController::class, 'searchCertificateTeachingStaff'])->name('admin-search-certificate-teaching-staff');
                Route::get('view-certificate-teaching-staff-profile/{hrin}', [AdminManageTeachingStaffController::class, 'viewSingleCertificateTeachingStaffProfile'])->name('admin-view-certificate-teaching-staff-profile');
                // Non teaching staff
                Route::get('view-certificate-non-teaching-staff', [AdminManageSupportStaffController::class, 'certificateNonTeachingStaff'])->name('admin-view-certificate-non-teaching-staff');
                Route::get('filter-certificate-non-teaching-staff', [AdminManageSupportStaffController::class, 'searchCertificateNonTeachingStaff'])->name('admin-search-certificate-non-teaching-staff');
                Route::get('view-certificate-non-teaching-staff-profile/{hrin}', [AdminManageSupportStaffController::class, 'viewSingleCertificateNonTeachingStaffProfile'])->name('admin-view-certificate-non-teaching-staff-profile');

                //Diploma
                // Lecturers
                Route::get('view-diploma-teaching-staff', [AdminManageTeachingStaffController::class, 'diplomaTeachingStaff'])->name('admin-view-diploma-teaching-staff');
                Route::get('filter-diploma-teaching-staff', [AdminManageTeachingStaffController::class, 'searchDiplomaTeachingStaff'])->name('admin-search-diploma-teaching-staff');
                Route::get('view-diploma-teaching-staff-profile/{hrin}', [AdminManageTeachingStaffController::class, 'viewSingleDiplomaTeachingStaffProfile'])->name('admin-view-diploma-teaching-staff-profile');
                // Non teaching staff
                Route::get('view-diploma-non-teaching-staff', [AdminManageSupportStaffController::class, 'diplomaNonTeachingStaff'])->name('admin-view-diploma-non-teaching-staff');
                Route::get('filter-diploma-non-teaching-staff', [AdminManageSupportStaffController::class, 'searchDiplomaNonTeachingStaff'])->name('admin-search-diploma-non-teaching-staff');
                Route::get('view-diploma-non-teaching-staff-profile/{hrin}', [AdminManageSupportStaffController::class, 'viewSingleDiplomaNonTeachingStaffProfile'])->name('admin-view-diploma-non-teaching-staff-profile');

                //Degree
                // Lecturers
                Route::get('view-degree-teaching-staff', [AdminManageTeachingStaffController::class, 'degreeTeachingStaff'])->name('admin-view-degree-teaching-staff');
                Route::get('filter-degree-teaching-staff', [AdminManageTeachingStaffController::class, 'searchDegreeTeachingStaff'])->name('admin-search-degree-teaching-staff');
                Route::get('view-degree-teaching-staff-profile/{hrin}', [AdminManageTeachingStaffController::class, 'viewSingleDegreeTeachingStaffProfile'])->name('admin-view-degree-teaching-staff-profile');
                // Non teaching staff
                Route::get('view-degree-non-teaching-staff', [AdminManageSupportStaffController::class, 'degreeNonTeachingStaff'])->name('admin-view-degree-non-teaching-staff');
                Route::get('filter-degree-non-teaching-staff', [AdminManageSupportStaffController::class, 'searchDegreeNonTeachingStaff'])->name('admin-search-degree-non-teaching-staff');
                Route::get('view-degree-non-teaching-staff-profile/{hrin}', [AdminManageSupportStaffController::class, 'viewSingleDegreeNonTeachingStaffProfile'])->name('admin-view-degree-non-teaching-staff-profile');
            });

            // EMIS Notices
            Route::prefix('emis-notices-circulars')->group(function () {
                Route::middleware([RedirectIfNoPermission::class.':notices-create'])->group(function () {
                    Route::get('create-category', [AdminManageEmisNoticesController::class, 'createNoticeCategory'])->name('admin-notices.create-categories');
                    Route::post('store-category', [AdminManageEmisNoticesController::class, 'storeNoticeCategory'])->name('admin-notices.store-category');
                    Route::get('create', [AdminManageEmisNoticesController::class, 'createNotices'])->name('admin-notices.create');
                    Route::post('store', [AdminManageEmisNoticesController::class, 'storeNotice'])->name('admin-notices.store');
                });
                Route::middleware([RedirectIfNoPermission::class.':notices-view'])->group(function () {
                    Route::get('view-categories', [AdminManageEmisNoticesController::class, 'manageNoticeCategories'])->name('admin-notice-categories.manage');
                    Route::get('manage', [AdminManageEmisNoticesController::class, 'manageNotices'])->name('admin-notices.manage');
                    Route::get('view/{id}', [AdminManageEmisNoticesController::class, 'viewSingleNotice'])->name('admin-notice.view-single');
                });
                Route::middleware([RedirectIfNoPermission::class.':notices-edit'])->group(function () {
                    Route::get('edit-category/{id}', [AdminManageEmisNoticesController::class, 'editNoticeCategory'])->name('admin-notices.edit-category');
                    Route::post('update-category/{id}', [AdminManageEmisNoticesController::class, 'updateNoticeCategory'])->name('admin-notices.update-category');
                    Route::get('edit-notice/{id}', [AdminManageEmisNoticesController::class, 'editNotice'])->name('admin-notices.edit-notice');
                    Route::post('update-notice/{id}', [AdminManageEmisNoticesController::class, 'updateNotice'])->name('admin-notices.update-notice');
                });
                Route::get('filter', [AdminManageEmisNoticesController::class, 'filterNotices'])->name('admin-notices.filter');
                Route::get('download-pdf/{id}', [AdminManageEmisNoticesController::class, 'downloadPdf'])->name('admin-notice.download-pdf');
            });

            //Facilities
            Route::prefix('facilities')->middleware([RedirectIfNoPermission::class.':infrastructure-view'])->group(function () {
                Route::get('/pre-primary-infrastructure', [AdminManageInfrastructureController::class, 'prePrimaryInfrastructure'])->name('admin-infrastructure-pre-primary-reports');
                Route::get('/primary-infrastructure', [AdminManageInfrastructureController::class, 'primaryInfrastructure'])->name('admin-infrastructure-primary-reports');
                Route::get('/secondary-infrastructure', [AdminManageInfrastructureController::class, 'secondaryInfrastructure'])->name('admin-infrastructure-secondary-reports');
                Route::get('/certificate-infrastructure', [AdminManageInfrastructureController::class, 'certificateInfrastructure'])->name('admin-infrastructure-certificate-reports');
                Route::get('/diploma-infrastructure', [AdminManageInfrastructureController::class, 'diplomaInfrastructure'])->name('admin-infrastructure-diploma-reports');
                Route::get('/degree-infrastructure', [AdminManageInfrastructureController::class, 'degreeInfrastructure'])->name('admin-infrastructure-degree-reports');
                Route::get('/international-infrastructure', [AdminManageInfrastructureController::class, 'internationalInfrastructure'])->name('admin-infrastructure-international-reports');
                Route::get('/view-school-infrastructure-updates/{id}', [AdminManageInfrastructureController::class, 'index'])->name('admin-view-school-infrastructure-updates');
            });

            // Admin Reports
            Route::prefix('reports')->middleware([RedirectIfNoPermission::class.':reports-view'])->name('admin-reports-')->group(function () {
                Route::get('/', [AdminReportsController::class, 'reports'])->name('general');
                Route::get('learner', [AdminReportsController::class, 'learnerReports'])->name('learners');
                Route::get('human-resource', [AdminReportsController::class, 'hrReports'])->name('hr');
                Route::get('infrastructure', [AdminReportsController::class, 'infrastructureReport'])->name('infrastructure');
                //Promotion reports
                Route::prefix('promotions')->name('promotions-')->group(function () {
                    Route::get('preprimary', [AdminManageInstitutionsPrePrimaryPromotionsController::class, 'index'])->name('preprimary');
                    Route::get('primary', [AdminManageInstitutionsPrimaryPromotionsController::class, 'index'])->name('primary');
                    Route::get('secondary', [AdminManageInstitutionsSecondaryPromotionsController::class, 'index'])->name('secondary');
                    Route::get('certificate', [AdminManageInstitutionsCertificatePromotionsController::class, 'index'])->name('certificate');
                    Route::get('diploma', [AdminManageInstitutionsDiplomaPromotionsController::class, 'index'])->name('diploma');
                    Route::get('degree', [AdminManageInstitutionsDegreePromotionsController::class, 'index'])->name('degree');
                    Route::get('international', [AdminManageInstitutionsInternationalPromotionsController::class, 'index'])->name('international');
                });
            });

            //Projects
            Route::prefix('projects')->group(function () {
                Route::get('create', [AdminManageProjectsController::class, 'create'])->name('admin-create-project')->middleware([RedirectIfNoPermission::class.':projects-me-create']);
                Route::get('view-all', [AdminManageProjectsController::class, 'view'])->name('admin-view-projects')->middleware([RedirectIfNoPermission::class.':projects-me-view']);
                Route::get('edit/{id}', [AdminManageProjectsController::class, 'edit'])->name('admin-edit-project')->middleware([RedirectIfNoPermission::class.':projects-me-edit']);
                Route::get('view-details/{id}', [AdminManageProjectsController::class, 'viewDetails'])->name('admin-view-project-details')->middleware([RedirectIfNoPermission::class.':projects-me-view']);
            });

            // EMIS Publications
            Route::prefix('publications')->group(function () {
                Route::get('/manage', [AdminManagePublicationsController::class, 'managePublications'])->name('admin-publication-manage')->middleware([RedirectIfNoPermission::class.':publications-view']);
                Route::get('filter-publications', [AdminManagePublicationsController::class, 'searchPublications'])->name('admin-publications-filter');
                Route::get('/create', [AdminManagePublicationsController::class, 'create'])->name('admin-publication-create')->middleware([RedirectIfNoPermission::class.':publications-create']);
                Route::post('/store', [AdminManagePublicationsController::class, 'store'])->name('admin-publication-store')->middleware([RedirectIfNoPermission::class.':publications-create']);
                Route::post('update-publication/{id}', [AdminManagePublicationsController::class, 'update'])->name('admin-publication-update')->middleware([RedirectIfNoPermission::class.':publications-edit']);
                Route::get('publish-publication/{id}', [AdminManagePublicationsController::class, 'publish'])->name('admin-publish-document')->middleware([RedirectIfNoPermission::class.':publications-publish']);
                Route::get('unpublish-publication/{id}', [AdminManagePublicationsController::class, 'unPublish'])->name('admin-unpublish-document')->middleware([RedirectIfNoPermission::class.':publications-unpublish']);
                Route::middleware([RedirectIfNoPermission::class.':publications-download'])->group(function () {
                    Route::get('/download-file/{id}', [AdminManagePublicationsController::class, 'downloadFile'])->name('admin-download-pdf-file');
                    Route::get('/export-excel-file', [AdminManagePublicationsController::class, 'exportExcel'])->name('admin-publications-export-excel');
                    Route::get('/export-pdf-file', [AdminManagePublicationsController::class, 'exportPdf'])->name('admin-publications-export-pdf');
                });
            });

            // User manager
            // Route::middleware(['role:emis-super-admin'])->group(function () {
            Route::get('emis-calendar', [AdministrationController::class, 'emisCalendar'])->name('admin.administration.emis-calendar')->middleware([RedirectIfNoPermission::class.':emis-calendars-view']);
            Route::get('emis-returns', [AdministrationController::class, 'emisReturns'])->name('admin.administration.emis-returns')->middleware([RedirectIfNoPermission::class.':emis-returns-view']);
            Route::get('emis-return/new', [AdministrationController::class, 'newEmisReturn'])->name('admin.administration.new-emis-return')->middleware([RedirectIfNoPermission::class.':emis-returns-create']);
            Route::get('emis-return/edit/{return_id}', [AdministrationController::class, 'editEmisReturn'])->name('admin.administration.edit-emis-return')->middleware([RedirectIfNoPermission::class.':emis-returns-edit']);

            Route::prefix('user-manager')->group(function () {
                Route::middleware([RedirectIfNoPermission::class.':users-view'])->group(function () {
                    Route::get('ministry-users', [UserManagerController::class, 'ministryUsers'])->name('admin.user-manager.ministry-users');
                    Route::get('district-users', [UserManagerController::class, 'districtUsers'])->name('admin.user-manager.district-users');
                    Route::get('sub-county-users', [UserManagerController::class, 'subCountyUsers'])->name('admin.user-manager.sub-county-users');
                    Route::get('school-users', [UserManagerController::class, 'institutionUsers'])->name('admin.user-manager.school-users');
                });
                Route::middleware([RedirectIfNoPermission::class.':users-export'])->group(function () {
                    Route::get('export-ministry-users', [UserManagerController::class, 'exportMinistryUsers'])->name('admin.user-manager.export-ministry-users');
                    Route::get('export-district-users', [UserManagerController::class, 'exportDistrictUsers'])->name('admin.user-manager.export-district-users');
                    Route::get('export-sub-county-users', [UserManagerController::class, 'exportSubCountyUsers'])->name('admin.user-manager.export-sub-county-users');
                    Route::get('export-institution-users', [UserManagerController::class, 'exportInstitutionUsers'])->name('admin.user-manager.export-institution-users');
                });

                Route::middleware([RedirectIfNoPermission::class.':roles-view'])->group(function () {
                    Route::get('roles', [UserManagerController::class, 'roles'])->name('admin.user-manager.roles');
                    Route::get('permissions', [AdminPermissionsController::class, 'index'])->name('admin.user-manager.permissions');
                    Route::get('create-permissions', [AdminPermissionsController::class, 'create'])->name('admin.user-manager.permissions-create');
                    Route::post('store-permissions', [AdminPermissionsController::class, 'store'])->name('admin.user-manager.permissions-store');
                });
            });
            // });

            // Settings
            Route::prefix('settings')->name('admin.settings.')->middleware([RedirectIfNoPermission::class.':settings-view'])->group(function () {
                //Route::get('academic-years', [AcademicYearsController::class, 'index'])->name('academic-years');
                Route::get('religions', [ReligionController::class, 'index'])->name('religions');
                Route::get('learner-transfer-reasons', [LearnerTransferReasonController::class, 'index'])->name('learner-transfer-reasons');
                Route::get('learner-flagging-reasons', [LearnerFlaggingReasonsController::class, 'index'])->name('learner-flagging-reasons');
                Route::get('amend-school-details-reasons', [AmendSchoolDetailsReasonsController::class, 'index'])->name('amend-school-details-reasons');
                Route::get('school-suspension-reasons', [SchoolSuspensionReasonController::class, 'index'])->name('school-suspension-reasons');
                Route::get('teacher-professional-qualifications', [TeacherProfessionalQualificationController::class, 'index'])->name('teacher-professional-qualifications');
                Route::get('teacher-responsibilities', [TeacherResponsibilityController::class, 'index'])->name('teacher-responsibilities');
                Route::get('licensing-and-registration-requirements', [LicensingAndRegistrationRequirementController::class, 'index'])->name('licensing-and-registration-requirements');
                Route::get('licensing-school-type-requirements', [SchoolTypeRequirementController::class, 'licensing'])->name('licensing-school-type-requirements');
                Route::get('registration-school-type-requirements', [SchoolTypeRequirementController::class, 'registration'])->name('registration-school-type-requirements');
                Route::get('clusters', [AdminManageClustersController::class, 'index'])->name('clusters');
                Route::get('cluster-view-details/{id}', [AdminManageClustersController::class, 'viewDetails'])->name('cluster-view-details');

                Route::prefix('projects')->name('projects.')->group(function () {
                    Route::get('currencies', [ProjectSettingsController::class, 'currencies'])->name('currencies');
                    Route::get('categories', [ProjectSettingsController::class, 'categories'])->name('categories');
                    Route::get('completion_statuses', [ProjectSettingsController::class, 'completionStatuses'])->name('completion-statuses');
                    Route::get('funders', [ProjectSettingsController::class, 'funders'])->name('funders');
                    Route::get('components', [ProjectSettingsController::class, 'components'])->name('components');
                    Route::get('component_types', [ProjectSettingsController::class, 'componentTypes'])->name('component_types');
                });

                Route::prefix('tickets')->name('tickets.')->group(function () {
                    Route::get('categories', [TicketSettingsController::class, 'categories'])->name('categories');
                    Route::get('sub-categories', [TicketSettingsController::class, 'subCategories'])->name('sub-categories');
                });
            });
            // Utilities
            Route::prefix('utilities')->name('admin.utilities.')->middleware([RedirectIfNoPermission::class.':utilities-view'])->group(function () {
                Route::get('learner-transfers', [AdminManageLearnerTransfersClaimsController::class, 'transfers'])->name('learner-transfers');
                Route::get('learner-claims', [AdminManageLearnerTransfersClaimsController::class, 'claims'])->name('learner-claims');
                Route::get('learner-promotions', [AdminManageLearnerTransfersClaimsController::class, 'promotions'])->name('learner-promotions');
                Route::get('learner-transitions', [AdminManageLearnerTransfersClaimsController::class, 'transitions'])->name('learner-transitions');
            });

            //Learners Data update
            Route::prefix('data-update')->middleware([RedirectIfNoPermission::class.':flagged-data-view'])->group(function () {
                Route::get('preprimary-flagged-learners-for-deleting', [AdminLearnersDataUpdateController::class, 'preprimaryFlaggedLearnersForDeleting'])->name('admin-preprimary-learners-for-deleting');
                Route::get('primary-flagged-learners-for-deleting', [AdminLearnersDataUpdateController::class, 'primaryFlaggedLearnersForDeleting'])->name('admin-primary-learners-for-deleting');
                Route::get('secondary-flagged-learners-for-deleting', [AdminLearnersDataUpdateController::class, 'secondaryFlaggedLearnersForDeleting'])->name('admin-secondary-learners-for-deleting');
                Route::get('certificate-flagged-learners-for-deleting', [AdminLearnersDataUpdateController::class, 'certificateFlaggedLearnersForDeleting'])->name('admin-certificate-learners-for-deleting');
                Route::get('diploma-flagged-learners-for-deleting', [AdminLearnersDataUpdateController::class, 'diplomaFlaggedLearnersForDeleting'])->name('admin-diploma-learners-for-deleting');
                Route::get('degree-flagged-learners-for-deleting', [AdminLearnersDataUpdateController::class, 'degreeFlaggedLearnersForDeleting'])->name('admin-degree-learners-for-deleting');
                Route::get('international-flagged-learners-for-deleting', [AdminLearnersDataUpdateController::class, 'internationalFlaggedLearnersForDeleting'])->name('admin-international-learners-for-deleting');
                Route::get('flagged-learner-view-application/{applicationId}', [AdminLearnersDataUpdateController::class, 'viewApplicationDetails']);

            });

            // Emis Support
            Route::prefix('support')->middleware([RedirectIfNoPermission::class.':tickets-view'])->group(function () {
                Route::get('new-tickets', [AdminManageTicketsController::class, 'newTickets'])->name('admin-support.new-tickets');
                Route::get('filter-tickets', [AdminManageTicketsController::class, 'searchTickets'])->name('admin-support.search-tickets');
                Route::get('all-tickets', [AdminManageTicketsController::class, 'allTickets'])->name('admin-support.all-tickets');
                Route::get('closed-tickets', [AdminManageTicketsController::class, 'closedTickets'])->name('admin-support.closed-tickets');
                Route::get('/view-ticket/{slug}/{ticket_id}', [AdminManageTicketsController::class, 'viewTicket'])->name('admin-support.view-ticket');
                Route::get('/change-ticket-status/{slug}/{ticket_id}/{status}', [AdminManageTicketsController::class, 'changeTicketStatus'])->name('admin-support.change-status')->middleware([RedirectIfNoPermission::class.':tickets-change-status']);
                Route::post('/save-ticket-comment/{ticket_id}', [AdminManageTicketsController::class, 'saveTicketComment'])->name('admin-support.save-comment')->middleware([RedirectIfNoPermission::class.':tickets-reply']);
                //Route::post('/save-ticket-comment-reply/{comment_id}', [AdminManageTicketsController::class, 'saveTicketCommentReply'])->name('admin-support.save-comment-reply');
                Route::get('tickets-export', [AdminManageTicketsController::class, 'exportTickets'])->name('admin-support.export-tickets')->middleware([RedirectIfNoPermission::class.':tickets-export']);
            });

            // User Activity logs
            Route::prefix('activity-logs')->middleware([RedirectIfNoPermission::class.':audit-trail-view'])->group(function () {
                Route::get('admin-users', [AdminManageActivityLogsController::class, 'adminUsers'])->name('admin-activity.admin-users');
                Route::get('filter-admin-users-logs', [AdminManageActivityLogsController::class, 'filterAdminLogs'])->name('admin-activity-filter-logs');

                Route::get('district-users', [AdminManageActivityLogsController::class, 'districtUsers'])->name('admin-activity.district-users');
                Route::get('filter-district-users-logs', [AdminManageActivityLogsController::class, 'filterDistrictLogs'])->name('district-activity-filter-logs');

                Route::get('sub-county-users', [AdminManageActivityLogsController::class, 'subCountyUsers'])->name('admin-activity.sub-county-users');
                Route::get('filter-sub-county-users-logs', [AdminManageActivityLogsController::class, 'filterSubCountyLogs'])->name('sub-county-activity-filter-logs');

                Route::get('school-users', [AdminManageActivityLogsController::class, 'schoolUsers'])->name('admin-activity.school-users');
                Route::get('filter-school-users-logs', [AdminManageActivityLogsController::class, 'filterSchoolLogs'])->name('school-activity-filter-logs');

                Route::get('export-excel', [AdminManageActivityLogsController::class, 'exportExcel'])->name('admin-export-activity-logs-excel')->middleware([RedirectIfNoPermission::class.':audit-trail-export']);

            });

            //Manage Uploaded Excels
            Route::get('uploaded-excels', [DeveloperModeController::class, 'uploadedExcels'])->name('admin-uploaded-excels')->middleware([RedirectIfNoPermission::class.':uploaded-excels-view']);

            // For developers
            Route::middleware(['developer'])->group(function () {
                Route::prefix('developer-mode')->group(function () {
                    Route::get('account-creation-dumps', [DeveloperModeController::class, 'accountCreationDumps'])->name('developer-account-creation-dumps');
                    Route::get('export-user-accounts', [DeveloperModeController::class, 'exportUserAccounts'])->name('developer-export-user-accounts');
                    Route::get('tac', [DeveloperModeController::class, 'tac'])->name('developer-tac');
                    Route::get('export-tac', [DeveloperModeController::class, 'exportTac'])->name('developer-export-tac');
                    Route::get('duplicate-approvals', [DeveloperModeController::class, 'duplicateApprovals'])->name('developer-duplicate-approvals');
                    Route::get('stats', [DeveloperModeController::class, 'stats'])->name('developer-stats');
                    Route::get('email-outbox', [DeveloperModeController::class, 'emailOutbox'])->name('developer-email-outbox');
                });
            });
        });
    });
    //Reset password
    Route::get('forgot-password', [AdminForgotPasswordResetController::class, 'forgotPassword'])->name('admin-forgot-password');
    Route::get('forgot-password/{token}', [AdminForgotPasswordResetController::class, 'forgotPasswordValidate']);
    Route::post('forgot-password-reset', [AdminForgotPasswordResetController::class, 'resetPassword'])->name('admin-forgot-password-reset');
    Route::put('reset-password', [AdminForgotPasswordResetController::class, 'updatePassword'])->name('admin-reset-password');

    // School Change Requests Routes
    Route::prefix('/change-requests')->group(function () {
        // Name Changes
        Route::get('/name/new', [AdminManageFlaggedInstitutionController::class, 'newNameChangeRequests'])->name('admin-new-name-changes');
        Route::get('/name/approved', [AdminManageFlaggedInstitutionController::class, 'approvedNameChangeRequests'])->name('admin-approved-name-changes');
        Route::get('/name/rejected', [AdminManageFlaggedInstitutionController::class, 'rejectedNameChangeRequests'])->name('admin-rejected-name-changes');

        // Ownership Changes
        Route::get('/ownership/new', [AdminManageFlaggedInstitutionController::class, 'newOwnershipChangeRequests'])->name('admin-new-ownership-changes');
        Route::get('/ownership/approved', [AdminManageFlaggedInstitutionController::class, 'approvedOwnershipChangeRequests'])->name('admin-approved-ownership-changes');
        Route::get('/ownership/rejected', [AdminManageFlaggedInstitutionController::class, 'rejectedOwnershipChangeRequests'])->name('admin-rejected-ownership-changes');

        // Actions
        Route::get('/view/{id}', [AdminManageFlaggedInstitutionController::class, 'viewRequestDetails'])->name('admin-view-change-request');
        // Route::post('/approve/{id}', [AdminManageFlaggedInstitutionController::class, 'approveRequest'])->name('admin-approve-change-request');
        // Route::post('/reject/{id}', [AdminManageFlaggedInstitutionController::class, 'rejectRequest'])->name('admin-reject-change-request');
    });
});

//Cluster user
Route::middleware(['cluster', 'adminTwoFactor'])->prefix('emis-support-officer')->name('emis-support-officer-')->group(function () {
    Route::get('dashboard', [ClusterUserController::class, 'dashboard'])->name('user-dashboard');
    Route::get('profile', [ClusterUserController::class, 'profile'])->name('user-profile');

    // User account approvals
    Route::prefix('user-account-approval')->name('user-account-approvals.')->group(function () {
        //Pre-primary
        Route::get('pre-primary-school', [ClusterUserAccountApprovalsController::class, 'prePrimaryUserAccountApproval'])->name('pre-primary-school.index');
        Route::get('pre-primary-school/{approval_id}/approve-confirmation', [ClusterUserAccountApprovalsController::class, 'approvePrePrimarySchoolUserAccountConfirmation'])->name('pre-primary-school.approve-page');
        //        Route::get('pre-primary-school/{approval_id}/approve', [SubCountyRegistrationApprovalsPrePrimaryController::class, 'approvePrePrimarySchoolRegistration'])->name('sub-county.user-account-approvals.pre-primary-school.approve');
        //        Route::get('pre-primary-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsPrePrimaryController::class, 'rejectPrePrimarySchoolRegPage'])->name('sub-county.user-account-approvals.pre-primary-school.reject-page');
        //        Route::get('pre-primary-school/{approval_id}/reject', [SubCountyRegistrationApprovalsPrePrimaryController::class, 'rejectPrePrimarySchoolRegistration'])->name('sub-county.user-account-approvals.pre-primary-school.reject');

        //Primary
        Route::get('primary-school', [ClusterUserAccountApprovalsController::class, 'primaryUserAccountApproval'])->name('primary-school.index');
        Route::get('primary-school/{approval_id}/approve-confirmation', [ClusterUserAccountApprovalsController::class, 'approvePrimarySchoolUserAccountConfirmation'])->name('primary-school.approve-page');
        //        Route::get('primary-school/{approval_id}/approve', [SubCountyRegistrationApprovalsPrimaryController::class, 'approvePrimarySchoolRegistration'])->name('sub-county.user-account-approvals.primary-school.approve');
        //        Route::get('primary-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsPrimaryController::class, 'rejectPrimarySchoolRegPage'])->name('sub-county.user-account-approvals.primary-school.reject-page');
        //        Route::get('primary-school/{approval_id}/reject', [SubCountyRegistrationApprovalsPrimaryController::class, 'rejectPrimarySchoolRegistration'])->name('sub-county.user-account-approvals.primary-school.reject');

        //Secondary
        Route::get('secondary-school', [ClusterUserAccountApprovalsController::class, 'secondaryUserAccountApproval'])->name('secondary-school.index');
        Route::get('secondary-school/{approval_id}/approve-confirmation', [ClusterUserAccountApprovalsController::class, 'approveSecondarySchoolUserAccountConfirmation'])->name('secondary-school.approve-page');
        //        Route::get('secondary-school/{approval_id}/approve', [SubCountyRegistrationApprovalsSecondaryController::class, 'approveSecondarySchoolRegistration'])->name('sub-county.user-account-approvals.secondary-school.approve');
        //        Route::get('secondary-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsSecondaryController::class, 'rejectSecondarySchoolRegPage'])->name('sub-county.user-account-approvals.secondary-school.reject-page');
        //        Route::get('secondary-school/{approval_id}/reject', [SubCountyRegistrationApprovalsSecondaryController::class, 'rejectSecondarySchoolRegistration'])->name('sub-county.user-account-approvals.secondary-school.reject');

        //Certificate
        Route::get('certificate-awarding-school', [ClusterUserAccountApprovalsController::class, 'certificateUserAccountApproval'])->name('certificate-awarding-school.index');
        Route::get('certificate-awarding-school/{approval_id}/approve-confirmation', [ClusterUserAccountApprovalsController::class, 'approveCertificateSchoolUserAccountConfirmation'])->name('certificate-awarding-school.approve-page');
        //        Route::get('certificate-awarding-school/{approval_id}/approve', [SubCountyRegistrationApprovalsCertificateController::class, 'approveCertificateSchoolRegistration'])->name('sub-county.user-account-approvals.certificate-awarding-school.approve');
        //        Route::get('certificate-awarding-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsCertificateController::class, 'rejectCertificateSchoolRegPage'])->name('sub-county.user-account-approvals.certificate-awarding-school.reject-page');
        //        Route::get('certificate-awarding-school/{approval_id}/reject', [SubCountyRegistrationApprovalsCertificateController::class, 'rejectCertificateSchoolRegistration'])->name('sub-county.user-account-approvals.certificate-awarding-school.reject');

        //Diploma
        Route::get('diploma-awarding-school', [ClusterUserAccountApprovalsController::class, 'diplomaUserAccountApproval'])->name('diploma-awarding-school.index');
        Route::get('diploma-awarding-school/{approval_id}/approve-confirmation', [ClusterUserAccountApprovalsController::class, 'approveDiplomaSchoolUserAccountConfirmation'])->name('diploma-awarding-school.approve-page');
        //        Route::get('diploma-awarding-school/{approval_id}/approve', [SubCountyRegistrationApprovalsDiplomaController::class, 'approveDiplomaSchoolRegistration'])->name('sub-county.user-account-approvals.diploma-awarding-school.approve');
        //        Route::get('diploma-awarding-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsDiplomaController::class, 'rejectDiplomaSchoolRegPage'])->name('sub-county.user-account-approvals.diploma-awarding-school.reject-page');
        //        Route::get('diploma-awarding-school/{approval_id}/reject', [SubCountyRegistrationApprovalsDiplomaController::class, 'rejectDiplomaSchoolRegistration'])->name('sub-county.user-account-approvals.diploma-awarding-school.reject');

        //Degree
        Route::get('degree-awarding-school', [ClusterUserAccountApprovalsController::class, 'degreeUserAccountApproval'])->name('degree-awarding-school.index');
        Route::get('degree-awarding-school/{approval_id}/approve-confirmation', [ClusterUserAccountApprovalsController::class, 'approveDegreeSchoolUserAccountConfirmation'])->name('degree-awarding-school.approve-page');
        //        Route::get('degree-awarding-school/{approval_id}/approve', [SubCountyRegistrationApprovalsDegreeController::class, 'approveDegreeSchoolRegistration'])->name('sub-county.user-account-approvals.degree-awarding-school.approve');
        //        Route::get('degree-awarding-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsDegreeController::class, 'rejectDegreeSchoolRegPage'])->name('sub-county.user-account-approvals.degree-awarding-school.reject-page');
        //        Route::get('degree-awarding-school/{approval_id}/reject', [SubCountyRegistrationApprovalsDegreeController::class, 'rejectDegreeSchoolRegistration'])->name('sub-county.user-account-approvals.degree-awarding-school.reject');

        //International
        Route::get('international-school', [ClusterUserAccountApprovalsController::class, 'internationalUserAccountApproval'])->name('international-school.index');
        Route::get('international-school/{approval_id}/approve-confirmation', [ClusterUserAccountApprovalsController::class, 'approveInternationalSchoolUserAccountConfirmation'])->name('international-school.approve-page');
        //        Route::get('international-school/{approval_id}/approve', [SubCountyRegistrationApprovalsInternationalController::class, 'approveInternationalSchoolRegistration'])->name('sub-county.user-account-approvals.international-school.approve');
        //        Route::get('international-school/{approval_id}/reject-page', [SubCountyRegistrationApprovalsInternationalController::class, 'rejectInternationalSchoolRegPage'])->name('sub-county.user-account-approvals.international-school.reject-page');
        //        Route::get('international-school/{approval_id}/reject', [SubCountyRegistrationApprovalsInternationalController::class, 'rejectInternationalSchoolRegistration'])->name('sub-county.user-account-approvals.international-school.reject');
    });

    // EMIS number applications
    Route::prefix('emis-number-applications')->name('emis-number-applications.')->group(function () {
        Route::get('pre-primary-school', [ClusterEmisNumberApplicationController::class, 'prePrimaryIndex'])->name('pre-primary.index');
        Route::get('primary-school', [ClusterEmisNumberApplicationController::class, 'primaryIndex'])->name('primary.index');
        Route::get('secondary-school', [ClusterEmisNumberApplicationController::class, 'secondaryIndex'])->name('secondary.index');
        Route::get('certificate-awarding-institution', [ClusterEmisNumberApplicationController::class, 'certificateIndex'])->name('certificate.index');
        Route::get('diploma-awarding-institution', [ClusterEmisNumberApplicationController::class, 'diplomaIndex'])->name('diploma.index');
        Route::get('degree-awarding-institution', [ClusterEmisNumberApplicationController::class, 'degreeIndex'])->name('degree.index');
        Route::get('international-school', [ClusterEmisNumberApplicationController::class, 'internationalIndex'])->name('international.index');

        Route::get('{referenceNumber}', [ClusterEmisNumberApplicationController::class, 'show'])->name('show');
    });

    // Manage schools
    Route::prefix('manage-institutions')->group(function () {
        // Pre-primary
        Route::get('pre-primary', [ClusterManageInstitutionsPrePrimaryController::class, 'managePrePrimary'])->name('manage-preprimary-schools');
        Route::get('view-pre-primary-institution/{id}', [ClusterManageInstitutionsPrePrimaryController::class, 'viewPrePrimaryInstitution'])->name('view-pre-primary-schools');

        //Primary
        Route::get('primary', [ClusterManageInstitutionsPrimaryController::class, 'managePrimary'])->name('manage-primary-schools');
        Route::get('view-primary-institution/{id}', [ClusterManageInstitutionsPrimaryController::class, 'viewPrimaryInstitution'])->name('view-primary-schools');

        //Secondary
        Route::get('secondary', [ClusterManageInstitutionsSecondaryController::class, 'manageSecondary'])->name('manage-secondary-schools');
        Route::get('view-secondary-institution/{id}', [ClusterManageInstitutionsSecondaryController::class, 'viewSecondaryInstitution'])->name('view-secondary-schools');

        //Certificate
        Route::get('certificate', [ClusterManageInstitutionsCertificateController::class, 'manageCertificate'])->name('manage-certificate-schools');
        Route::get('view-certificate-institution/{id}', [ClusterManageInstitutionsCertificateController::class, 'viewCertificateInstitution'])->name('view-certificate-schools');

        //Diploma
        Route::get('diploma', [ClusterManageInstitutionsDiplomaController::class, 'manageDiploma'])->name('manage-diploma-schools');
        Route::get('view-diploma-institution/{id}', [ClusterManageInstitutionsDiplomaController::class, 'viewDiplomaInstitution'])->name('view-diploma-schools');

        //Degree
        Route::get('degree', [ClusterManageInstitutionsDegreeController::class, 'manageDegree'])->name('manage-degree-schools');
        Route::get('view-degree-institution/{id}', [ClusterManageInstitutionsDegreeController::class, 'viewDegreeInstitution'])->name('view-degree-schools');

        //International
        Route::get('international', [ClusterManageInstitutionsInternationalController::class, 'manageInternational'])->name('manage-international-schools');
        Route::get('view-international-institution/{id}', [ClusterManageInstitutionsInternationalController::class, 'viewInternationalInstitution'])->name('view-international-schools');
    });

    // Manage learners
    Route::prefix('manage-learners')->group(function () {
        //pre-primary learners
        Route::get('manage-pre-primary', [ClusterLearnersController::class, 'managePrePrimaryLearners'])->name('manage-pre-primary-learners');
        Route::get('filter-pre-primary-learners', [ClusterLearnersFilterController::class, 'searchPrePrimaryLearners'])->name('search-pre-primary-learner-filter');
        Route::get('view-preprimary-learner-profile/{lin}', [ClusterLearnersController::class, 'viewSinglePrePrimaryLearnerProfile'])->name('pre-primary-learner-profile-view');
        //primary learners
        Route::get('manage-primary', [ClusterLearnersController::class, 'managePrimaryLearners'])->name('manage-primary-learners');
        Route::get('filter-primary-learners', [ClusterLearnersFilterController::class, 'searchPrimaryLearners'])->name('search-primary-learner-filter');
        Route::get('view-primary-learner-profile/{lin}', [ClusterLearnersController::class, 'viewSinglePrimaryLearnerProfile'])->name('primary-learner-profile-view');

        //secondary learners
        Route::get('manage-secondary', [ClusterLearnersController::class, 'manageSecondaryLearners'])->name('manage-secondary-learners');
        Route::get('filter-secondary-learners', [ClusterLearnersFilterController::class, 'searchSecondaryLearners'])->name('search-secondary-learner-filter');
        Route::get('view-secondary-learner-profile/{lin}', [ClusterLearnersController::class, 'viewSingleSecondaryLearnerProfile'])->name('secondary-learner-profile-view');

        //certificate students
        Route::get('manage-certificate', [ClusterLearnersController::class, 'manageCertificateLearners'])->name('manage-certificate-learners');
        Route::get('filter-certificate-students', [ClusterLearnersFilterController::class, 'searchCertificateLearners'])->name('search-certificate-learner-filter');
        Route::get('view-certificate-student-profile/{lin}', [ClusterLearnersController::class, 'viewSingleCertificateLearnerProfile'])->name('certificate-learner-profile-view');

        //diploma students
        Route::get('manage-diploma', [ClusterLearnersController::class, 'manageDiplomaLearners'])->name('manage-diploma-learners');
        Route::get('filter-diploma-students', [ClusterLearnersFilterController::class, 'searchDiplomaLearners'])->name('search-diploma-learner-filter');
        Route::get('view-diploma-student-profile/{lin}', [ClusterLearnersController::class, 'viewSingleDiplomaLearnerProfile'])->name('diploma-learner-profile-view');

        //degree students
        Route::get('manage-degree', [ClusterLearnersController::class, 'manageDegreeLearners'])->name('manage-degree-learners');
        Route::get('filter-degree-students', [ClusterLearnersFilterController::class, 'searchDegreeLearners'])->name('search-degree-learner-filter');
        Route::get('view-degree-student-profile/{lin}', [ClusterLearnersController::class, 'viewSingleDegreeLearnerProfile'])->name('degree-learner-profile-view');

        //international learners
        Route::get('manage-international', [ClusterLearnersController::class, 'manageInternationalLearners'])->name('manage-international-learners');
        Route::get('filter-international-learners', [ClusterLearnersFilterController::class, 'searchInternationalLearners'])->name('search-international-learner-filter');
        Route::get('view-international-learner-profile/{lin}', [ClusterLearnersController::class, 'viewSingleInternationalLearnerProfile'])->name('international-learner-profile-view');
    });

    //Enrolments stats
    Route::prefix('manage-enrolments')->group(function () {
        //Pre-primary
        Route::get('view-pre-primary-enrolments', [ClusterManageInstitutionsPrePrimaryEnrolmentsController::class, 'manageEnrolments'])->name('view-pre-primary-enrolments');
        //Primary
        Route::get('view-primary-enrolments', [ClusterManageInstitutionsPrimaryEnrolmentsController::class, 'manageEnrolments'])->name('view-primary-enrolments');
        //Secondary
        Route::get('view-secondary-enrolments', [ClusterManageInstitutionsSecondaryEnrolmentsController::class, 'manageEnrolments'])->name('view-secondary-enrolments');
        //Certificate
        Route::get('view-certificate-enrolments', [ClusterManageInstitutionsCertificateEnrolmentsController::class, 'manageEnrolments'])->name('view-certificate-enrolments');
        //Diploma
        Route::get('view-diploma-enrolments', [ClusterManageInstitutionsDiplomaEnrolmentsController::class, 'manageEnrolments'])->name('view-diploma-enrolments');
        //Degree
        Route::get('view-degree-enrolments', [ClusterManageInstitutionsDegreeEnrolmentsController::class, 'manageEnrolments'])->name('view-degree-enrolments');
        //International
        Route::get('view-international-enrolments', [ClusterManageInstitutionsInternationalEnrolmentsController::class, 'manageEnrolments'])->name('view-international-enrolments');
    });

    // Human Resource
    Route::prefix('manage-human-resource')->group(function () {
        // Pre-primary
        // Caregivers
        Route::get('view-preprimary-teaching-staff', [ClusterManageTeachingStaffController::class, 'prePrimaryTeachingStaff'])->name('view-pre-primary-teaching-staff');
        Route::get('filter-preprimary-teaching-staff', [ClusterManageTeachingStaffController::class, 'searchPrePrimaryTeachingStaff'])->name('search-pre-primary-teaching-staff');
        Route::get('view-preprimary-teaching-staff-profile/{hrin}', [ClusterManageTeachingStaffController::class, 'viewSinglePrePrimaryTeachingStaffProfile'])->name('view-pre-primary-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-preprimary-non-teaching-staff', [ClusterManageSupportStaffController::class, 'prePrimaryNonTeachingStaff'])->name('view-pre-primary-non-teaching-staff');
        Route::get('filter-preprimary-non-teaching-staff', [ClusterManageSupportStaffController::class, 'searchPrePrimaryNonTeachingStaff'])->name('search-pre-primary-non-teaching-staff');
        Route::get('view-preprimary-non-teaching-staff-profile/{hrin}', [ClusterManageSupportStaffController::class, 'viewSinglePrePrimaryNonTeachingStaffProfile'])->name('view-pre-primary-non-teaching-staff-profile');

        //Primary
        // Teachers
        Route::get('view-primary-teaching-staff', [ClusterManageTeachingStaffController::class, 'primaryTeachingStaff'])->name('view-primary-teaching-staff');
        Route::get('filter-primary-teaching-staff', [ClusterManageTeachingStaffController::class, 'searchPrimaryTeachingStaff'])->name('search-primary-teaching-staff');
        Route::get('view-primary-teaching-staff-profile/{hrin}', [ClusterManageTeachingStaffController::class, 'viewSinglePrimaryTeachingStaffProfile'])->name('view-primary-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-primary-non-teaching-staff', [ClusterManageSupportStaffController::class, 'primaryNonTeachingStaff'])->name('view-primary-non-teaching-staff');
        Route::get('filter-primary-non-teaching-staff', [ClusterManageSupportStaffController::class, 'searchPrimaryNonTeachingStaff'])->name('search-primary-non-teaching-staff');
        Route::get('view-primary-non-teaching-staff-profile/{hrin}', [ClusterManageSupportStaffController::class, 'viewSinglePrimaryNonTeachingStaffProfile'])->name('view-primary-non-teaching-staff-profile');

        //Secondary
        // Teachers
        Route::get('view-secondary-teaching-staff', [ClusterManageTeachingStaffController::class, 'secondaryTeachingStaff'])->name('view-secondary-teaching-staff');
        Route::get('filter-secondary-teaching-staff', [ClusterManageTeachingStaffController::class, 'searchSecondaryTeachingStaff'])->name('search-secondary-teaching-staff');
        Route::get('view-secondary-teaching-staff-profile/{hrin}', [ClusterManageTeachingStaffController::class, 'viewSingleSecondaryTeachingStaffProfile'])->name('view-secondary-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-secondary-non-teaching-staff', [ClusterManageSupportStaffController::class, 'secondaryNonTeachingStaff'])->name('view-secondary-non-teaching-staff');
        Route::get('filter-secondary-non-teaching-staff', [ClusterManageSupportStaffController::class, 'searchSecondaryNonTeachingStaff'])->name('search-secondary-non-teaching-staff');
        Route::get('view-secondary-non-teaching-staff-profile/{hrin}', [ClusterManageSupportStaffController::class, 'viewSingleSecondaryNonTeachingStaffProfile'])->name('view-secondary-non-teaching-staff-profile');

        //Certificate
        // Tutors
        Route::get('view-certificate-teaching-staff', [ClusterManageTeachingStaffController::class, 'certificateTeachingStaff'])->name('view-certificate-teaching-staff');
        Route::get('filter-certificate-teaching-staff', [ClusterManageTeachingStaffController::class, 'searchCertificateTeachingStaff'])->name('search-certificate-teaching-staff');
        Route::get('view-certificate-teaching-staff-profile/{hrin}', [ClusterManageTeachingStaffController::class, 'viewSingleCertificateTeachingStaffProfile'])->name('view-certificate-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-certificate-non-teaching-staff', [ClusterManageSupportStaffController::class, 'certificateNonTeachingStaff'])->name('view-certificate-non-teaching-staff');
        Route::get('filter-certificate-non-teaching-staff', [ClusterManageSupportStaffController::class, 'searchCertificateNonTeachingStaff'])->name('search-certificate-non-teaching-staff');
        Route::get('view-certificate-non-teaching-staff-profile/{hrin}', [ClusterManageSupportStaffController::class, 'viewSingleCertificateNonTeachingStaffProfile'])->name('view-certificate-non-teaching-staff-profile');

        //Diploma
        // Lecturers
        Route::get('view-diploma-teaching-staff', [ClusterManageTeachingStaffController::class, 'diplomaTeachingStaff'])->name('view-diploma-teaching-staff');
        Route::get('filter-diploma-teaching-staff', [ClusterManageTeachingStaffController::class, 'searchDiplomaTeachingStaff'])->name('search-diploma-teaching-staff');
        Route::get('view-diploma-teaching-staff-profile/{hrin}', [ClusterManageTeachingStaffController::class, 'viewSingleDiplomaTeachingStaffProfile'])->name('view-diploma-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-diploma-non-teaching-staff', [ClusterManageSupportStaffController::class, 'diplomaNonTeachingStaff'])->name('view-diploma-non-teaching-staff');
        Route::get('filter-diploma-non-teaching-staff', [ClusterManageSupportStaffController::class, 'searchDiplomaNonTeachingStaff'])->name('search-diploma-non-teaching-staff');
        Route::get('view-diploma-non-teaching-staff-profile/{hrin}', [ClusterManageSupportStaffController::class, 'viewSingleDiplomaNonTeachingStaffProfile'])->name('view-diploma-non-teaching-staff-profile');

        //Degree
        // Lecturers
        Route::get('view-degree-teaching-staff', [ClusterManageTeachingStaffController::class, 'degreeTeachingStaff'])->name('view-degree-teaching-staff');
        Route::get('filter-degree-teaching-staff', [ClusterManageTeachingStaffController::class, 'searchDegreeTeachingStaff'])->name('search-degree-teaching-staff');
        Route::get('view-degree-teaching-staff-profile/{hrin}', [ClusterManageTeachingStaffController::class, 'viewSingleDegreeTeachingStaffProfile'])->name('view-degree-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-degree-non-teaching-staff', [ClusterManageSupportStaffController::class, 'degreeNonTeachingStaff'])->name('view-degree-non-teaching-staff');
        Route::get('filter-degree-non-teaching-staff', [ClusterManageSupportStaffController::class, 'searchDegreeNonTeachingStaff'])->name('search-degree-non-teaching-staff');
        Route::get('view-degree-non-teaching-staff-profile/{hrin}', [ClusterManageSupportStaffController::class, 'viewSingleDegreeNonTeachingStaffProfile'])->name('view-degree-non-teaching-staff-profile');

        //International Level
        // Teachers
        Route::get('view-international-teaching-staff', [ClusterManageTeachingStaffController::class, 'internationalTeachingStaff'])->name('view-international-teaching-staff');
        Route::get('filter-international-teaching-staff', [ClusterManageTeachingStaffController::class, 'searchInternationalTeachingStaff'])->name('search-international-teaching-staff');
        Route::get('view-international-teaching-staff-profile/{hrin}', [ClusterManageTeachingStaffController::class, 'viewSingleInternationalTeachingStaffProfile'])->name('view-international-teaching-staff-profile');
        // Non teaching staff
        Route::get('view-international-support-staff', [ClusterManageSupportStaffController::class, 'internationalNonTeachingStaff'])->name('view-international-non-teaching-staff');
        Route::get('filter-international-support-staff', [ClusterManageSupportStaffController::class, 'searchInternationalNonTeachingStaff'])->name('search-international-non-teaching-staff');
        Route::get('view-international-support-staff-profile/{hrin}', [ClusterManageSupportStaffController::class, 'viewSingleInternationalNonTeachingStaffProfile'])->name('view-international-non-teaching-staff-profile');
    });

    //Reports
    Route::prefix('reports')->name('reports-')->group(function () {
        //Promotion reports
        Route::prefix('promotions')->name('promotions-')->group(function () {
            Route::get('preprimary', [AdminManageInstitutionsPrePrimaryPromotionsController::class, 'index'])->name('preprimary');
            Route::get('primary', [AdminManageInstitutionsPrimaryPromotionsController::class, 'index'])->name('primary');
            Route::get('secondary', [AdminManageInstitutionsSecondaryPromotionsController::class, 'index'])->name('secondary');
            Route::get('certificate', [AdminManageInstitutionsCertificatePromotionsController::class, 'index'])->name('certificate');
            Route::get('diploma', [AdminManageInstitutionsDiplomaPromotionsController::class, 'index'])->name('diploma');
            Route::get('degree', [AdminManageInstitutionsDegreePromotionsController::class, 'index'])->name('degree');
            Route::get('international', [AdminManageInstitutionsInternationalPromotionsController::class, 'index'])->name('international');
        });
    });

    //Learners Data update
    Route::prefix('data-update')->group(function () {
        Route::get('preprimary-flagged-learners-for-deleting', [LearnersEsoDataUpdateController::class, 'preprimaryFlaggedLearnersForDeleting'])->name('preprimary-learners-for-deleting');
        Route::get('primary-flagged-learners-for-deleting', [LearnersEsoDataUpdateController::class, 'primaryFlaggedLearnersForDeleting'])->name('primary-learners-for-deleting');
        Route::get('secondary-flagged-learners-for-deleting', [LearnersEsoDataUpdateController::class, 'secondaryFlaggedLearnersForDeleting'])->name('secondary-learners-for-deleting');
        Route::get('certificate-flagged-learners-for-deleting', [LearnersEsoDataUpdateController::class, 'certificateFlaggedLearnersForDeleting'])->name('certificate-learners-for-deleting');
        Route::get('diploma-flagged-learners-for-deleting', [LearnersEsoDataUpdateController::class, 'diplomaFlaggedLearnersForDeleting'])->name('diploma-learners-for-deleting');
        Route::get('degree-flagged-learners-for-deleting', [LearnersEsoDataUpdateController::class, 'degreeFlaggedLearnersForDeleting'])->name('degree-learners-for-deleting');
        Route::get('international-flagged-learners-for-deleting', [LearnersEsoDataUpdateController::class, 'internationalFlaggedLearnersForDeleting'])->name('international-learners-for-deleting');
        Route::get('flagged-learner-view-application/{applicationId}', [LearnersEsoDataUpdateController::class, 'viewApplicationDetails']);

    });

    Route::prefix('regional-cluster')->group(function () {
        Route::get('manage', [ClusterRegionalManagerController::class, 'index'])->name('regional-cluster-manage');
        Route::get('login-as/{cluster_id}', [ClusterRegionalManagerController::class, 'loginAs'])->name('login-as');
    });

    // EMIS Notices
    Route::prefix('emis-notices')->group(function () {
        Route::get('manage', [ClusterUserNoticesController::class, 'manageNotices'])->name('notices.manage');
        Route::get('filter', [ClusterUserNoticesController::class, 'filterNotices'])->name('notices.filter');
        Route::get('view/{id}', [ClusterUserNoticesController::class, 'viewSingleNotice'])->name('notice.view-single');
        Route::get('download-pdf/{id}', [ClusterUserNoticesController::class, 'downloadPdf'])->name('notice.download-pdf');
    });

    //Publications
    Route::prefix('publications')->group(function () {
        Route::get('/manage', [ClusterUserPublicationsController::class, 'managePublications'])->name('publication-manage');
        Route::get('/download-file/{id}', [ClusterUserPublicationsController::class, 'downloadFile'])->name('download-pdf-file');
    });

});
Route::middleware('auth')->group(function () {
    Route::get('/academic-year/change', [AcademicYearController::class, 'changeAcademicYear'])->name('academic-year.change');
});
Route::get('test-nira/{nin?}', [NiraApiController::class, 'getPerson']);

Route::get('php-info', function () {
    phpinfo();
})->name('php-info');

/*
Route::get('maintainance', function () {
    return view('errors.maintainance');
});
*/

// System Settings: Inactivity Timeout
// GET: Show the inactivity timeout settings page
// POST: Update the inactivity timeout value in .env
Route::get('admin/system-settings/inactivity-timeout', [SystemSettingsController::class, 'showInactivityTimeout'])->name('admin.inactivity-timeout');
Route::post('admin/system-settings/inactivity-timeout', [SystemSettingsController::class, 'updateInactivityTimeout'])->name('admin.inactivity-timeout.update');

