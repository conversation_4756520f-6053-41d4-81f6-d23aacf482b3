<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="js">

<head>
    <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-Q0CRXX3H2R"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-Q0CRXX3H2R');
</script>
	<meta charset="utf-8">
	<meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="refresh" content="{{ config('session.lifetime') * 60 }}">
    <meta name="author" content="Softnio">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta name="description" content="Education Information Management System.">
	<!-- Fav Icon  -->
	<link rel="shortcut icon" href="{{ asset('images/favicon.png') }}">
	<!-- Page Title  -->
	<title>@yield('title') - DEMIS</title>
	<!-- StyleSheets  -->
    @include('core::includes.assets.dash-lite')
	<link id="skin-default" rel="stylesheet" href="{{ asset('assets/css/skins/theme-egyptian.css?ver=3.3.0') }}">
	<link rel="stylesheet" href="{{ asset('assets/css/libs/fontawesome-icons.css?ver=3.3.0') }}">
	<link rel="stylesheet" href="{{ asset('assets/css/custom-other.css?ver=3') }}">
	<!-- intl-tel-input CSS -->
    {{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.min.css"/> --}}
	@yield('styles')
	<style>
		.logo-img {
			max-height: 50px !important;
		}
	</style>
</head>

<body class="nk-body bg-lighter npc-default has-sidebar ">
    <div id="demis-app{{ request()->is('emisreturns/survey/*') }}" class="nk-app-root">
		<!-- main @s -->
		<div class="nk-main ">
			<!-- sidebar @s -->
			@include('core::institution.layouts.sidebar')
			<!-- sidebar @e -->
			<!-- wrap @s -->
			<div class="nk-wrap ">
				<!-- main header @s -->
				@include('core::institution.layouts.header')
				<!-- main header @e -->
				<!-- content @s -->
				<div class="nk-content">
					<div class="container-fluid">
						<div class="nk-content-inner">
							<div class="nk-content-body">
								@yield('content')

				@if(request()->is('emisreturns/survey/*'))
					@php
					$survey_id = request()->route('survey_id');
                    $section_id = request()->route('section_id');
					@endphp
					@livewire('emis-returns.survey-container', ['survey_id' => $survey_id, 'section_id'=>$section_id])
				@endif

							</div>
						</div>
					</div>
				</div>
				<!-- content @e -->
				<!-- footer @s -->
				@include('core::institution.layouts.footer')
				<!-- footer @e -->
			</div>
			<!-- wrap @e -->
		</div>
		<!-- main @e -->
       {{--   password --}}
        @if(auth()->user()->is_temporary_password_yn)
            <change-temporary-password temporary-password="{{ auth()->user()->is_temporary_password_yn }}"></change-temporary-password>
        @else
            @learnerSummaryForm
            <learner-summary-form></learner-summary-form>
            @endlearnerSummaryForm
        @endif

	</div>
	<!-- app-root @e -->
	<!-- JavaScript -->
    @yield('ticket-scripts')
	<script src="{{ asset('assets/js/bundle.js?ver=3.3.0') }}"></script>
	<script src="{{ asset('assets/js/scripts.js?ver=3.3.0') }}"></script>
	<script src="{{ asset('assets/js/libs/jquery.blockUI.js') }}"></script>

    @yield('lr-scripts')
    @yield('scripts')
	@yield('text-editor')


    @vite(['Modules/EmisReturns/resources/assets/js/app.js', 'Modules/EmisReturns/resources/assets/css/style.css'])
	@vite(['Modules/LearnerManagement/resources/assets/js/app.js'])
	@vite(['Modules/HumanResource/resources/assets/sass/app.scss', 'Modules/HumanResource/resources/assets/js/app.js'])

	@if (!request()->is('institution/learners/performance*'))
    @vite('Modules/Core/resources/assets/js/app.js')
    @endif

    <script>
        $("#logout").click(()=>{
            $("#logout-form").submit()
        });
    </script>
	<script>
		$.ajaxSetup({
			headers: {
				'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
			}
		});
	</script>
    @stack('scripts')
<!-- intl-tel-input JS -->
{{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script> --}}
</body>

</html>
