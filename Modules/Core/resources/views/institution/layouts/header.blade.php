@if(auth()->user()->school_type)

<div class="nk-header nk-header-fixed bg-dark-dim is-light no-print">
	<div class="container-fluid">
		<div class="nk-header-wrap">
			<div class="nk-menu-trigger d-xl-none ml-n1">
				<a href="#" class="nk-nav-toggle nk-quick-nav-icon" data-target="sidebarMenu"><em class="icon ni ni-menu"></em></a>
			</div>
			<div class="nk-header-brand d-xl-none">
				<a href="{{ route('institution-dashboard') }}" class="logo-link">
					<img class="d-none d-md-inline logo-light logo-img" src="{{ asset('images/logo.png') }}" srcset="{{ asset('images/logo2x.png') }} 2x" alt="logo">
					<img class="d-none d-md-inline logo-dark logo-img" src="{{ asset('images/logo-dark.png') }}" srcset="{{ asset('images/logo-dark2x.png') }} 2x" alt="logo-dark">
                    <img class="d-md-none align-self-center logo-dark" style="height: 25px" src="{{ asset('images/logo-small.png') }}" srcset="{{ asset('images/logo-small.png') }} 2x" alt="logo-dark">
				</a>
			</div><!-- .nk-header-brand -->
			<div class="nk-header-news d-none d-xl-block">
				<div class="nk-header-news d-none d-xl-block">
                    <latest-notice></latest-notice>
				</div>
			</div><!-- .nk-header-news -->
			<div class="nk-header-tools">
				<ul class="nk-quick-nav">
                    @if(auth()->user()->school_type->name === 'preprimary' ||
                        auth()->user()->school_type->name === 'primary' ||
                        auth()->user()->school_type->name === 'secondary' ||
                        auth()->user()->school_type->name === 'certificate' ||
                        auth()->user()->school_type->name === 'diploma' ||
                        auth()->user()->school_type->name === 'degree' ||
                        auth()->user()->school_type->name === 'international')
                        {{-- <school-academic-years></school-academic-years> --}}
                        @include('core::includes/school-academic-years')
                        <li class="dropdown user-dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                @if(auth()->user()->hasRole('institution-admin'))
                                <div class="user-toggle">
                                    <div class="user-avatar bg-secondary">
                                        <span>{{ auth()->user()->school->initials ?? "" }}</span>
                                    </div>
                                    <div class="user-info d-none d-md-block">
                                        <div class="user-status text-dark">Institution Account</div>
                                        <div class="user-name dropdown-indicator">
                                            {{ Str::limit(auth()->user()->school->name,  $limit = 15, $end = '...') }}
                                        </div>
                                    </div>
                                </div>
                                @elseif(auth()->user()->hasRole('institution-user'))
                                <div class="user-toggle">
                                    <div class="user-avatar bg-secondary">
                                        <span>{{ auth()->user()->school_contact->person->initials ?? "" }}</span>
                                    </div>
                                    <div class="user-info d-none d-md-block">
                                        <div class="user-status text-dark">
                                            Contact Person Account
                                        </div>
                                        <div class="user-name dropdown-indicator">
                                            {{ Str::limit(auth()->user()->school_contact->person->full_name,  $limit = 15, $end = '...') }}
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </a>
                            <div class="dropdown-menu dropdown-menu-md dropdown-menu-right dropdown-menu-s1">
                                <div class="dropdown-inner user-card-wrap bg-lighter d-none d-md-block">
                                    @if(auth()->user()->hasRole('institution-admin'))
                                    <div class="user-card">
                                        <div class="user-avatar bg-secondary">
                                            <span>{{ auth()->user()->school->initials ?? "" }}</span>
                                        </div>
                                        <div class="user-info">
                                            <span class="lead-text">{{ auth()->user()->school->name ?? "" }}</span>
                                            <span class="sub-text">{{ auth()->user()->school->email ?? "" }}</span>
                                        </div>
                                    </div>
                                    @elseif(auth()->user()->hasRole('institution-user'))
                                    <div class="user-card">
                                        <div class="user-avatar bg-secondary">
                                            <span>{{ auth()->user()->school_contact->person->initials ?? "" }}</span>
                                        </div>
                                        <div class="user-info">
                                            <span class="lead-text">{{ auth()->user()->school_contact->person->full_name ?? "" }}</span>
                                            <span class="sub-text">{{ auth()->user()->school_contact->email ?? "" }}</span>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                                <div class="dropdown-inner">
                                    <ul class="link-list">
                                        <li><a href="{{ route('institution-profile-info') }}"><em class="icon ni ni-user-alt"></em><span>View Profile</span></a></li>
                                        <li><a href="#"><em class="icon ni ni-setting-alt"></em><span>Account Setting</span></a></li>
                                        <li><a href="#"><em class="icon ni ni-activity-alt"></em><span>Login Activity</span></a></li>
                                        <li><a class="dark-switch" href="#"><em class="icon ni ni-moon"></em><span>Dark Mode</span></a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-inner">
                                    <ul class="link-list">
                                        <li>
                                            <span class="link link-primary" id="logout" style="cursor: pointer;">
                                               <a href="{{ route('institution-logout-user') }}"><em class="icon ni ni-signout"></em><span>Sign out</span></a>
                                            </span>
                                        </li>
{{--                                        <form id="logout-form" method="POST" action="{{ route('institution-logout-user') }}">@csrf</form>--}}
                                    </ul>
                                </div>
                            </div>
                        </li><!-- .dropdown -->
{{--                        <li class="dropdown notification-dropdown mr-n1">--}}
{{--                            <a href="#" class="dropdown-toggle nk-quick-nav-icon" data-toggle="dropdown">--}}
{{--                                <div class="icon-status icon-status-info"><em class="icon ni ni-bell"></em></div>--}}
{{--                            </a>--}}
{{--                            <div class="dropdown-menu dropdown-menu-xl dropdown-menu-right dropdown-menu-s1">--}}
{{--                                <div class="dropdown-head">--}}
{{--                                    <span class="sub-title nk-dropdown-title">Notifications</span>--}}
{{--                                    <a href="#">Mark All as Read</a>--}}
{{--                                </div>--}}
{{--                                <div class="dropdown-body">--}}
{{--                                    <div class="nk-notification">--}}
{{--                                        <div class="nk-notification-item dropdown-inner">--}}
{{--                                            <div class="nk-notification-icon">--}}
{{--                                                <em class="icon icon-circle bg-warning-dim ni ni-curve-down-right"></em>--}}
{{--                                            </div>--}}
{{--                                            <div class="nk-notification-content">--}}
{{--                                                <div class="nk-notification-text">Update your <span>Institution</span> profile</div>--}}
{{--                                                <div class="nk-notification-time">2 hrs ago</div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                        <div class="nk-notification-item dropdown-inner">--}}
{{--                                            <div class="nk-notification-icon">--}}
{{--                                                <em class="icon icon-circle bg-success-dim ni ni-curve-down-left"></em>--}}
{{--                                            </div>--}}
{{--                                            <div class="nk-notification-content">--}}
{{--                                                <div class="nk-notification-text">Update <span>Learners</span> details</div>--}}
{{--                                                <div class="nk-notification-time">2 hrs ago</div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                        <div class="nk-notification-item dropdown-inner">--}}
{{--                                            <div class="nk-notification-icon">--}}
{{--                                                <em class="icon icon-circle bg-warning-dim ni ni-curve-down-right"></em>--}}
{{--                                            </div>--}}
{{--                                            <div class="nk-notification-content">--}}
{{--                                                <div class="nk-notification-text">Your <span>License</span> is about to expire</div>--}}
{{--                                                <div class="nk-notification-time">2 hrs ago</div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                    </div><!-- .nk-notification -->--}}
{{--                                </div><!-- .nk-dropdown-body -->--}}
{{--                                <div class="dropdown-foot center">--}}
{{--                                    <a href="#">View All</a>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </li><!-- .dropdown -->--}}
					@else
                        <li class="dropdown user-dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <div class="user-toggle">
                                    <div class="user-avatar bg-secondary">
                                        <span>TA</span>
                                    </div>
                                    <div class="user-info d-none d-md-block">
                                        <div class="user-status text-dark">Temporary Account</div>
    {{--									<div class="user-name dropdown-indicator">{{ $application->new_owner_first_name." ".$application->new_owner_surname ?? "" }}</div>--}}
                                    </div>
                                </div>
                            </a>
                            <div class="dropdown-menu dropdown-menu-md dropdown-menu-right dropdown-menu-s1">
                                <div class="dropdown-inner user-card-wrap bg-lighter d-none d-md-block">
                                    <div class="user-card">
                                        <div class="user-avatar bg-secondary">
                                            <span>TA</span>
                                        </div>
                                        <div class="user-info">
    {{--										<span class="lead-text">{{ $application->new_owner_first_name." ".$application->new_owner_surname ?? "" }}</span>--}}
                                            <span class="sub-text">{{ auth()->user()->email ?? "" }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="dropdown-inner">
                                    <ul class="link-list">
                                        <li>
                                            <span class="link link-primary" id="logout" style="cursor: pointer;">
                                                <em class="icon ni ni-signout"></em><span>Sign out</span>
                                            </span>
                                        </li>
                                        <form id="logout-form" method="POST" action="{{ route('institution-logout-user') }}">@csrf</form>
                                    </ul>
                                </div>
                            </div>
                        </li><!-- .dropdown -->
					@endif
				</ul><!-- .nk-quick-nav -->
			</div><!-- .nk-header-tools -->
		</div><!-- .nk-header-wrap -->
	</div><!-- .container-fliud -->
</div>
@endif
