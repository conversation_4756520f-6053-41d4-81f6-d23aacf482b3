<?php $url = url()->current(); ?>
@if(auth()->user()->school_type)
<div class="nk-sidebar nk-sidebar-fixed is-theme no-print" data-content="sidebarMenu">
    <div style="background-color: #00879B !important;" class="nk-sidebar-element nk-sidebar-head">
        <div class="nk-sidebar-brand d-flex">
            <a href="{{ route('institution-dashboard') }}" class="logo-link mx-auto nk-sidebar-logo">
                <img class="logo-light logo-img" src="{{ asset('images/logo.png') }}" srcset="{{ asset('images/logo2x.png') }} 2x" alt="logo" style="max-height: 50px">
                <img class="logo-dark logo-img" src="{{ asset('images/logo-dark.png') }}" srcset="{{ asset('images/logo-dark2x.png') }} 2x" alt="logo-dark">
                <img class="logo-small logo-img logo-img-small" src="{{ asset('images/logo-small2x.png') }}" srcset="{{ asset('images/logo-small2x.png') }} 2x" alt="logo-small" style="top: 23.5px;width: 42px;">
            </a>
        </div>
        <div class="nk-menu-trigger">
            <a href="#" class="nk-nav-toggle nk-quick-nav-icon d-xl-none" data-target="sidebarMenu"><em class="icon ni ni-arrow-left"></em></a>
            <a href="#" class="nk-nav-compact nk-quick-nav-icon d-none d-xl-inline-flex" data-target="sidebarMenu"><em class="icon ni ni-menu"></em></a>
        </div>
    </div><!-- .nk-sidebar-element -->
    <div class="nk-sidebar-element">
        <div class="nk-sidebar-content">
            <div class="nk-sidebar-menu" data-simplebar>
                <ul class="nk-menu">
                    {{-- EMIS Dashboard --}}
                    <li class="nk-menu-item">
                        <a href="{{ route('institution-dashboard') }}" class="nk-menu-link">
                            <span class="nk-menu-icon"><em class="icon ni ni-dashboard"></em></span>
                            <span class="nk-menu-text">EMIS Dashboard</span>
                        </a>
                    </li>

                    {{-- EMIS Returns --}}
                    <li class="nk-menu-item has-sub">
                        <a href="#" class="nk-menu-link nk-menu-toggle">
                            <span class="nk-menu-icon"><em class="icon ni ni-upload-cloud"></em></span>
                            <span class="nk-menu-text">EMIS DATA UPLOAD</span>
                        </a>
                        <ul class="nk-menu-sub">
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-emis-returns') }}" class="nk-menu-link"><span class="nk-menu-text">Upload Data</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-emis-return-file-uploads') }}" class="nk-menu-link"><span class="nk-menu-text">Data Upload Status</span></a>
                            </li>
                        </ul>
                    </li><!-- .nk-menu-item -->

                    @php
                        $user = \Illuminate\Support\Facades\Auth::user();
                        // echo var_dump($user->school);exit;
                        $school2 = null;
                        if ($user->hasRole('institution-admin')) {
                            $school2 = $user->school;
                        } elseif ($user->hasRole('institution-user')) {
                            $school2 = $user->school_contact->school;
                        }
                        // exit($school->id);
                        // echo var_dump($school->id);exit;
                        $is_government_owned = $school2 ? ($school2->is_government_owned_yn ? '1' : '2') : null;
                    @endphp
                    {{-- @if(isset($school->id) && $is_government_owned == 2) --}}
{{--                    @if (config('app.env') === 'local' OR  config('app.env') === 'staging')--}}
                        @if(auth()->user()->school_type && auth()->user()->school_type->id <= 3)
                            @if($is_government_owned == 2)
                            <li class="nk-menu-heading">
                                <h6 class="overline-title text-primary-alt">LICENCE &amp; REGISTRATION</h6>
                            </li>
                            <li class="nk-menu-item has-sub {{ navIsRoute('licence-and-registration', 'active current-page') }}">
                                <a href="#" class="nk-menu-link nk-menu-toggle">
                                    <span class="nk-menu-icon"><em class="icon ni ni-award"></em></span>
                                    <span class="nk-menu-text">Licence &amp; Registration</span>
                                </a>
                                <ul class="nk-menu-sub">
                                    <li class="nk-menu-item {{ navIsRoute('licence-and-registration', 'active current-page') }}">
                                        <a href="{{ route('institute_applications') }}" class="nk-menu-link">
                                            <span class="nk-menu-icon"><em class="icon ni ni-list"></em></span>
                                            <span class="nk-menu-text">Applications List</span>
                                        </a>
                                    </li>
                                    <li class="nk-menu-item {{ navIsRoute('licence-and-registration', 'active current-page') }}">
                                        <a href="{{ '/institution/licence-and-registration' }}" class="nk-menu-link">
                                            <span class="nk-menu-icon"><em class="icon ni ni-list-ol"></em></span>
                                            <span class="nk-menu-text">Update / Apply</span>
                                        </a>
                                    </li>
                                </ul><!-- .nk-menu-sub -->
                            </li>
{{--                                <li class="nk-menu-item has-sub {{ navIsRoute('licence-and-registration', 'active current-page') }}">--}}
{{--                                <a href="{{route('institute_applications')}}" class="nk-menu-link">--}}
{{--                                    <span class="nk-menu-icon"><em class="icon ni ni-award"></em></span>--}}
{{--                                    <span class="nk-menu-text">Licence &amp; Registration</span>--}}
{{--                                </a>--}}

{{--                                    <ul class="nk-menu-sub">--}}
{{--                                        <li class="nk-menu-item {{ navIsRoute('r', 'active current-page') }}">--}}
{{--                                            <a href="{{route('institute_applications')}}" class="nk-menu-link">--}}
{{--                                                <span class="nk-menu-text">Applications List</span>--}}
{{--                                            </a>--}}
{{--                                        </li>--}}
{{--                                        <li class="nk-menu-item {{ navIsRoute('licence-and-registration', 'active current-page') }}">--}}
{{--                                            <a href="{{ '/institution/licence-and-registration' }}" class="nk-menu-link">--}}
{{--                                                <span class="nk-menu-text">Update / Apply</span>--}}
{{--                                            </a>--}}
{{--                                        </li>--}}
{{--                                    </ul><!-- .nk-menu-sub -->--}}

{{--                                </li><!-- .nk-menu-item -->--}}
                            @endif
                        @endif
{{--                    @endif--}}
                    <li class="nk-menu-heading">
                        <h6 class="overline-title text-primary-alt">APPROVALS</h6>
                    </li>
                    <!-- Enrolments Approvals -->
                    <li class="nk-menu-item has-sub">
                        <a href="#" class="nk-menu-link nk-menu-toggle" <?php if(preg_match("/learner-pre-registration-applications/i",$url)){ ?> class="nav-link active" style="background-color: #02366B" <?php } ?>>
                            <span class="nk-menu-icon"><em class="icon ni ni-notes"></em></span>
                            <span class="nk-menu-text">Learner Registry</span>
                        </a>
                        <ul class="nk-menu-sub" <?php if(preg_match("/learner-pre-registration-applications/i",$url)){ ?> style="display: block;" <?php } ?>>
                            <li class="nk-menu-item" <?php if(preg_match("/draft/i",$url)){ ?> class="nav-link active" style="background-color: #02366B" <?php } ?>>
                                <a href="{{ route('learner-enrolment-applications.draft') }}" class="nk-menu-link">
                                    <span class="nk-menu-text">Draft Applications</span>
                                </a>
                            </li>
                            <li class="nk-menu-item" <?php if(preg_match("/pending/i",$url)){ ?> class="nav-link active" style="background-color: #02366B" <?php } ?>>
                                <a href="{{ route('learner-enrolment-applications.pending') }}" class="nk-menu-link">
                                    <span class="nk-menu-text">Pending Applications</span>
                                </a>
                            </li>
                            <li class="nk-menu-item" <?php if(preg_match("/approved/i",$url)){ ?> class="nav-link active" style="background-color: #02366B" <?php } ?>>
                                <a href="{{ route('learner-enrolment-applications.approved') }}" class="nk-menu-link">
                                    <span class="nk-menu-text">Approved Applications</span>
                                </a>
                            </li>
                            <li class="nk-menu-item" <?php if(preg_match("/rejected/i",$url)){ ?> class="nav-link active" style="background-color: #02366B" <?php } ?>>
                                <a href="{{ route('learner-enrolment-applications.rejected') }}" class="nk-menu-link">
                                    <span class="nk-menu-text">Rejected Applications</span>
                                </a>
                            </li>
                        </ul><!-- .nk-menu-sub -->
                    </li>
                    <!-- /Enrolments Approvals -->

                    <li class="nk-menu-heading">
                        <h6 class="overline-title text-primary-alt">MANAGE EMIS DATA</h6>
                    </li>
                    <li class="nk-menu-item">
                        <a href="{{ route('institution-profile') }}" class="nk-menu-link">
                            <span class="nk-menu-icon"><em class="icon ni ni-setting"></em></span>
                            <span class="nk-menu-text">My Institution</span>
                        </a>
                    </li><!-- .nk-menu-item -->

                    <li class="nk-menu-item has-sub {{ navIsRoute('learner-profile', 'active current-page') }}">
                        <a href="#" class="nk-menu-link nk-menu-toggle">
                            <span class="nk-menu-icon"><em class="icon ni ni-book-read"></em></span>
                            @if(auth()->user()->school_type && in_array(auth()->user()->school_type->name, ['certificate','diploma','degree']))
                                <span class="nk-menu-text">Students</span>
                            @else
                                <span class="nk-menu-text">Learners</span>
                            @endif
                        </a>
                        <ul class="nk-menu-sub">

                            <!-- added a link for the over view to give a report -->
                            <li class="nk-menu-item {{ navIsRoute('learner-profile', 'active current-page') }}">
                                <a href="{{ route('institution-learners-overview') }}" class="nk-menu-link">
                                    <span class="nk-menu-text">Overview</span>
                                </a>
                            </li>
                            <li class="nk-menu-item {{ navIsRoute('learner-profile', 'active current-page') }}">
                                <a href="{{ route('institution-learners-index') }}" class="nk-menu-link">
                                    @if(auth()->user()->school_type && in_array(auth()->user()->school_type->name, ['certificate','diploma','degree']))
                                        <span class="nk-menu-text">View Students</span>
                                    @else
                                        <span class="nk-menu-text">View Learners</span>
                                    @endif
                                </a>
                            </li>

                            <li class="nk-menu-item has-sub">
                                <a href="#" class="nk-menu-link nk-menu-toggle">
                                    <span class="nk-menu-text">Attendance</span>
                                </a>
                                <ul class="nk-menu-sub">
                                    <li class="nk-menu-item">
                                        <a href="{{ route('learner-numbers') }}" class="nk-menu-link"><span class="nk-menu-text">Attendance Summary</span></a>
                                    </li>
                                    <li class="nk-menu-item">
                                        <a href="{{ route('learner-attendance') }}" class="nk-menu-link"><span class="nk-menu-text">Record Attendance</span></a>
                                    </li>
                                </ul>
                            </li>

                            @if(in_array(auth()->user()->school_type->id, [1, 2, 3]))
                                <li class="nk-menu-item">
                                    <a href="{{ route('learner-performance') }}" class="nk-menu-link"><span class="nk-menu-text">Performance</span></a>
                                </li>
                            @endif
                            @if(auth()->user()->school_type->id <= 6)
                                <li class="nk-menu-item">
                                    <a href="{{ route('learner-expected-enrolment') }}" class="nk-menu-link"><span class="nk-menu-text">Expected Enrolment</span></a>
                                </li>
                            @endif
                            @if(auth()->user()->school_type->id <= 3 || auth()->user()->school_type->id === 7)
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-learners-promotions') }}" class="nk-menu-link"><span class="nk-menu-text">Promotions</span></a>
                                </li>
                            @endif
                            @if(auth()->user()->school_type->id >= 4 && auth()->user()->school_type->id <= 5)
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-learners-enrolments') }}" class="nk-menu-link"><span class="nk-menu-text">Enrolments</span></a>
                                </li>
                            @endif
                            @if(in_array(auth()->user()->school_type->name, ['primary', 'secondary']))
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-learners-transitions') }}" class="nk-menu-link"><span class="nk-menu-text">Transitions</span></a>
                                </li>
                            @endif
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-learners-transfers') }}" class="nk-menu-link"><span class="nk-menu-text">Transfers</span></a>
                            </li>
                            @if(auth()->user()->school_type->id === 3)
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-learners-claims') }}" class="nk-menu-link"><span class="nk-menu-text">Claims</span></a>
                                </li>
                            @endif
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-learner-search-lin') }}" class="nk-menu-link"><span class="nk-menu-text">Search Lin</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-learners-image-upload') }}" class="nk-menu-link"><span class="nk-menu-text">Bulk Image Uploads</span></a>
                            </li>

                            @if(auth()->user()->school_type->name !== 'degree')
{{--                                @if(auth()->user()->school_type->name === 'secondary')--}}
{{--                                    <li class="nk-menu-item">--}}
{{--                                        <a href="{{ route('institution-learners-index-numbers') }}" class="nk-menu-link"><span class="nk-menu-text">Learners Index Numbers</span></a>--}}
{{--                                    </li>--}}
{{--                                @endif--}}
                                <!-- @if (config('app.env') === 'local' OR  config('app.env') === 'staging') -->
                                    @if(auth()->user()->school_type->name === 'primary' || auth()->user()->school_type->name === 'secondary')
                                        <li class="nk-menu-item">
                                            <a href="{{ route('institution-candidates-index-numbers') }}" class="nk-menu-link"><span class="nk-menu-text">Candidates Index Numbers</span></a>
                                        </li>
                                    @endif
                                <!-- @endif -->
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-learners-for-deleting') }}" class="nk-menu-link"><span class="nk-menu-text">Flagged Learners</span></a>
                                </li>

                            @endif

                        </ul><!-- .nk-menu-sub -->
                    </li><!-- .nk-menu-item -->

                    <li class="nk-menu-item">
                        <a href="{{ route('institution-fetch-parents') }}" class="nk-menu-link">
                            <span class="nk-menu-icon"><em class="icon ni ni-users"></em></span>
                            <span class="nk-menu-text">Parents / Guardians</span>
                        </a>
                    </li><!-- .nk-menu-item -->

                    <!-- Staff members -->
                    <li class="nk-menu-item has-sub">
                        <a href="#" class="nk-menu-link nk-menu-toggle">
                            <span class="nk-menu-icon"><em class="icon ni ni-users-fill"></em></span>
                            <span class="nk-menu-text">Human Resource</span>
                        </a>
                        <ul class="nk-menu-sub">
                            @php
                                if(auth()->user()->school_type->name === 'preprimary') {
                                    $teacher =  'Teacher';
                                    $support_staff = 'Support Staff';
                                }
                                elseif(in_array(auth()->user()->school_type->name, ['primary','secondary','international'])) {
                                    $teacher =  'Teacher';
                                    $support_staff = 'Non Teaching Staff';
                                }
                                elseif(in_array(auth()->user()->school_type->name, ['certificate','diploma'])) {
                                    $teacher =  'Tutors / Instructors';
                                    $support_staff = 'Non Teaching Staff';
                                }elseif(auth()->user()->school_type->name === 'degree') {
                                    $teacher =  'Lecturer';
                                    $support_staff = 'Administrative Staff';
                                }
                            @endphp
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-hr-all-teachers') }}" class="nk-menu-link"><span class="nk-menu-text">{{ $teacher }}s</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-hr-non-teaching-staff') }}" class="nk-menu-link"><span class="nk-menu-text">{{ $support_staff }}</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('teaching-staff-attendance') }}" class="nk-menu-link"><span class="nk-menu-text">{{ $teacher }} Attendance</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('attendance-registry') }}" class="nk-menu-link"><span class="nk-menu-text">{{ $teacher }} Attendance Registry</span></a>
                            </li>
                            @if (config('app.env') === 'local' OR  config('app.env') === 'staging')
                                @if(in_array(auth()->user()->school_type->name, ['primary','secondary','certificate','diploma']))
                                    @role('institution-admin')
                                        <li class="nk-menu-item">
                                            <a href="{{ route('institution-hr-transfers-teachers') }}" class="nk-menu-link"><span class="nk-menu-text">{{ $teacher }} Transfers</span></a>
                                        </li>
                                        <li class="nk-menu-item">
                                            <a href="{{ route('institution-hr-postings-teachers') }}" class="nk-menu-link"><span class="nk-menu-text">{{ $teacher }} Postings</span></a>
                                        </li>
                                        @if(in_array(auth()->user()->school_type->name, ['certificate','diploma']))
                                            <li class="nk-menu-item">
                                                <a href="{{ route('institution-hr-transfers-non-teaching-staff') }}" class="nk-menu-link"><span class="nk-menu-text">{{ $support_staff }} Transfers</span></a>
                                            </li>
                                            <li class="nk-menu-item">
                                                <a href="{{ route('institution-hr-postings-non-teaching-staff') }}" class="nk-menu-link"><span class="nk-menu-text">{{ $support_staff }} Postings</span></a>
                                            </li>
                                        @endif
                                    @endrole
                                @endif
                            @endif
                        </ul><!-- .nk-menu-sub -->
                    </li><!-- .nk-menu-item -->

                    <li class="nk-menu-item">
                        <a href="{{ route('institution-infrastructure') }}" class="nk-menu-link">
                            <span class="nk-menu-icon"><em class="icon ni ni-building"></em></span>
                            <span class="nk-menu-text">Infrastructure</span>
                        </a>
                    </li><!-- .nk-menu-item -->

                    @if(auth()->user()->school_type->name === 'preprimary')
                        <li class="nk-menu-item">
                            <a href="{{ route('institution-learning-and-playing-materials') }}" class="nk-menu-link">
                                <span class="nk-menu-icon"><em class="icon ni ni-book"></em></span>
                                <span class="nk-menu-text">Learning &amp; Playing Materials</span>
                            </a>
                        </li><!-- .nk-menu-item -->
                    @endif
                    @if(auth()->user()->school_type->name === 'primary' OR auth()->user()->school_type->name === 'secondary')

                        <li class="nk-menu-item has-sub">
                            <a href="#" class="nk-menu-link nk-menu-toggle" <?php if(preg_match("/instructional-materials/i",$url)){ ?> class="nav-link active" style="background-color: #02366B" <?php } ?>>
                                <span class="nk-menu-icon"><em class="icon ni ni-book"></em></span>
                                <span class="nk-menu-text">Instructional Materials</span>
                            </a>
                            <ul class="nk-menu-sub" <?php if(preg_match("/instructional-materials/i",$url)){ ?> style="display: block;" <?php } ?>>
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-textbooks-manage') }}" class="nk-menu-link">
                                        <span class="nk-menu-text">Textbooks</span>
                                    </a>
                                </li>
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-reference-books-manage') }}" class="nk-menu-link">
                                        <span class="nk-menu-text">Reference Books</span>
                                    </a>
                                </li>
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-sne-kits-manage') }}" class="nk-menu-link">
                                        <span class="nk-menu-text">SNE Kits</span>
                                    </a>
                                </li>
                                @if(auth()->user()->school_type->name === 'secondary')
                                    <li class="nk-menu-item">
                                        <a href="{{ route('institution-lab-equipment') }}" class="nk-menu-link">
                                            <span class="nk-menu-text">Lab Equipment</span>
                                        </a>
                                    </li><!-- .nk-menu-item -->
                                    <li class="nk-menu-item">
                                        <a href="{{ route('institution-reagents-manage') }}" class="nk-menu-link">
                                            <span class="nk-menu-text">Reagents</span>
                                        </a>
                                    </li>
                                @endif
                            </ul><!-- .nk-menu-sub -->
                        </li><!-- .nk-menu-item -->
                    @endif

                    {{-- Facilities --}}
                    <li class="nk-menu-item has-sub">
                        <a href="#" class="nk-menu-link nk-menu-toggle">
                            <span class="nk-menu-icon"><em class="icon ni ni-color-palette"></em></span>
                            <span class="nk-menu-text">Facilities</span>
                        </a>
                        <ul class="nk-menu-sub">
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-facilities-water-source') }}" class="nk-menu-link"><span class="nk-menu-text">Source &amp; Access to Water</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-facilities-hand-washing-facilities') }}" class="nk-menu-link"><span class="nk-menu-text">Handwashing Facilities</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-facilities-garbage-final-disposal') }}" class="nk-menu-link"><span class="nk-menu-text">Garbage Final Disposal</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-facilities-source-of-energy') }}" class="nk-menu-link"><span class="nk-menu-text">Source of Energy</span></a>
                            </li>
                            @if(auth()->user()->school_type->name !== 'preprimary')
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-facilities-ict-facilities') }}" class="nk-menu-link"><span class="nk-menu-text">ICT Facilities</span></a>
                                </li>
                            @endif
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-facilities-other-facilities') }}" class="nk-menu-link"><span class="nk-menu-text">Other Facilities</span></a>
                            </li>
                        </ul>
                    </li><!-- .nk-menu-item -->

                    <!-- pe & sports -->

                    <li class="nk-menu-item has-sub">
                        <a href="#" class="nk-menu-link nk-menu-toggle">
                            <span class="nk-menu-icon"><em class="icon ni ni-network"></em></span>
                            <span class="nk-menu-text">P.E & Sports</span>
                        </a>
                        <ul class="nk-menu-sub">
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-pe-sports-equipment') }}" class="nk-menu-link"><span class="nk-menu-text">Sports Equipment</span></a>
                            </li>
                            @if(auth()->user()->school_type->name !== 'preprimary')
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-pe-sports-facilities') }}" class="nk-menu-link"><span class="nk-menu-text">Sports Facilities</span></a>
                                </li>
                                @if(auth()->user()->school_type->name == "certificate" || auth()->user()->school_type->name == "diploma" || auth()->user()->school_type->name == "degree")
                                    <li class="nk-menu-item">
                                        <a href="{{ route('institution-pe-sports-activities') }}" class="nk-menu-link"><span class="nk-menu-text">Sports Activities</span></a>
                                    </li>
                                @endif
                                @if(auth()->user()->school_type->name !== 'international')
                                    <li class="nk-menu-item">
                                        <a href="{{ route('institution-pe-sports-extra-curricular-activities') }}" class="nk-menu-link"><span class="nk-menu-text">Participation In Extra-Curricular Sports Activities</span></a>
                                    </li>
                                @endif
                            @endif
                        </ul>
                    </li><!-- .nk-menu-item -->

                    @if(auth()->user()->school_type->name === "primary" || auth()->user()->school_type->name === "secondary")
                        <li class="nk-menu-item">
                            <a href="{{ route('institution-pe-practical-skills') }}" class="nk-menu-link">
                                <span class="nk-menu-icon"><em class="icon ni ni-histroy"></em></span>
                                <span class="nk-menu-text">Extra-curricular Activities</span>
                            </a>
                        </li>
                    @endif
                    {{-- Budget & Finance --}}
                    @if(auth()->user()->school_type->name !== 'international')
                        <li class="nk-menu-item has-sub">
                            <a href="#" class="nk-menu-link nk-menu-toggle">
                                <span class="nk-menu-icon"><em class="icon ni ni-coins"></em></span>
                                <span class="nk-menu-text">Finance</span>
                            </a>
                            <ul class="nk-menu-sub">
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-income') }}" class="nk-menu-link"><span class="nk-menu-text">Income</span></a>
                                </li>
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-budgets') }}" class="nk-menu-link"><span class="nk-menu-text">Budgets</span></a>
                                </li>
                                <li class="nk-menu-item">
                                    <a href="{{ route('institution-expenses') }}" class="nk-menu-link"><span class="nk-menu-text">Expenses</span></a>
                                </li>
                            </ul><!-- .nk-menu-sub -->
                        </li><!-- .nk-menu-item -->
                    @endif


                    <li class="nk-menu-heading">
                        <h6 class="overline-title text-primary-alt">Utilities</h6>
                    </li>
                    {{-- Publications --}}
                    <li class="nk-menu-item has-sub">
                        <a href="#" class="nk-menu-link nk-menu-toggle">
                            <span class="nk-menu-icon"><em class="icon ni ni-tile-thumb-fill"></em></span>
                            <span class="nk-menu-text">Publications</span>
                        </a>
                        <ul class="nk-menu-sub">
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-publications-index') }}" class="nk-menu-link"><span class="nk-menu-text">Ministry Publications</span></a>
                            </li>
                        </ul><!-- .nk-menu-sub -->
                    </li><!-- .nk-menu-item -->
                    {{-- EMIS Reports --}}
                    {{--                    <li class="nk-menu-item">--}}
                    {{--                        <a href="{{ route('institution-reports-index') }}" class="nk-menu-link">--}}
                    {{--                            <span class="nk-menu-icon"><em class="icon ni ni-growth-fill"></em></span>--}}
                    {{--                            <span class="nk-menu-text">EMIS Reports</span>--}}
                    {{--                        </a>--}}
                    {{--                    </li><!-- .nk-menu-item -->--}}
                    {{-- EMIS Reports --}}
                    <li class="nk-menu-item">
                        <a href="{{ route('institution-notices.manage') }}" class="nk-menu-link">
                            <span class="nk-menu-icon"><em class="icon ni ni-notes-alt"></em></span>
                            <span class="nk-menu-text">EMIS Notices</span>
                        </a>
                    </li><!-- .nk-menu-item -->
                    @if(auth()->user()->hasRole('institution-admin'))
                        <li class="nk-menu-item">
                            <a href="{{ route('institution-users') }}" class="nk-menu-link">
                                <span class="nk-menu-icon"><em class="icon ni ni-users"></em></span>
                                <span class="nk-menu-text">User Manager</span>
                            </a>
                        </li><!-- .nk-menu-item -->
                    @endif
                    <li class="nk-menu-item">
                        <a href="{{ route('download-centre') }}" class="nk-menu-link">
                            <span class="nk-menu-icon">
                                <em class="icon ni ni-download-cloud">
                                </em>
                            </span>
                            <span class="nk-menu-text">Download Centre</span>
                        </a>
                    </li><!-- .nk-menu-item -->
                    <li class="nk-menu-heading">
                        <h6 class="overline-title text-primary-alt">Help-Center</h6>
                    </li><!-- .nk-menu-heading -->
                    <li class="nk-menu-item has-sub">
                        <a href="#" class="nk-menu-link nk-menu-toggle">
                            <span class="nk-menu-icon"><em class="icon ni ni-ticket"></em></span>
                            <span class="nk-menu-text">EMIS Support</span>
                        </a>
                        <ul class="nk-menu-sub">
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-tickets-create') }}" class="nk-menu-link"><span class="nk-menu-text">Create Ticket</span></a>
                            </li><li class="nk-menu-item">
                                <a href="{{ route('institution-tickets-new') }}" class="nk-menu-link"><span class="nk-menu-text">Open Tickets</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-tickets-closed') }}" class="nk-menu-link"><span class="nk-menu-text">Resolved Tickets</span></a>
                            </li>
                            <li class="nk-menu-item">
                                <a href="{{ route('institution-tickets-all') }}" class="nk-menu-link"><span class="nk-menu-text">All Tickets</span></a>
                            </li>
                        </ul><!-- .nk-menu-sub -->
                    </li><!-- .nk-menu-item -->

                </ul><!-- .nk-menu -->
            </div><!-- .nk-sidebar-menu -->
        </div><!-- .nk-sidebar-content -->
    </div><!-- .nk-sidebar-element -->
</div>
@endif
