@extends('core::admin.layouts.admin_design')

@section('title', 'Inactivity Timeout Settings')

@section('content')
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    @php
                        // Convert seconds to minutes for display/input
                        $timeoutMinutes = round($timeout / 60);
                    @endphp

                    <h3>Inactivity Timeout</h3>
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    <form method="POST" action="{{ route('admin.inactivity-timeout.update') }}" class="mb-3">
                        @csrf
                        <div class="row g-2 align-items-center flex-nowrap">
                            <div class="col-auto">
                                <label for="timeout" class="col-form-label">Timeout (min):</label>
                            </div>
                            <div class="col-auto">
                                <input type="number" min="1" max="1440" class="form-control form-control-sm text-center" id="timeout" name="timeout" value="{{ $timeoutMinutes }}" required style="width: 90px;">
                            </div>
                            <div class="col-auto">
                                <button type="submit" class="btn btn-primary btn-sm">Update</button>
                            </div>
                        </div>
                    </form>
                    <p>
                        Current inactivity timeout is set to: <strong>{{ $timeoutMinutes }}</strong> minutes ({{ $timeout }} seconds).
                    </p>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('project-scripts')
    <script>
        // Pass PHP value to JS
        window.inactivityTimeout = {{ $timeout }};
        // Example: use in your auto-logout script
        // setTimeout(logoutFunction, window.inactivityTimeout * 1000);
    </script>
@stop
