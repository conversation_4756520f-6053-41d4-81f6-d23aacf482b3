<li class="dropdown">
    <button class="{{ $selectedAcademicYear['status']=='archived' ? 'bg-gray' : 'bg-dark-teal'; }} dropdown-toggle btn btn-dim btn-sm text-white" data-toggle="dropdown">
        <em class="d-none d-sm-inline icon ni ni-calender-date"></em>
        <span><span class="d-none d-md-inline"></span>{{ $selectedAcademicYear['name'] }}</span>
        <em style="margin-top: -1.6px;" class="pl-0 pl-md-1 dd-indc icon ni ni-chevron-right"></em>
    </button>
    <div class="dropdown-menu dropdown-menu-right">
    <ul class="link-list-opt no-bdr">
                @foreach ($academicYearData as  $year)
                <li>
                    <a href="/academic-year/change?year={{ $year['id'] }}" class="{{ @$year['selected'] ? 'text-dark-teal': ''}} cursor">
                        <span>{{ $year['name'] }}</span>
                    </a>
                </li>
                @endforeach
        </ul>
    </div>
</li>
