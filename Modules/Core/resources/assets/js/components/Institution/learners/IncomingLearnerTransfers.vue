<template>
    <div class="card-inner-group">
        <div class="card-inner position-relative card-tools-toggle">
            <div class="card-title-group">
                <div class="card-tools">
                    <form @submit.prevent="loadTransfers(1, true)">
                        <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                            <div v-show="schoolTypeIdObj <= 6" style="width: 200px !important" class="form-wrap">
                                <select id="filterIncomingEducationGradeId" class="form-select-sm">
                                    <option value="">ALL CLASSES</option>
                                    <option v-for="education_grade in education_grades" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                </select>
                            </div>
                            <div v-show="schoolTypeIdObj === 7" style="width: 200px !important" class="form-wrap">
                                <select id="filterIncomingLearnerCurriculumId" class="form-select-sm">
                                    <option value="">SELECT CURRICULUM</option>
                                    <option v-for="curriculum in curriculums" :value="curriculum.id">{{ curriculum.name.toUpperCase() }}</option>
                                </select>
                            </div>
                            <div v-show="schoolTypeIdObj === 7" style="width: 200px !important" class="form-wrap">
                                <select id="filterIncomingInterEducationGradeId" class="form-select-sm">
                                    <option value="">SELECT GRADE</option>
                                </select>
                            </div>
                            <div style="width: 180px !important" class="form-wrap">
                                <select id="filterIncomingGender" class="form-select-sm">
                                    <option value="">ALL GENDERS</option>
                                    <option value="M">MALE</option>
                                    <option value="F">FEMALE</option>
                                </select>
                            </div>
                            <div class="form-wrap">
                                <div class="input-group">
                                    <input v-model.trim="filter.name" type="text" class="form-control" placeholder="Learner Name">
                                    <div class="input-group-append">
                                        <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                            <em class="icon ni ni-cross"></em>
                                        </button>
                                        <button class="btn rounded-right bg-dark-teal" type="submit">
                                            <em class="icon ni ni-filter mr-1"></em>Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div><!-- .card-tools -->
            </div><!-- .card-title-group -->
        </div><!-- .card-inner -->

        <div class="card-inner p-0">
            <div class="nk-tb-list nk-tb-ulist is-compact">
                <div class="nk-tb-item nk-tb-head">
                    <div class="nk-tb-col w-25 bg-secondary ucap">
                        <span class="sub-text text-white">Learner</span>
                    </div>
                    <div class="nk-tb-col bg-secondary text-center ucap">
                        <span class="sub-text text-white">Class</span>
                    </div>
                    <div class="nk-tb-col w-25 bg-secondary ucap">
                        <span class="sub-text text-white">Former School</span>
                    </div>
                    <div class="nk-tb-col w-20 bg-secondary ucap">
                        <span class="sub-text text-white">Transfer Reason</span>
                    </div>
                    <div class="nk-tb-col w-15 bg-secondary text-center ucap">
                        <span class="sub-text text-white">Transfer Date</span>
                    </div>
                </div><!-- .nk-tb-item -->
            </div><!-- .nk-tb-list -->
            <div v-if="incoming_transfers.data.length" class="nk-tb-list nk-tb-ulist is-compact">
                <div v-for="enrolment in incoming_transfers.data" class="nk-tb-item">
                    <div class="nk-tb-col w-25">
                        <div class="user-card">
                            <div class="user-avatar">
                                <img :src="enrolment.learner.person.photo_url" style="border-radius: 0" :alt="enrolment.learner.person.full_name">
                            </div>
                            <div class="user-name text-uppercase">
                                <a :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin" class="tb-lead cursor text-dark">{{ enrolment.learner.person.full_name }}</a>
                            </div>
                        </div>
                    </div>
                    <div class="nk-tb-col text-center">
                        <span v-if="schoolTypeIdObj <= 6 && enrolment.education_grade !== null" class="text-dark">{{ enrolment.education_grade.name.toUpperCase() }}</span>
                        <span v-if="schoolTypeIdObj === 7 && enrolment.international_education_grade !== null" class="text-dark">{{ enrolment.international_education_grade.name.toUpperCase() }}</span>
                    </div>
                    <div class="nk-tb-col w-25">
                        <span v-if="enrolment.transfer !== null" class="text-dark">{{ enrolment.transfer.outbound_school.name }}</span>
                        <span v-if="enrolment.transfer !== null" class="font-italic text-muted">({{ enrolment.transfer.outbound_school.district.name.toUpperCase() }})</span>
                    </div>
                    <div class="nk-tb-col w-20">
                        <span v-if="enrolment.transfer !== null" class="text-dark">{{ enrolment.transfer.transfer_reason.name }}</span>
                    </div>
                    <div class="nk-tb-col w-15 text-center">
                        <span v-if="enrolment.transfer !== null" class="text-dark">{{ formatDate(enrolment.transfer.date_created) }}</span>
                    </div>
                </div><!-- .nk-tb-item -->
            </div><!-- .nk-tb-list -->
        </div><!-- .card-inner -->

        <div v-if="!incoming_transfers.data.length" class="card-inner p-0">
            <div class="card-body">
                <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                    <em class="icon ni ni-alert-circle"></em> There are no incoming learner transfers to display at the moment.
                </div>
            </div>
        </div>

        <div v-if="incoming_transfers.data.length" class="card-inner d-flex flex-row justify-content-between">
            <nav>
                <ul class="pagination">
                    <li :class="[incoming_transfers.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                        <a @click="incoming_transfers.current_page > 1 ? loadTransfers(1) : null" :class="[incoming_transfers.current_page === 1 ? '' : 'cursor', 'page-link']">
                            <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                        </a>
                    </li>
                    <li :class="[incoming_transfers.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                        <a @click="incoming_transfers.current_page > 1 ? loadTransfers(incoming_transfers.current_page-1) : null" :class="[incoming_transfers.current_page === 1 ? '' : 'cursor', 'page-link']">
                            <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                        </a>
                    </li>
                    <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                        <a @click="loadTransfers(link.label)" class="page-link cursor" v-html="link.label"></a>
                    </li>
                    <li :class="[incoming_transfers.current_page === incoming_transfers.last_page ? 'disabled' : '', getLinkClasses()]">
                        <a @click="incoming_transfers.current_page < incoming_transfers.last_page ? loadTransfers(incoming_transfers.current_page+1) : null" :class="[incoming_transfers.current_page === incoming_transfers.last_page ? '' : 'cursor', 'page-link']">
                            <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                        </a>
                    </li>
                    <li :class="[incoming_transfers.current_page === incoming_transfers.last_page ? 'disabled' : '', getLinkClasses()]">
                        <a @click="incoming_transfers.current_page < incoming_transfers.last_page ? loadTransfers(incoming_transfers.last_page) : null" :class="[incoming_transfers.current_page === incoming_transfers.last_page ? '' : 'cursor', 'page-link']">
                            <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="d-flex ml-4">
                <span class="align-self-center">
                    Showing <span class="text-primary">{{ incoming_transfers.from }}</span> to <span class="text-primary">{{ incoming_transfers.to }}</span> of <span class="text-primary">{{ incoming_transfers.total }}</span>
                </span>
            </div>
        </div><!-- .card-inner -->

        <!-- Transfer Modal -->
        <div class="modal fade zoom" tabindex="-1" id="incomingTransfersModal">
            <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetTransferProcess()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h5 class="modal-title">Transfer Learner</h5>
                    </div>
                    <div class="modal-body">
                        <notifications ref="notify"></notifications>
                        <div class="alert alert-info alert-icon">
                            <em class="icon ni ni-alert-circle"></em>
                            <h5 class="mb-2"><span class="text-dark-teal">Follow Instructions Below:</span></h5>
                            <h6 v-if="!verify" class="small mb-2"><span class="text-danger">Step 1:</span> Enter LIN and click proceed.</h6>
                            <h6 v-if="verify" class="small mb-2"><span class="text-danger">Step 2:</span> Fill in all necessary details</h6>
                            <h6 v-if="verify"  class="small mb-2"><span class="text-danger">Step 3:</span> Verify Parent's NIN and Click Transfer Learner Button.</h6>
                        </div>
                        <form @submit.prevent="verify ? transferLearner() : getLearnerDetails()">
                            <div v-if="!verify" :class="[!verify ? 'mb-5' : '','row']">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label" for="learner_lin">Learner Identification Number (LIN)</label>
                                        <div class="form-control-wrap">
                                            <input required v-model.trim="learner.lin" type="text" class="text-uppercase form-control bg-primary-dim text-center" placeholder="Enter Learner LIN" id="learner_lin" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="verify" class="row mb-4">
                                <div class="col-12">
                                    <span class="d-block text-dark">LIN: <span class="ucap text-dark-teal">{{ learner.lin }}</span></span>
                                    <span class="d-block text-dark">Learner: <span class="ucap text-dark-teal">{{ learner.person.full_name }}</span></span>
                                    <span class="d-block text-dark">Current Class:
                                        <span v-if="learner.education_grade !== null" class="ucap text-dark-teal">{{ learner.education_grade.name.toUpperCase() }}</span>
                                        <span v-if="learner.international_education_grade !== null" class="ucap text-dark-teal">{{ learner.international_education_grade.name.toUpperCase() }}</span>
                                    </span>
                                    <span class="d-block text-dark">Current School: <span class="ucap text-dark-teal">{{ learner.school.name }}, {{ learner.school.district.name.toUpperCase() }}</span></span>
                                </div>
                            </div>
                            <div v-show="verify" class="row mb-4">
                                <div v-show="verify && schoolTypeIdObj <= 6" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label">Class <span class="text-danger">*</span></label>
                                        <select :required="verify && schoolTypeIdObj <= 6" id="transferEducationGradeId" class="form-select-sm">
                                            <option value="">--SELECT--</option>
                                            <option v-for="grade in education_grades" :value="grade.id">{{ grade.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div v-show="verify" class="col-lg-6">
                                    <div v-show="verify" class="form-group">
                                        <label class="form-label">Transfer Reason <span class="text-danger">*</span></label>
                                        <select :required="verify" id="transferTransferReasonId" class="form-select-sm">
                                            <option value="">--SELECT--</option>
                                            <option v-for="reason in transfer_reasons" :value="reason.id">{{ reason.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div v-show="verify" class="col-12">
                            <h6>Update Parent Details</h6>
                            <div v-show="!transfer.uganda" class="row mt-3">
                                <div class="col-lg-6 mt-lg-0">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label for="learnerParentFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-control-group">
                                            <input :required="!transfer.uganda" v-model.trim="transfer.parent_first_name" id="learnerParentFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mt-lg-0">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label for="learnerParentSurname" class="form-label">Surname <span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-control-group">
                                            <input :required="!transfer.uganda" v-model.trim="transfer.parent_surname" id="learnerParentSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mt-lg-2 mt-3">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label for="learnerParentOtherNames" class="form-label">Other Names</label>
                                        </div>
                                        <div class="form-control-group">
                                            <input v-model.trim="transfer.parent_other_names" id="learnerParentOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                        </div>
                                    </div>
                                </div>

                                <div v-show="verify && !transfer.uganda" class="col-lg-6 mt-lg-2 mt-3">
                                    <div v-show="verify" class="form-group">
                                        <div class="form-label-group">
                                            <label class="form-label">Gender <span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-control-inline custom-radio">
                                                <input type="radio" class="custom-control-input" v-model.number="transfer.parent_gender" value="M" id="learnerParentMale">
                                                <label class="custom-control-label text-uppercase" for="learnerParentMale">Male</label>
                                            </div>
                                            <div class="custom-control custom-control-inline custom-radio">
                                                <input type="radio" class="custom-control-input" v-model.number="transfer.parent_gender" value="F" id="learnerParentFemale">
                                                <label class="custom-control-label text-uppercase" for="learnerParentFemale">Female</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-show="verify" class="row mb-4 mt-2">
                                <div v-show="verify" class="col-lg-6">
                                    <div v-show="verify" class="form-group">
                                        <label class="form-label" for="transferParentRelationship">Parent Relationship <span class="text-danger">*</span></label>
                                        <select :required="verify" id="transferParentRelationship" class="form-select-sm">
                                            <option value="">--SELECT--</option>
                                            <option value="parent">PARENT</option>
                                            <option value="guardian">GUARDIAN</option>
                                        </select>
                                    </div>
                                </div>
                                <div v-show="verify" class="col-lg-6">
                                    <div v-show="verify" class="form-group">
                                        <label for="transferCountryId" class="form-label">Parent Nationality <span class="text-danger">*</span></label>
                                        <select :disabled="nin_verify" :required="verify" id="transferCountryId" class="form-select-sm">
                                            <option value="">--SELECT--</option>
                                            <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div v-show="verify && nin_verify && transfer.uganda" class="row mb-4">
                                <div class="table-responsive">
                                    <table class="table table-sm table-hover">
                                        <tr>
                                            <td rowspan="2" class="align-middle text-center text-uppercase text-dark text-center px-2">
                                                <div class="w-100px mx-auto">
                                                    <img id="transferPersonPhoto" src="@images/default_male.jpg" class="rounded-0" alt="parent photo">
                                                </div>
                                            </td>
                                            <td colspan="2" class="px-2 align-middle text-uppercase text-dark">
                                                <h6 class="overline-title mb-0 text-dark-teal">NAME</h6>
                                                <span class="">{{ nira_person.surname }} {{ nira_person.given_names }} {{ nira_person.maiden_names }} {{ nira_person.previous_surnames }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-uppercase text-dark">
                                                <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                <span class="">{{ nira_person.gender }}</span>
                                            </td>
                                            <td class="px-2 w-75 align-middle text-uppercase text-dark">
                                                <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                <span class="">{{ nira_person.date_of_birth }}</span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div v-show="verify" class="row mb-4">
                                <div v-show="verify" class="col-lg-6">
                                    <div v-if="verify && transfer.uganda" class="form-group">
                                        <div class="form-label-group">
                                            <label for="transferNin" class="form-label">Parent NIN</label>
                                        </div>
                                        <div class="form-control-group">
                                            <div class="input-group">
                                                <input :readonly="nin_verify" :required="transfer.uganda && verify" v-model.trim="transfer.parent_nin" id="transferNin" minlength="14" maxlength="14" type="text" placeholder="eg. CM001122334455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                                <div class="input-group-append">
                                                    <span @click="verifyNIN()" :class="[transfer.parent_nin.length !== 14 || nin_loading ? 'disabled' : '','btn rounded-right text-white bg-dark-teal']">
                                                        <span v-if="nin_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                        <span v-if="nin_loading" class="sr-only">Verifying...</span>
                                                        <span v-if="!nin_loading && !nin_verify" class="">Verify</span>
                                                        <em v-if="!nin_loading && nin_verify" class="icon ni ni-check-circle-fill"></em>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="verify && !transfer.uganda && learner.person.identity_type_id !== 6" class="form-group">
                                        <div class="form-label-group">
                                            <label for="transferPassport" class="form-label">Parent Passport <span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-control-group">
                                            <div class="input-group">
                                                <input :required="!transfer.uganda && verify && learner.person.identity_type_id !== 6" v-model.trim="transfer.parent_passport" id="transferPassport" type="text" placeholder="eg. B0010002" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
<!--                                                <div class="input-group-append">-->
<!--                                                    <span @click="verifyPassport()" :class="[!transfer.id_number.length || nin_loading ? 'disabled' : '','btn rounded-right text-white bg-dark-teal']">-->
<!--                                                        <span v-if="nin_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>-->
<!--                                                        <span v-if="nin_loading" class="align-self-center">Verifying...</span>-->
<!--                                                        <span v-if="nin_loading" class="sr-only">Verifying...</span>-->
<!--                                                        <span v-if="!nin_loading" class="">Verify</span>-->
<!--                                                    </span>-->
<!--                                                </div>-->
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="verify && !transfer.uganda && learner.person.identity_type_id === 6" class="form-group">
                                        <div class="form-label-group">
                                            <label for="transferRefugeeNumber" class="form-label">Parent Refugee Number <span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-control-group">
                                            <div class="input-group">
                                                <input :required="!transfer.uganda && verify && learner.person.identity_type_id === 6" v-model.trim="transfer.parent_refugee_number" id="transferRefugeeNumber" type="text" placeholder="eg. FVA-0738288" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
<!--                                                <div class="input-group-append">-->
<!--                                                    <span @click="verifyRefugeeNumber()" :class="[!transfer.id_number.length || nin_loading ? 'disabled' : '','btn rounded-right text-white bg-dark-teal']">-->
<!--                                                        <span v-if="nin_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>-->
<!--                                                        <span v-if="nin_loading" class="align-self-center">Verifying...</span>-->
<!--                                                        <span v-if="nin_loading" class="sr-only">Verifying...</span>-->
<!--                                                        <span v-if="!nin_loading" class="">Verify</span>-->
<!--                                                    </span>-->
<!--                                                </div>-->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-show="verify" class="col-lg-6">
                                    <div v-show="verify" class="form-group">
                                        <div class="form-label-group">
                                            <label for="parentPhoneOne" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-control-group">
                                            <input :required="verify" v-model="transfer.parent_phone_1" id="parentPhoneOne" maxlength="9" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                                <div v-show="verify" class="col-lg-6 mt-2">
                                    <div v-show="verify" class="form-group">
                                        <div class="form-label-group">
                                            <label for="parentPhoneTwo" class="form-label">Phone Number 2</label>
                                        </div>
                                        <div class="form-control-group">
                                            <input v-model="transfer.parent_phone_2" id="parentPhoneTwo" maxlength="9" type="text" placeholder="Enter Phone Number 2" class="form-control bg-primary-dim" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                                <div v-show="verify" class="col-lg-6 mt-2">
                                    <div v-show="verify" class="form-group">
                                        <div class="form-label-group">
                                            <label for="parentEmail" class="form-label">Email Address</label>
                                        </div>
                                        <div class="form-control-group">
                                            <input v-model="transfer.parent_email" id="parentEmail" type="text" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12 d-flex flex-lg-row justify-content-center">
                                    <button @click="resetTransferProcess()" :disabled="loading" type="button" class="btn btn-dim btn-outline-light me-2">
                                        <span>Clear</span>
                                        <em class="ni ni-cross ml-2"></em>
                                    </button>
                                    <button :disabled="loading || (verify && !nin_verify && transfer.uganda)" type="submit" class="btn bg-dark-teal d-flex">
                                        <span v-if="loading" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                        <span v-if="loading && !verify" class="align-self-center">Fetching Data, Please wait...</span>
                                        <span v-if="loading && !verify" class="sr-only">Fetching Data, Please wait...</span>
                                        <span v-if="!loading && !verify" class="align-self-center">Proceed</span>

                                        <span v-if="loading && verify" class="align-self-center">Transferring, Please wait...</span>
                                        <span v-if="loading && verify" class="sr-only">Transferring, Please wait...</span>
                                        <span v-if="!loading && verify" class="align-self-center">Transfer Learner</span>

                                        <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- /Transfer Modal -->
    </div><!-- .card-inner-group -->
</template>

<script>
import Notifications from "../../Notifications.vue";
import moment from "moment";

export default {
    name: "IncomingLearnerTransfers",
    props: [
        "educationGradesObj",
        "countriesObj",
        "learnerTransferReasonsObj",
        "incomingTransfersObj",
        "schoolTypeIdObj",
        "curriculumsObj"
    ],
    components: {
        Notifications
    },
    data: function () {
        return {
            api_url: '/institutions/learners/transfers',
            select_all_incoming_transfers: false,
            verify: false,
            loading: false,
            filtering: false,
            nin_loading: false,
            nin_verify: false,
            loading_message: '',
            photoDropify: null,
            incoming_transfers: {
                data: [],
                links: [],
                total: 0,
            },
            learner: {
                lin: '',
                current_education_grade_id: '',
                school: {
                    name: '',
                    district: {
                        name: '',
                    },
                },
                education_grade: {
                    name: '',
                },
                person: {
                    full_name: '',
                },
            },
            selected_incoming_transfers: [],
            countries: [],
            education_grades: [],
            curriculums: [],
            transfer_reasons: [],
            filter: {
                education_grade_id: '',
                inter_sch_curriculum_id: '',
                gender: '',
                name: '',
            },
            transfer: {
                transfer_reason_id: '',
                education_grade_id: '',
                country_id: '',
                parent_relationship: '',
                parent_nin: '',
                parent_passport: '',
                parent_refugee_number: '',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: 'M',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
                uganda: true,
            },
            nira_person: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.incoming_transfers = this.incomingTransfersObj;
            this.education_grades = this.educationGradesObj;
            this.transfer_reasons = this.learnerTransferReasonsObj;
            this.countries = this.countriesObj;
            this.curriculums = this.curriculumsObj;

            $('#filterIncomingEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            //For international schools
            $('#filterIncomingLearnerCurriculumId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_curriculum_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadGrades();
                    return data.text;
                },
            });

            $('#filterIncomingInterEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_education_grade_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#filterIncomingGender').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });

            $('#transferEducationGradeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#incomingTransfersModal'),
                templateSelection: function (data, container) {
                    self.transfer.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#transferCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#incomingTransfersModal'),
                templateSelection: function (data, container) {
                    self.transfer.uganda = data.text === 'UGANDA';
                    self.transfer.country_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#transferTransferReasonId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#incomingTransfersModal'),
                templateSelection: function (data, container) {
                    self.transfer.transfer_reason_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#transferParentRelationship').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#incomingTransfersModal'),
                templateSelection: function (data, container) {
                    self.transfer.parent_relationship = data.id;
                    return data.text;
                },
            });

            //Set phone number 1 flag
            let parentPhone1 = document.querySelector('#parentPhoneOne');
            let iti_person_phone_1 = intlTelInput(parentPhone1, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone1.addEventListener('blur', ()=>{
                self.transfer.parent_phone_1 = iti_person_phone_1.getNumber().slice(-9);
                parentPhone1.value = iti_person_phone_1.getNumber().slice(-9);
            });
            parentPhone1.addEventListener('change', ()=>{
                self.transfer.parent_phone_1 = iti_person_phone_1.getNumber().slice(-9);
                parentPhone1.value = iti_person_phone_1.getNumber().slice(-9);
            });

            //Set phone number 2 flag
            let parentPhone2 = document.querySelector('#parentPhoneTwo');
            let iti_person_phone_2 = intlTelInput(parentPhone2, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone2.addEventListener('blur', ()=>{
                self.transfer.parent_phone_2 = iti_person_phone_2.getNumber().slice(-9);
                parentPhone2.value = iti_person_phone_2.getNumber().slice(-9);
            });
            parentPhone2.addEventListener('change', ()=>{
                self.transfer.parent_phone_2 = iti_person_phone_2.getNumber().slice(-9);
                parentPhone2.value = iti_person_phone_2.getNumber().slice(-9);
            });

            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                $('#transferCountryId').val(ug.id).change();
                $('#transferParentRelationship').val('parent').change();
            }, 50);
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        formatDate: function (date) {
            return moment(date).format("MMMM DD, YYYY");
        },
        loadGrades: function () {
            let self = this;
            // clean grades
            this.grades = [];
            let select = $("#filterIncomingInterEducationGradeId");
            select.empty().trigger('change');
            let newOption = new Option("SELECT GRADE", "", false, false);
            select.append(newOption).trigger('change');
            self.filter.inter_sch_education_grade_id = "";

            //load new options
            if (self.filter.inter_sch_curriculum_id !== "") {
                self.grades = self.curriculums.find(curriculum=>{
                    return curriculum.id === self.filter.inter_sch_curriculum_id
                }).grades;

                self.grades.forEach(grade=>{
                    let gradeOption = new Option(grade.name, grade.id, false, false);
                    select.append(gradeOption).trigger('change');
                });
            }
        },
        toggleAllTransfers: function () {
            this.selected_incoming_transfers =[];

            if (this.select_all_incoming_transfers) {
                this.incoming_transfers.data.forEach(admin=>{
                    this.selected_incoming_transfers.push(admin.id)
                });
            }
        },
        toggleOneTransfers: function (id) {
            this.select_all_incoming_transfers = this.selected_incoming_transfers.length === this.incoming_transfers.data.length
        },
        getLearnerDetails: function () {
            let lin_format = /^[A-Z]{1}[0-9]{2}[FM][0-9]{4}[A-Z]{1}[0-9]{5}$/;
            if (lin_format.test(this.learner.lin.toUpperCase()) === false) {
                this.$refs.notify.messages.push({status: 'error', title: 'Data Error: ', message:"Wrong LIN format"});
            } else {
                this.loading = true;
                axios.get(this.api_url+'/verify/'+this.learner.lin.toLowerCase())
                    .then(response=>{
                        this.loading = false;
                        this.verify = true;
                        this.learner = response.data;
                        $('#transferEducationGradeId').val(this.learner.current_education_grade_id).change();
                        $('#transferCountryId').val(this.learner.person.country_id).change();
                    })
                    .catch(error=>{
                        this.loading = false;
                        this.renderError(error);
                    });
            }
        },
        transferLearner: function () {
            this.loading = true;
            axios.post(this.api_url+'/'+this.learner.lin.toLowerCase(), this.transfer)
                .then(response=>{
                    this.$refs.notify.messages.push({status: 'success', title: 'Success: ', message:"Learner Transfer Successful"});
                    this.incoming_transfers = response.data;
                    $('#incomingTransfersModal').modal('hide');
                    this.resetTransferProcess();
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error);
                });
        },
        verifyNIN() {
            if (this.transfer.uganda && this.transfer.parent_nin.length === 14 && !this.nin_loading && !this.nin_verify) {
                this.nin_loading = true;
                axios.post('/nira/user-info', {id_number: this.transfer.parent_nin.toUpperCase()})
                    .then(response => {
                        this.nin_loading = false;
                        this.uganda = true;
                        this.nira_person = response.data;
                        this.nin_verify = true;
                        if (this.nira_person.photo !== null) {
                            if (this.nira_person.photo.includes('.png')) {
                                $('#transferPersonPhoto').attr('src', '/images/nira-photos/' + this.nira_person.photo.toLowerCase());
                            } else {
                                $('#transferPersonPhoto').attr('src', 'data:image/png;base64,' + this.nira_person.photo);
                            }
                        } else {
                            $('#transferPersonPhoto').attr('src', this.nira_person.gender === 'M' ? '@images/default_male.jpg' : '@images/default_female.jpg');
                        }
                    })
                    .catch(error => {
                        this.nin_loading = false;
                        this.renderError(error);
                    });
            }
        },
        verifyPassport() {
            if (this.uganda && this.transfer.parent_passport.length && !this.nin_loading) {
                this.nin_loading = true;
                window.setTimeout(()=>{
                    this.nin_loading = false;
                    this.uganda = false;
                    this.nin_verify = true;
                }, 1000)
            }
        },
        // verifyRefugeeNumber() {
        //     if (!this.uganda && this.transfer.id_number.length && !this.nin_loading) {
        //         this.nin_loading = true;
        //         window.setTimeout(()=>{
        //             this.nin_loading = false;
        //             this.uganda = false;
        //             this.nin_verify = true;
        //         }, 1000)
        //     }
        // },
        resetTransferProcess: function () {
            this.verify = false;
            this.nin_verify = false;
            this.loading = false;
            this.nin_loading = false;
            this.filtering = false;
            this.learner = {
                lin: '',
                school: {
                    name: '',
                    district: {
                        name: '',
                    },
                },
                education_grade: {
                    name: '',
                },
                person: {
                    full_name: '',
                },
            };
            this.transfer = {
                transfer_reason_id: '',
                education_grade_id: '',
                country_id: '',
                id_number: '',
                parent_relationship: '',
                parent_passport: '',
                parent_refugee_number: '',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: 'M',
                parent_nin: '',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
                uganda: true,
            }
            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                $('#transferEducationGradeId').val('').change();
                $('#transferTransferReasonId').val('').change();
                $('#transferCountryId').val(ug.id).change();
                $('#transferParentRelationship').val('parent').change();
            }, 50);
        },
        loadTransfers: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'/incoming?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.incoming_transfers = response.data;
                        this.filtering = filtering;
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        startLoading: function(message) {
            if (message.length) {
                this.loading_message = message;
                $.blockUI({
                    message: $('#loadingMessage'),
                    css: {
                        padding:0,
                        margin:0,
                        width:'30%',
                        top:'40%',
                        left:'35%',
                        textAlign:'center',
                        color:'#364a63',
                        wordWrap: 'break-word',
                        backgroundColor: '#fff',
                        backgroundClip: 'border-box',
                        border: '0 solid rgba(0, 0, 0, 0.125)',
                        borderRadius: '4px',
                        cursor:'wait'
                    },
                });
            }
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            this.filter = {
                education_grade_id: '',
                inter_sch_curriculum_id: '',
                gender: '',
                name: '',
            };
            $('#filterIncomingGender').val('').change();
            $('#filterIncomingEducationGradeId').val('').change();
            $('#filterIncomingLearnerCurriculumId').val('').change();
            $('#filterIncomingInterEducationGradeId').val('').change();
            this.loadTransfers(1, false);
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        renderError: function (error) {
            console.log(error.response)
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        getPaginationLinks: function () {
            let arr = this.incoming_transfers.links;
            arr.pop();
            arr.shift();
            return arr;
        },
    },
}
</script>

<style scoped>

</style>
