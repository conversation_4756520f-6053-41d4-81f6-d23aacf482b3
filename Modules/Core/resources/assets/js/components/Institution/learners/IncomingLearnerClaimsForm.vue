<template>
    <div>
        <div class="card-inner position-relative card-tools-toggle col-10">

            <div class="card-title-group">
                <div class="card-tools card-stretch card-bordered border-dark-teal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Clai<PERSON></h5>
                        </div>
                        <div class="modal-body">
                            <notifications ref="notify"></notifications>
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="alert alert-info alert-icon">
                                <em class="icon ni ni-alert-circle"></em>
                                <h5 class="mb-2"><span class="text-dark-teal">Follow Instructions Below:</span></h5>
                                <h6 class="small mb-2"><span class="text-danger">Step 1:</span> Select Exam Year, Enter Index Number and Click Proceed.</h6>
                                <h6 class="small mb-2"><span class="text-danger">Step 2:</span> Fill in all necessary details</h6>
                                <h6 class="small mb-2"><span class="text-danger">Step 3:</span> Verify Parent's NIN and Click Claim Learner Button.</h6>
                            </div>
                            <form @submit.prevent="verify ? claimLearner() : getLearnerDetails(learner)">
                                <div v-show="!verify" :class="[!verify ? 'mb-5' : '','row']">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label class="form-label" for="learnerExamYearId">Exam Year <span class="text-danger">*</span></label>
                                            <select required="" id="learnerExamYearId" class="form-select-sm">
                                                <option value="">--SELECT--</option>
                                                <option v-for="year in academicYears" :value="year">{{ year }}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 mt-3">
                                        <div class="form-group">
                                            <label class="form-label" for="learner_index_number">Learner Index Number</label>
                                            <div class="form-control-wrap">
                                                <input required v-model.trim="form_learner.index_number" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="Enter Learner Index Number" id="learner_index_number" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 mt-3">
                                        <div class="custom-control custom-control-xs custom-checkbox">
                                            <input required="" v-model.trim="form_learner.agree" type="checkbox" class="custom-control-input" id="tc-agree">
                                            <label class="custom-control-label" for="tc-agree">I understand that this is not a false claim & that the learner belongs to our school</label>
                                        </div>
                                    </div><!-- .col -->
                                </div>
                                <div v-if="verify" class="row mb-4">
                                    <div class="col-12 ml-2">
                                        <h6>Learner Details</h6>
                                        <span class="d-block text-dark">Full Names: <span class="ucap text-dark-teal">{{ learner.person.full_name }}</span></span>
                                        <span class="d-block text-dark">Index Number: <span class="ucap text-dark-teal" v-for="indexNumber in learner.index_numbers">{{ indexNumber.level }}: {{ indexNumber.index_number }} - {{ indexNumber.exam_year }}</span></span>
                                        <span class="d-block text-dark">Current Class: <span class="ucap text-dark-teal">{{ learner.education_grade.name.toUpperCase() }}</span></span>
                                        <span class="d-block text-dark">Current School: <span class="ucap text-dark-teal">{{ learner.school.name }}, {{ learner.school.district.name.toUpperCase() }}</span></span>
                                    </div>
                                </div>

                                <div v-show="verify" class="col-12">
                                    <h6>Update Learner Details</h6>
                                    <div class="row mb-4">
                                        <div v-show="verify" class="col-lg-6">
                                            <div v-show="verify" class="form-group">
                                                <label for="learnerCountryId" class="form-label">Nationality <span class="text-danger">*</span></label>
                                                <select :required="verify" id="learnerCountryId" class="form-select-sm">
                                                    <option value="">--SELECT--</option>
                                                    <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div v-show="verify" class="col-lg-6">
                                            <div v-if="verify && uganda" class="form-group">
                                                <div class="form-label-group">
                                                    <label for="learnerNin" class="form-label">Learner NIN</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <div class="input-group">
                                                        <input :readonly="learner_verify" v-model.trim="form_learner.nin" id="learnerNin" minlength="14" maxlength="14" type="text" placeholder="eg. CM001122334455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                                        <div class="input-group-append">
                                                        <span @click="learnerVerifyNIN()" :class="[form_learner.nin.length !== 14 || nin_learner_loading ? 'disabled' : '','btn rounded-right text-white bg-dark-teal']">
                                                            <span v-if="nin_learner_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                            <span v-if="nin_learner_loading" class="sr-only">Verifying...</span>
                                                            <span v-if="!nin_learner_loading && !learner_verify" class="">Verify</span>
                                                            <em v-if="!nin_learner_loading && learner_verify" class="icon ni ni-check-circle-fill"></em>
                                                        </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-if="verify && !uganda && learner.person.identity_type_id !== 6" class="form-group">
                                                <div class="form-group">
                                                    <div class="form-label-group">
                                                        <label for="learnerStudentNumber" class="form-label">Student Pass <span class="text-danger">*</span></label>
                                                    </div>
                                                    <div class="form-control-group">
                                                        <input :required="!uganda" v-model.trim="form_learner.student_pass" id="learnerStudentNumber" minlength="9" maxlength="9" type="text" title="Student Pass Format ST0011223" placeholder="eg. ST0011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-if="verify && !uganda && learner.person.identity_type_id === 6" class="form-group">
                                                <div class="form-group">
                                                    <div class="form-label-group">
                                                        <label for="learnerRefugeeNumber" class="form-label">Refugee No. <span class="text-danger">*</span></label>
                                                    </div>
                                                    <div class="form-control-group">
                                                        <input :required="!uganda" v-model.trim="form_learner.student_refugee_number" id="learnerRefugeeNumber" minlength="12" maxlength="12" type="text" title="Learner Refugee Number" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <div v-show="verify && learner_verify && uganda" class="col-lg-6 mt-3">
                                            <div class="table-responsive">
                                                <table class="table table-sm table-hover">
                                                    <tr>
                                                        <td rowspan="2" class="align-middle text-center text-uppercase text-dark text-center px-2">
                                                            <div class="w-100px mx-auto">
                                                                <img id="learnerPersonPhoto" src="@images/default_male.jpg" class="rounded-0" alt="learner photo">
                                                            </div>
                                                        </td>
                                                        <td colspan="2" class="px-2 align-middle text-uppercase text-dark">
                                                            <h6 class="overline-title mb-0 text-dark-teal">NAME</h6>
                                                            <span class="">{{ nira_learner.surname }} {{ nira_learner.given_names }} {{ nira_learner.maiden_names }} {{ nira_learner.previous_surnames }}</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="px-2 align-middle text-uppercase text-dark">
                                                            <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                            <span class="">{{ nira_learner.gender }}</span>
                                                        </td>
                                                        <td class="px-2 w-75 align-middle text-uppercase text-dark">
                                                            <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                            <span class="">{{ nira_learner.date_of_birth }}</span>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>

                                        <div class="col-lg-6 mt-3">
                                            <div class="form-group">
                                                <label class="form-label" for="learnerBirthDate">Date Of Birth</label>
                                                <div class="form-control-wrap">
                                                    <div class="form-icon form-icon-left">
                                                        <em class="icon ni ni-calendar"></em>
                                                    </div>
                                                    <input readonly :required="verify" v-model.trim="form_learner.birth_date" placeholder="eg. 14 APRIL, 2022" id="learnerBirthDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="verify && uganda" class="col-lg-6 mt-3">
                                            <div v-show="verify" class="form-group">
                                                <label for="learnerDistrictOfBirthId" class="form-label">District Of Birth</label>
                                                <select :required="verify && uganda" id="learnerDistrictOfBirthId" class="form-select-sm">
                                                    <option value="">--SELECT--</option>
                                                    <option v-for="district in districts" :value="district.id">{{ district.name.toUpperCase() }}</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 mt-3">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">Gender</label>
                                                </div>
                                                <div class="form-group">
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input type="radio" class="custom-control-input" v-model="form_learner.gender" value="M" id="learnerMale">
                                                        <label class="custom-control-label text-uppercase" for="learnerMale">Male</label>
                                                    </div>
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input type="radio" class="custom-control-input" v-model="form_learner.gender" value="F" id="learnerFemale">
                                                        <label class="custom-control-label text-uppercase" for="learnerFemale">Female</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <!-- parent details -->
                                <div v-show="verify" class="col-12">
                                    <h6>Update Parent Details</h6>
                                    <div v-show="!uganda" class="row mt-3">
                                        <div class="col-lg-6 mt-lg-0">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="learnerParentFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="!uganda" v-model.trim="form_learner.parent_first_name" id="learnerParentFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 mt-lg-0">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="learnerParentSurname" class="form-label">Surname <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="!uganda" v-model.trim="form_learner.parent_surname" id="learnerParentSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 mt-lg-2 mt-3">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="learnerParentOtherNames" class="form-label">Other Names</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input v-model.trim="form_learner.parent_other_names" id="learnerParentOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>

                                        <div v-show="verify && !uganda" class="col-lg-6 mt-lg-2 mt-3">
                                            <div v-show="verify" class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">Gender <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-group">
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input type="radio" class="custom-control-input" v-model.number="form_learner.parent_gender" value="M" id="learnerParentMale">
                                                        <label class="custom-control-label text-uppercase" for="learnerParentMale">Male</label>
                                                    </div>
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input type="radio" class="custom-control-input" v-model.number="form_learner.parent_gender" value="F" id="learnerParentFemale">
                                                        <label class="custom-control-label text-uppercase" for="learnerParentFemale">Female</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-show="verify" class="row mb-4">
                                        <div v-show="verify" class="col-lg-6 mt-3">
                                            <div v-show="verify" class="form-group">
                                                <label class="form-label" for="parentRelationship">Parent Relationship <span class="text-danger">*</span></label>
                                                <select :required="verify" id="parentRelationship" class="form-select-sm">
                                                    <option value="">--SELECT--</option>
                                                    <option value="parent">PARENT</option>
                                                    <option value="guardian">GUARDIAN</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div v-show="verify" class="col-lg-6 mt-3">
                                            <div v-show="verify" class="form-group">
                                                <label for="parentCountryId" class="form-label">Parent Nationality <span class="text-danger">*</span></label>
                                                <select :required="verify" id="parentCountryId" class="form-select-sm">
                                                    <option value="">--SELECT--</option>
                                                    <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-show="verify && nin_verify && uganda" class="row mb-4">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-hover">
                                                <tr>
                                                    <td rowspan="2" class="align-middle text-center text-uppercase text-dark text-center px-2">
                                                        <div class="w-100px mx-auto">
                                                            <img id="parentPersonPhoto" src="@images/default_male.jpg" class="rounded-0" alt="parent photo">
                                                        </div>
                                                    </td>
                                                    <td colspan="2" class="px-2 align-middle text-uppercase text-dark">
                                                        <h6 class="overline-title mb-0 text-dark-teal">NAME</h6>
                                                        <span class="">{{ nira_parent.surname }} {{ nira_parent.given_names }} {{ nira_parent.maiden_names }} {{ nira_parent.previous_surnames }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                        <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                        <span class="">{{ nira_parent.gender }}</span>
                                                    </td>
                                                    <td class="px-2 w-75 align-middle text-uppercase text-dark">
                                                        <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                        <span class="">{{ nira_parent.date_of_birth }}</span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div v-show="verify" class="row mb-4">
                                        <div v-show="verify" class="col-lg-6">
                                            <div v-if="verify && uganda" class="form-group">
                                                <div class="form-label-group">
                                                    <label for="claimNin" class="form-label">Parent NIN</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <div class="input-group">
                                                        <input :readonly="nin_verify" :required="uganda && verify" v-model.trim="form_learner.parent_nin" id="claimNin" minlength="14" maxlength="14" type="text" placeholder="eg. CM001122334455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                                        <div class="input-group-append">
                                                            <span @click="parentVerifyNIN()" :class="[form_learner.parent_nin.length !== 14 || nin_loading ? 'disabled' : '','btn rounded-right text-white bg-dark-teal']">
                                                                <span v-if="nin_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                <span v-if="nin_loading" class="sr-only">Verifying...</span>
                                                                <span v-if="!nin_loading && !nin_verify" class="">Verify</span>
                                                                <em v-if="!nin_loading && nin_verify" class="icon ni ni-check-circle-fill"></em>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-if="verify && !uganda && learner.person.identity_type_id !== 6" class="form-group">
                                                <div class="form-label-group">
                                                    <label for="claimPassport" class="form-label">Parent Passport <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <div class="input-group">
                                                        <input :required="!uganda && verify" v-model.trim="form_learner.parent_passport" id="claimPassport" type="text" placeholder="eg. B0010002" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
<!--                                                        <div class="input-group-append">-->
<!--                                                            <span @click="verifyPassport()" :class="[!form_learner.parent_passport.length || nin_loading ? 'disabled' : '','btn rounded-right text-white bg-dark-teal']">-->
<!--                                                                <span v-if="nin_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>-->
<!--                                                                <span v-if="nin_loading" class="align-self-center">Verifying...</span>-->
<!--                                                                <span v-if="nin_loading" class="sr-only">Verifying...</span>-->
<!--                                                                <span v-if="!nin_loading" class="">Verify</span>-->
<!--                                                            </span>-->
<!--                                                        </div>-->
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-if="verify && !uganda && learner.person.identity_type_id === 6" class="form-group">
                                                <div class="form-label-group">
                                                    <label for="claimRefugeeNumber" class="form-label">Parent Refugee Number <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <div class="input-group">
                                                        <input :required="!uganda && verify && learner.person.identity_type_id === 6" v-model.trim="form_learner.parent_refugee_number" id="claimRefugeeNumber" type="text" placeholder="eg. FVA-0738288" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
<!--                                                        <div class="input-group-append">-->
<!--                                                            <span @click="verifyRefugeeNumber()" :class="[!form_learner.id_number.length || nin_loading ? 'disabled' : '','btn rounded-right text-white bg-dark-teal']">-->
<!--                                                                <span v-if="nin_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>-->
<!--                                                                <span v-if="nin_loading" class="align-self-center">Verifying...</span>-->
<!--                                                                <span v-if="nin_loading" class="sr-only">Verifying...</span>-->
<!--                                                                <span v-if="!nin_loading" class="">Verify</span>-->
<!--                                                            </span>-->
<!--                                                        </div>-->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="verify" class="col-lg-6">
                                            <div v-show="verify" class="form-group">
                                                <div class="form-label-group">
                                                    <label for="parentPhoneOne" class="form-label">Phone Number 1<span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="verify" v-model="form_learner.parent_phone_1" id="parentPhoneOne" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="verify" class="col-lg-6 mt-2">
                                            <div v-show="verify" class="form-group">
                                                <div class="form-label-group">
                                                    <label for="parentPhoneTwo" class="form-label">Phone Number 2</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input v-model="form_learner.parent_phone_2" id="parentPhoneTwo" maxlength="10" type="text" placeholder="Enter Phone Number 2" class="form-control bg-primary-dim" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="verify" class="col-lg-6 mt-2">
                                            <div v-show="verify" class="form-group">
                                                <div class="form-label-group">
                                                    <label for="claimEmail" class="form-label">Email Address</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input v-model="form_learner.parent_email" id="claimEmail" type="text" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12 d-flex flex-lg-row justify-content-center">
                                        <button @click="resetClaimProcess()" :disabled="loading" type="button" class="btn btn-dim btn-outline-light me-2">
                                            <span>Clear</span>
                                            <em class="ni ni-cross ml-2"></em>
                                        </button>
                                        <button :disabled="loading || (verify && !nin_verify && uganda)" type="submit" class="btn bg-dark-teal d-flex">
                                            <span v-if="loading" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                            <span v-if="loading && !verify" class="align-self-center">Fetching Data, Please wait...</span>
                                            <span v-if="loading && !verify" class="sr-only">Fetching Data...</span>
                                            <span v-if="!loading && !verify" class="align-self-center">Proceed</span>

                                            <span v-if="loading && verify" class="align-self-center">Claiming, Please wait...</span>
                                            <span v-if="loading && verify" class="sr-only">Claiming, Please wait...</span>
                                            <span v-if="!loading && verify" class="align-self-center">Claim Learner</span>

                                            <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div><!-- .card-tools -->
            </div><!-- .card-title-group -->
        </div><!-- .card-inner -->


    </div><!-- .card-inner-group -->
</template>

<script>
import Notifications from "../../Notifications.vue";
import moment from "moment";
import ErrorNotifications from "../../Notifications.vue";

export default {
    name: "IncomingLearnerClaimsForm",
    props: [
        "countriesObj",
        "districtsObj",
    ],
    components: {
        Notifications,
        ErrorNotifications
    },
    data: function () {
        return {
            api_url: '/institutions/learners/claims',
            verify: false,
            loading: false,
            filtering: false,
            nin_learner_loading: false,
            nin_loading: false,
            nin_verify: false,
            //learner_nin: "yes",
            //learner_refugee_no: "no",
            learner_verify: false,
            loading_message: '',
            photoDropify: null,
            learner: {
                current_education_grade_id: '',
                school: {
                    name: '',
                    district: {
                        name: '',
                    },
                },
                education_grade: {
                    name: '',
                },
                person: {
                    gender: 'M',
                    birth_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                    country_id: 221,
                    country: {
                        name: ''
                    },
                },
                district_of_birth_id: '',
                parents:[],
                index_numbers: []
            },
            countries: [],
            districts: [],
            exam_years: [],
            form_learner: {
                index_number: '',
                exam_year: '',
                agree: '',
                district_of_birth_id: '',
                learner_country_id: '',
                country_id: '',
                nin: '',
                student_pass: '',
                student_refugee_number: '',
                gender: 'M',
                birth_date: moment().subtract(11, 'years').format("D MMMM, YYYY"),
                parent_relationship: '',
                parent_passport: '',
                parent_refugee_number: '',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: 'M',
                parent_nin: '',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
            },
            uganda: true,
            uneb_learner: {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                index_number: '',
                gender: '',
                date_of_birth: '',
                name: '',
                exam_level: '',
                exam_year: '',
            },
            nira_learner: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            nira_parent: {
                national_id: '',
                surname: '',
                given_names: '',
                previous_surnames: '',
                maiden_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.countries = this.countriesObj;
            this.districts = this.districtsObj;

            $('#learnerExamYearId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_learner.exam_year = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerBirthDate').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().subtract(4, 'years').toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.form_learner.birth_date = moment(e.date).format('D MMMM, YYYY');
            });

            $('#learnerCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.uganda = data.text === 'UGANDA';
                    self.$parent.uganda = data.text === 'UGANDA';
                    self.form_learner.learner_country_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerDistrictOfBirthId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_learner.district_of_birth_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#parentCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.uganda = data.text === 'UGANDA';
                    self.form_learner.country_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#parentRelationship').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_learner.parent_relationship = data.id;
                    return data.text;
                },
            });

            //Set phone number 1 flag
            let parentPhone1 = document.querySelector('#parentPhoneOne');
            let iti_person_phone_1 = intlTelInput(parentPhone1, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone1.addEventListener('blur', ()=>{
                self.form_learner.parent_phone_1 = iti_person_phone_1.getNumber().slice(-9);
                parentPhone1.value = iti_person_phone_1.getNumber().slice(-9);
            });
            parentPhone1.addEventListener('change', ()=>{
                self.form_learner.parent_phone_1 = iti_person_phone_1.getNumber().slice(-9);
                parentPhone1.value = iti_person_phone_1.getNumber().slice(-9);
            });

            //Set phone number 2 flag
            let parentPhone2 = document.querySelector('#parentPhoneTwo');
            let iti_person_phone_2 = intlTelInput(parentPhone2, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone2.addEventListener('blur', ()=>{
                self.form_learner.parent_phone_2 = iti_person_phone_2.getNumber().slice(-9);
                parentPhone2.value = iti_person_phone_2.getNumber().slice(-9);
            });
            parentPhone2.addEventListener('change', ()=>{
                self.form_learner.parent_phone_2 = iti_person_phone_2.getNumber().slice(-9);
                parentPhone2.value = iti_person_phone_2.getNumber().slice(-9);
            });

            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                $('#learnerExamYearId').val('').change();
                $('#learnerCountryId').val(ug.id).change();
                $('#parentCountryId').val(ug.id).change();
                $('#parentRelationship').val('parent').change();
            }, 50);
        },
        formatDate: function (date) {
            return moment(date).format("MMMM DD, YYYY");
        },

        getLearnerDetails: function () {
            //console.log(learner);
            this.loading = true;
            axios.post(this.api_url+'/verify', {index_number: this.form_learner.index_number, exam_year: this.form_learner.exam_year})
                .then(response => {
                    this.loading = false;
                    this.learner = response.data;
                    this.form_learner.gender = this.learner.person.gender;
                    $('#learnerBirthDate').datepicker('setDate', moment(this.learner.person.birth_date).toDate());
                    this.form_learner.birth_date = moment(this.learner.person.birth_date).format('D MMMM, YYYY');
                    $('#learnerCountryId').val(this.learner.person.country_id).change();
                    $('#learnerDistrictOfBirthId').val(this.learner.district_of_birth_id).change();
                    $('#parentCountryId').val(this.learner.person.country_id).change();
                    this.verify = true
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        claimLearner: function () {
            this.loading = true;
            axios.post(this.api_url+'/'+this.learner.lin.toLowerCase(), this.form_learner)
                .then(response=>{
                    this.$refs.notify.messages.push({status: 'success', title: 'Success:', message:"Learner Claim Successful, Check learner claim list for details"});
                    this.incoming_claims = response.data;
                    this.resetClaimProcess();
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error);
                });
        },
        learnerVerifyNIN: function () {
            if (this.uganda && this.form_learner.nin.length === 14 && !this.nin_learner_loading && !this.learner_verify) {
                this.nin_learner_loading = true;
                axios.post('/nira/user-info', {id_number: this.form_learner.nin.toUpperCase()})
                    .then(response => {
                        this.nin_learner_loading = false;
                        this.uganda = true;
                        this.nira_learner = response.data;
                        this.learner_verify = true;
                        this.form_learner.first_name = this.nira_learner.given_names;
                        this.form_learner.surname = this.nira_learner.surname;
                        this.form_learner.gender = this.nira_learner.gender;
                        this.form_learner.birth_date = moment(this.nira_learner.birth_date).format("D MMMM, YYYY");
                        if (this.nira_learner.photo !== null) {
                            if (this.nira_learner.photo.includes('.png')) {
                                $('#learnerPersonPhoto').attr('src', '/images/nira-photos/' + this.nira_learner.photo.toLowerCase());
                            } else {
                                $('#learnerPersonPhoto').attr('src', 'data:image/png;base64,' + this.nira_learner.photo);
                            }
                        } else {
                            $('#learnerPersonPhoto').attr('src', this.nira_learner.gender === 'M' ? '@images/default_male.jpg' : '@images/default_female.jpg');
                        }
                    })
                    .catch(error => {
                        this.nin_learner_loading = false;
                        this.renderError(error);
                    });
            }
        },
        parentVerifyNIN() {
            if (this.uganda && this.form_learner.parent_nin.length === 14 && !this.nin_loading && !this.nin_verify) {
                this.nin_loading = true;
                axios.post('/nira/user-info', {id_number: this.form_learner.parent_nin.toUpperCase()})
                    .then(response => {
                        this.nin_loading = false;
                        this.uganda = true;
                        this.nira_parent = response.data;
                        this.nin_verify = true;
                        if (this.nira_parent.photo !== null) {
                            if (this.nira_parent.photo.includes('.png')) {
                                $('#parentPersonPhoto').attr('src', '/images/nira-photos/' + this.nira_parent.photo.toLowerCase());
                            } else {
                                $('#parentPersonPhoto').attr('src', 'data:image/png;base64,' + this.nira_parent.photo);
                            }
                        } else {
                            $('#parentPersonPhoto').attr('src', this.nira_parent.gender === 'M' ? '@images/default_male.jpg' : '@images/default_female.jpg');
                        }
                    })
                    .catch(error => {
                        this.nin_loading = false;
                        this.renderError(error);
                    });
            }
        },
        verifyPassport() {
            if (this.uganda && this.form_learner.parent_passport.length && !this.nin_loading) {
                this.nin_loading = true;
                window.setTimeout(()=>{
                    this.nin_loading = false;
                    this.uganda = false;
                    this.nin_verify = true;
                }, 1000)
            }
        },
        // verifyRefugeeNumber() {
        //     if (!this.uganda && this.form_learner.id_number.length && !this.nin_loading) {
        //         this.nin_loading = true;
        //         window.setTimeout(()=>{
        //             this.nin_loading = false;
        //             this.uganda = false;
        //             this.nin_verify = true;
        //         }, 1000)
        //     }
        // },
        resetClaimProcess: function () {
            this.verify = false;
            this.nin_verify = false;
            this.loading = false;
            this.uganda =  true;
            this.nin_learner_loading = false;
            this.nin_loading = false;
            //this.learner_nin = "yes";
            this.learner_verify = false;
            this.filtering = false;
            this.learner = {
                school: {
                    name: '',
                    district: {
                        name: '',
                    },
                },
                education_grade: {
                    name: '',
                },
                person: {
                    full_name: '',
                },
                parents:[],
                index_numbers:[]
            };
            this.form_learner = {
                index_number: '',
                exam_year: '',
                agree: '',
                district_of_birth_id: '',
                learner_country_id: '',
                country_id: '',
                nin: '',
                student_pass: '',
                student_refugee_number: '',
                gender: 'M',
                birth_date: moment().subtract(11, 'years').format("D MMMM, YYYY"),
                parent_relationship: '',
                parent_passport: '',
                parent_refugee_number: '',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: '',
                parent_nin: '',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
            };

            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                $('#learnerExamYearId').val('').change();
                $('#learnerCountryId').val(ug.id).change();
                $('#learnerDistrictOfBirthId').val('').change();
                $('#learnerBirthDate').datepicker('setDate', moment().toDate());
                $('#parentCountryId').val(ug.id).change();
                $('#parentRelationship').val('parent').change();
            }, 50);
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
            }
        },
    },
    computed: {
        academicYears: function () {
            let years = [];

            for (let i = Number(moment().format("YYYY"))-1; i >= 2015; i--) {
                years.push(i);
            }

            return years;
        },
    },
}
</script>

<style scoped>

</style>
