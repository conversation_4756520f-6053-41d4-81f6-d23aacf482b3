<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Bulk Photo Uploads</h3>
                    <div class="nk-block-des">
                        <p>Upload images (compressed) for learners</p>
                    </div>
                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner">
                    <notifications ref="notify"></notifications>
                    <div class="preview-block">
                        <form @submit.prevent="uploadZippedFile()">
                            <div class="row">
                                <div class="col-12">
                                    <span class="fs-16 font-weight-bold mr-lg-2">Step 1:</span>
                                    <span>Select the grade whose photos you are trying to upload</span>
                                </div>
                                <div v-show="school_type_id === 7" class="col-lg-6 mt-2">
                                    <div class="form-group">
                                        <select :required="school_type_id === 7" id="curriculumId" class="form-select-sm">
                                            <option value="">--SELECT CURRICULUM--</option>
                                            <option v-for="curriculum in curriculums" :key="curriculum.id" :value="curriculum.id">{{ curriculum.name }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div v-show="school_type_id <= 6" class="col-lg-6 mt-2">
                                    <div class="form-group">
                                        <select :required="school_type_id <= 6" id="educationGradeId" class="form-select-sm">
                                            <option value="">--SELECT GRADE--</option>
                                            <option v-for="education_grade in education_grades" :key="education_grade.id" :value="education_grade.id">{{ education_grade.name }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div v-show="school_type_id === 7" class="col-lg-6 mt-3">
                                    <div class="form-group">
                                        <select :required="school_type_id === 7" id="interGradeId" class="form-select-sm">
                                            <option value="">--SELECT GRADE--</option>
                                            <option v-for="inter_grade in inter_grades" :key="inter_grade.id" :value="inter_grade.id">{{ inter_grade.name }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-5">
                                <div class="col-lg-6 col-12">
                                    <span class="fs-16 font-weight-bold mr-lg-2">Step 2:</span>
                                    <span class="d-block">Rename all photos you intend to upload with the corresponding learner identification numbers (LIN) only then put them in one folder and zip it.</span>
                                </div>
                                <div class="col-lg-6 mt-2"></div>
                            </div>
                            <div class="row mt-5">
                                <div class="col-12">
                                    <span class="fs-16 font-weight-bold mr-lg-2">Step 3:</span>
                                    <span>Select the zipped file with the photos</span>
                                </div>
                                <div class="col-lg-6 mt-2">
                                    <div class="form-group">
                                        <div class="custom-file">
                                            <input
                                                :disabled="loading"
                                                ref="zippedFile"
                                                type="file"
                                                :class="[loading ? '' : 'cursor', 'custom-file-input']"
                                                id="zippedFile"
                                                @change="checkFile()"
                                                accept="application/zip,.zip,.rar"
                                                required>
                                            <label id="zippedFileLabel" class="custom-file-label" for="zippedFile">Select File to Upload</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-5">
                                <div class="col-lg-6 col-12 d-flex flex-lg-row justify-content-center">
                                    <button @click="resetUpload()" type="button" class="btn btn-outline-dark-teal me-2">
                                        <span>Clear</span>
                                        <em class="ni ni-cross ml-2"></em>
                                    </button>
                                    <button :disabled="loading" type="submit" class="btn bg-dark-teal d-flex">
                                        <span v-if="loading" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                        <span v-if="loading" class="align-self-center">Uploading...</span>
                                        <span v-if="loading" class="sr-only">Uploading...</span>
                                        <span v-if="!loading" class="align-self-center">Upload Archive</span>

                                        <em v-if="!loading" class="ni ni-upload ml-2"></em>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div><!-- .card -->
        </div><!-- .nk-block -->
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";

export default {
    name: "LearnerPhotoBulkUploads",
    props: ["educationGradesObj","curriculumsObj","schoolTypeIdObj"],
    components: {
        Notifications
    },
    data: function () {
        return {
            api_url: '/institutions/learners/photo-bulk-uploads',
            loading: false,
            valid_file: false,
            file_name: null,
            education_grades: [],
            curriculums: [],
            inter_grades: [],
            upload: {
                education_grade_id: '',
                inter_sch_curriculum_id: '',
                inter_sch_grade_id: '',
                zipped_file: null,
            },
            education_grade: '',
            school_type_id: '',
        }
    },
    mounted() {
        this.initPlugins()
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.education_grades = this.educationGradesObj;
            this.curriculums = this.curriculumsObj;
            this.school_type_id = this.schoolTypeIdObj;

            $('#educationGradeId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.upload.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    self.education_grade = data.text;
                    return data.text;
                },
            });

            $('#curriculumId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.upload.inter_sch_curriculum_id = data.id.length > 0 ? Number(data.id) : "";
                    $("#interGradeId").val("").change();
                    self.loadInterGrades();
                    return data.text;
                },
            });

            $('#interGradeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.upload.inter_sch_grade_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });
        },
        loadInterGrades: function () {
            this.inter_grades = [];
            this.resetInterGradesSelect();
            if (this.upload.inter_sch_curriculum_id !== "") {
                this.inter_grades = this.curriculums.find(curriculum=>{
                    return curriculum.id === this.upload.inter_sch_curriculum_id;
                }).grades;
            }
        },
        resetInterGradesSelect: function() {
            let self = this;
            let select = $("#interGradeId");
            if (select.hasClass("select2-hidden-accessible")) {
                select.select2('destroy');
                select.select2({
                    minimumResultsForSearch: 0,
                    containerCssClass: 'border-dark-teal',
                    templateSelection: function (data, container) {
                        self.upload.inter_sch_grade_id = data.id.length > 0 ? Number(data.id) : "";
                        return data.text;
                    },
                });
            }
        },
        checkFile: function () {
            let validExts = [".zip", ".rar"];
            let fileExt = this.$refs.zippedFile.value;
            fileExt = fileExt.substring(fileExt.lastIndexOf('.'));

            if (validExts.indexOf(fileExt) < 0) {
                new Noty({
                    type: "error",
                    text: "Invalid file selected, valid files are of " + validExts.toString() + " types."
                }).show();

                this.resetFile();
                return false;
            }

            let fileName = this.$refs.zippedFile.value;
            fileName = fileName.substring(fileName.lastIndexOf('\\')+1);
            this.file_name = fileName;
            $('#zippedFileLabel').text(fileName)

            this.valid_file = true;
            this.upload.zipped_file = this.$refs.zippedFile.files[0];

            return true;
        },

        resetFile: function () {
            this.file_name = null;
            this.upload.zipped_file = '';
            this.valid_file = false;
            this.loading = false;
            this.$refs.zippedFile.value = null;
            window.setTimeout(()=>{$('#zippedFileLabel').text("Select File to Upload")}, 10);
        },
        resetUpload: function () {
            $('#educationGradeId').val('').change();
            $('#curriculumId').val('').change();
            $('#interGradeId').val('').change();
            this.resetFile();
        },
        uploadZippedFile: function () {
            this.loading = true;
            let formData = new FormData();
            formData.append('education_grade_id', this.upload.education_grade_id);
            formData.append('inter_sch_curriculum_id', this.upload.inter_sch_curriculum_id);
            formData.append('inter_sch_education_grade_id', this.upload.inter_sch_grade_id);
            formData.append('zipped_file', this.upload.zipped_file);

            axios.post(this.api_url, formData, {headers: {'Content-Type': 'multipart/form-data'}})
                .then(response=>{
                    this.$refs.notify.messages.push({status: 'success', title: 'Success:', message:"Photos uploaded successfully"});
                    //this.$refs.notify.messages.push({status: 'success', title: 'Success:', message:"Photos for "+this.education_grade+" uploaded successfully"});
                    this.resetUpload();
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error)
                });
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {

    },
}
</script>

<style scoped>

</style>
