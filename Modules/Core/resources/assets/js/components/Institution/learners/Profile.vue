<template>
    <div class="">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 v-if="schoolTypeId > 3 && schoolTypeId !== 7" class="nk-block-title page-title">Student Profile</h3>
                    <h3 v-else class="nk-block-title page-title">Learner Profile</h3>
                    <div class="nk-block-des text-soft"><p>{{ learner.person.full_name }} ({{ learner.lin }}) Details.</p></div>
                    <!--                    <nav class="nk-block-des">-->
                    <!--                        <ul class="breadcrumb breadcrumb-arrow">-->
                    <!--                            <li class="breadcrumb-item"><a href="/institution/dashboard">Dashboard</a></li>-->
                    <!--                            <li class="breadcrumb-item"><a href="/institution/learners/all">Learners</a></li>-->
                    <!--                            <li class="breadcrumb-item active text-soft">{{ learner.person.full_name }}</li>-->
                    <!--                        </ul>-->
                    <!--                    </nav>-->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->

        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="nk-block mt-4">
            <div class="card card-bordered card-stretch border-dark-teal">
                <div class="tab-content card-aside-wrap">
                    <div class="card-aside card-aside-left user-aside toggle-slide toggle-slide-left toggle-break-lg" data-content="userAside" data-toggle-screen="lg" data-toggle-overlay="true">
                        <div class="card-inner-group" data-simplebar>
                            <div class="card-inner">
                                <div class="user-card">
                                    <div class="user-avatar w-80px" style="height: 0%; border-radius: 0">
                                        <img :src="learner.person.photo_url" :alt="learner.person.full_name" style="border-radius: 0">
                                    </div>
                                    <div class="user-info">
                                        <span class="lead-text text-uppercase">{{ learner.person.full_name }}</span>
                                        <span class="lead-text text-bold">Age: <span class="text-uppercase ml-1">{{ age }}yrs</span></span>
                                        <span class="lead-text text-bold">Sex: <span class="text-uppercase ml-1">{{ learner.person.gender === 'F' ? "Female" : "Male" }}</span></span>
                                    </div>
                                </div><!-- .user-card -->
                            </div><!-- .card-inner -->

                            <div class="card-inner p-0">
                                <ul class="nav nav-tabs link-list-menu side-link-menu">
                                    <li class="lead-nav-links">
                                        <a class="nav-item active" data-toggle="tab" href="#basicInfo">
                                            <em class="icon ni ni-user-fill-c"></em><span>Basic Info</span>
                                        </a>
                                    </li>
                                    <li class="lead-nav-links">
                                        <a class="nav-item py-2" data-toggle="tab" href="#parentsGuardian">
                                            <em class="icon ni ni-users-fill"></em>
                                            <span v-if="schoolTypeId >= 4 && schoolTypeId !== 7">Next Of Kin</span>
                                            <span v-else>Parents/Guardian</span>
                                        </a>
                                    </li>
                                    <li class="lead-nav-links">
                                        <a class="nav-item py-2" data-toggle="tab" href="#specialNeeds">
                                            <em class="icon ni ni-shield-alert-fill"></em><span>Special Needs</span>
                                        </a>
                                    </li>
                                    <li class="lead-nav-links">
                                        <a class="nav-item py-2" data-toggle="tab" href="#healthInformation">
                                            <em class="icon ni ni-activity-round-fill"></em><span>Health Issues</span>
                                        </a>
                                    </li>
                                    <li class="lead-nav-links">
                                        <a class="nav-item py-2" data-toggle="tab" href="#TalentInformation">
                                            <em class="icon ni ni-tag-alt-fill"></em><span>Talents</span>
                                        </a>
                                    </li>
                                    <li v-if="schoolTypeId === 1 || schoolTypeId === 2" class="lead-nav-links">
                                        <a class="nav-item py-2" data-toggle="tab" href="#familiarLanguages">
                                            <em class="icon ni ni-label-alt-fill"></em><span>Familiar Languages</span>
                                        </a>
                                    </li>
                                    <li v-if="schoolTypeId === 2 || schoolTypeId === 3" class="lead-nav-links">
                                        <a class="nav-item py-2" data-toggle="tab" href="#practicalSkills">
                                            <em class="icon ni ni-opt-dot"></em><span>Practical Skills</span>
                                        </a>
                                    </li>
                                    <li v-if="schoolTypeId === 3 && aLevel" class="lead-nav-links">
                                        <a class="nav-item py-2" data-toggle="tab" href="#subjectsInformation">
                                            <em class="icon ni ni-book"></em><span>Subjects</span>
                                        </a>
                                    </li>
                                </ul>
                            </div><!-- .card-inner -->
                        </div><!-- .card-inner-group -->
                    </div><!-- card-aside -->

                    <div class="tab-pane active card-inner card-inner-lg" style="padding: 2%" id="basicInfo">
                        <div class="">
                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a class="nav-link active" data-toggle="tab" href="#emisDetails">EMIS DETAILS</a>
                                </li>
                                <li v-if="showUnebDetails && hasUNEBDetails" class="nav-item">
                                    <a class="nav-link" data-toggle="tab" href="#unebDetails">UNEB DETAILS</a>
                                </li>
                                <li v-if="learner.uneb_equated_data && schoolTypeId === 3" class="nav-item">
                                    <a class="nav-link" data-toggle="tab" href="#unebEquatedDetails">UNEB EQUATED HISTORY</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-toggle="tab" href="#niraDetails">NIRA DETAILS</a>
                                </li>
                            </ul>

                            <div class="card">
                                <div class="card-aside-wrap">
                                    <div class="card-content">
                                        <div class="card-inner">
                                            <div class="tab-content">

                                                <!-- EMIS Details -->
                                                <div class="tab-pane active" id="emisDetails">
                                                    <!--EMIS Details-->
                                                    <div class="nk-block">
                                                        <div class="nk-block-head nk-block-head-lg">
                                                            <div class="nk-block-between">
                                                                <div class="nk-block-head-content">
                                                                    <h5 class="nk-block-title">Personal Information</h5>
                                                                    <div class="nk-block-des">
                                                                        <p>EMIS Basic details.</p>
                                                                    </div>
                                                                </div>

                                                                <div class="nk-block-head-content">
                                                                    <!-- <button data-toggle="modal" @click="editLearner(learner)" data-target="#updateLearnerInfoModal" data-backdrop="static" type="button" class="btn btn-sm mt-4 bg-dark-teal">
                                                                        Edit Learner
                                                                        </button> -->

                                                                    <!-- <button :disabled="learner.is_flagged_yn" data-toggle="modal" data-target="#deleteModal" data-backdrop="static" type="button" class="cursor btn btn-sm mt-4 bg-red">
                                                                        <em class="icon ni ni-trash-fill text-white"></em> <span class="mt-1">Initiate Delete</span>
                                                                    </button> -->
                                                                </div>
                                                                <div class="nk-block-head-content align-self-start d-lg-none">
                                                                    <a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                                                </div>
                                                            </div>
                                                        </div><!-- .nk-block-head -->
                                                        <div class="nk-block">
                                                            <div v-if="learner.is_flagged_yn" class="alert alert-warning alert-icon">
                                                                <em class="icon ni ni-alert-circle"></em> This learner is in <strong>flagged</strong> mode!!!
                                                            </div>

                                                            <div class="nk-data">
                                                                <div class="table-responsive">
                                                                    <table class="table table-sm table-striped">
                                                                        <tbody>
                                                                        <tr>
                                                                            <td class="text-uppercase text-muted">Full Name</td>
                                                                            <td class="text-uppercase text-dark">{{ learner.person.full_name }}</td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="text-uppercase text-muted">Date Of Birth</td>
                                                                            <td class="text-uppercase text-dark">{{ formatDate(learner.person.birth_date) }}</td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="text-uppercase text-muted">Nationality</td>
                                                                            <td class="text-uppercase text-dark">{{ learner.person.country.name.toUpperCase() }}</td>
                                                                        </tr>
                                                                        <tr v-show="learner.person.country.name === 'UGANDA'">
                                                                            <td class="text-uppercase text-muted">District Of Birth</td>
                                                                            <td v-if="learner.district_of_birth !== null" class="text-uppercase text-dark">{{ learner.district_of_birth.name.toUpperCase() }}</td>
                                                                            <td v-else class="text-uppercase text-muted">Not Set</td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="text-uppercase text-muted">Orphanage Status</td>
                                                                            <td v-if="learner.is_father_dead === false & learner.is_mother_dead === false" class="text-uppercase text-dark">N/A</td>
                                                                            <td v-if="learner.is_father_dead === true & learner.is_mother_dead === false" class="text-uppercase text-dark">Only Father Dead</td>
                                                                            <td v-if="learner.is_father_dead === false & learner.is_mother_dead === true" class="text-uppercase text-dark">Only Mother Dead</td>
                                                                            <td v-if="learner.is_father_dead === true & learner.is_mother_dead === true" class="text-uppercase text-dark">Both Parents Dead</td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="text-uppercase text-muted">LIN</td>
                                                                            <td class="text-uppercase text-dark">
                                                                                <span v-if="learner.lin === null" class="text-muted">Not set</span>
                                                                                <span v-else>{{ learner.lin }}</span>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="text-uppercase text-muted">
                                                                                <span v-if="learner.person.identity_type_id === 1 || learner.person.country.name === 'UGANDA'" class="data-label">NIN</span>
                                                                                <span v-else-if="learner.person.identity_type_id === 4" class="data-label">Student Pass</span>
                                                                                <span v-else-if="learner.person.identity_type_id === 6" class="data-label">Refugee Number</span>
                                                                                <span v-else class="data-label">ID Number</span>
                                                                            </td>
                                                                            <td class="text-uppercase text-dark">
                                                                                <span v-if="learner.person.id_number === null" class="text-muted">Not set</span>
                                                                                <span v-else>{{ learner.person.masked_id_number }}</span>
                                                                            </td>
                                                                        </tr>
                                                                        <tr v-if="schoolTypeId === 3 && !learner.uneb_equated_data">
                                                                            <td class="text-uppercase text-muted">
                                                                                <span class="data-label">UNEB Index Number</span>
                                                                            </td>
                                                                            <td class="text-uppercase text-dark">
                                                                                <div v-if="learnerIndexNumbers && learnerIndexNumbers.length > 0">
                                                                                    <span v-for="(index, indexKey) in learnerIndexNumbers" :key="indexKey">
                                                                                    {{ index.level }}: {{ index.index_number }} - {{ index.exam_year }}
                                                                                    <span v-if="indexKey !== learnerIndexNumbers.length - 1">, </span>
                                                                                    </span>
                                                                                </div>
                                                                                <span v-else class="text-muted">Not set</span>
                                                                            </td>
                                                                        </tr>
                                                                        <tr v-if="schoolTypeId === 3 && learner.uneb_equated_data">
                                                                            <td class="text-uppercase text-muted">
                                                                                <span class="data-label">Equated Code</span>
                                                                            </td>
                                                                            <td class="text-uppercase text-dark">
                                                                                <span v-if="true">{{ learner.uneb_equated_data.exam_level }}: {{ learner.uneb_equated_data.equated_code }} -  {{ learner.uneb_equated_data.equated_year }}</span>
                                                                                <span v-else class="text-muted">Not Set</span>
                                                                            </td>
                                                                        </tr>
                                                                        <tr v-if="schoolTypeId === 7">
                                                                            <td class="text-uppercase text-muted">
                                                                                <span class="data-label">Calendar</span>
                                                                            </td>
                                                                            <td class="text-uppercase text-dark">
                                                                                <span v-if="learner.international_calendar === null" class="text-muted">Not set</span>
                                                                                <span v-else>{{ learner.international_calendar.name }}</span>
                                                                            </td>
                                                                        </tr>
                                                                        <tr v-if="schoolTypeId === 7">
                                                                            <td class="text-uppercase text-muted">
                                                                                <span class="data-label">Curriculum</span>
                                                                            </td>
                                                                            <td class="text-uppercase text-dark">
                                                                                <span v-if="learner.international_curriculum === null" class="text-muted">Not set</span>
                                                                                <span v-else>{{ learner.international_curriculum.name }}</span>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="text-uppercase text-muted">
                                                                                <span v-if="schoolTypeId <= 3 || schoolTypeId === 7" class="data-label">Class</span>
                                                                                <span v-if="schoolTypeId >= 4 && schoolTypeId !== 7" class="data-label">Year Of Study</span>
                                                                            </td>
                                                                            <td v-if="schoolTypeId <= 6" class="text-uppercase text-dark">
                                                                                <span v-if="learner.current_education_grade_id === null" class="text-muted">Not set</span>
                                                                                <span v-else>{{ learner.education_grade.name.toUpperCase() }}</span>
                                                                            </td>
                                                                            <td v-if="schoolTypeId === 7" class="text-uppercase text-dark">
                                                                                <span v-if="learner.inter_sch_education_grade_id === null" class="text-muted">Not set</span>
                                                                                <span v-else>{{ learner.international_education_grade.name.toUpperCase() }}</span>
                                                                            </td>
                                                                        </tr>
                                                                        <tr v-if="schoolTypeId >= 4 && schoolTypeId !== 7">
                                                                            <td class="text-uppercase text-muted">
                                                                                <span class="data-label">COURSE</span>
                                                                            </td>
                                                                            <td class="text-uppercase text-dark">
                                                                                <span v-if="learner.current_post_primary_institution_course_id !== null">
                                                                                    {{ learner.learner_enrolments[0].post_primary_institution_course.name }}
                                                                                </span>
                                                                                <span v-else-if="learner.current_institution_examined_course_id !== null">
                                                                                    {{ learner.learner_enrolments[0].institution_examined_course.name}}
                                                                                </span>
                                                                                <span v-else-if="learner.school_course !== null">
                                                                                    {{ learner.school_course.name.toUpperCase() }}
                                                                                </span>
                                                                                <span v-else class="data-value text-muted">Not set</span>
                                                                            </td>
                                                                        </tr>
                                                                        <!--                                                            <tr v-if="learner.person.phone_1 !== null">-->
                                                                        <!--                                                                <td class="text-uppercase text-muted">Phone 1</td>-->
                                                                        <!--                                                                <td class="text-uppercase text-dark">+{{ learner.person.phone_1 }}</td>-->
                                                                        <!--                                                            </tr>-->
                                                                        <!--                                                            <tr v-if="learner.person.phone_2 !== null">-->
                                                                        <!--                                                                <td class="text-uppercase text-muted">Phone 2</td>-->
                                                                        <!--                                                                <td class="text-uppercase text-dark">+{{ learner.person.phone_2 }}</td>-->
                                                                        <!--                                                            </tr>-->
                                                                        <!--                                                            <tr v-if="learner.person.email !== null">-->
                                                                        <!--                                                                <td class="text-uppercase text-muted">Email</td>-->
                                                                        <!--                                                                <td class="text-lowercase text-dark">{{ learner.person.email }}</td>-->
                                                                        <!--                                                            </tr>-->
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div><!-- .nk-block -->
                                                    </div><!-- .nk-block -->
                                                </div>

                                                <!-- UNEB Details -->
                                                <div v-if="showUnebDetails" class="tab-pane" id="unebDetails">
                                                    <div v-if="hasUNEBDetails" class="nk-block">
                                                        <div class="nk-block">
                                                            <div class="nk-data">
                                                                <div class="table-responsive">
                                                                    <!-- Candidates Details Tables -->
                                                                    <candidate-details-table :candidates="learner.candidates" />
                                                                    <!-- PLE, UCE, UACE Details Tables -->
                                                                    <uneb-details-table v-if="learner.ple_uneb !== null" :data="learner.ple_uneb" title="PLE Details" />
                                                                    <uneb-details-table v-if="learner.uce_uneb !== null" :data="learner.uce_uneb" title="UCE Details" />
                                                                    <uneb-details-table v-if="learner.uace_uneb !== null" :data="learner.uace_uneb" title="UACE Details" />
                                                                </div>
                                                            </div>
                                                        </div><!-- .nk-block -->
                                                    </div><!-- .nk-block -->
                                                    <div v-else class="alert alert-secondary alert-icon">
                                                        <em class="icon ni ni-alert-circle"></em> This learner has no UNEB details attached
                                                    </div>
                                                </div>
                                                <!-- Equated codes Table -->
                                                <uneb-equated-code-details-table :school-type-id="schoolTypeId" :learner="learner" />
                                                <!-- NIRA Details Table -->
                                                <nira-details-table :learner="learner"/>
                                            </div>

                                            <!-- Education Information -->
                                            <!--                                            <div class="nk-block mt-3">-->
                                            <!--                                                <div class="card h-100">-->
                                            <!--                                                    <div class="card-inner border-bottom">-->
                                            <!--                                                        <div class="card-title-group">-->
                                            <!--                                                            <div class="card-title">-->
                                            <!--                                                                <h6 class="title">Education Information</h6>-->
                                            <!--                                                            </div>-->
                                            <!--                                                        </div>-->
                                            <!--                                                    </div>-->
                                            <!--                                                    <div class="card-inner">-->
                                            <!--                                                        <div class="timeline">-->
                                            <!--                                                            <ul class="timeline-list overflow-auto h-300px">-->
                                            <!--                                                                <li v-for="enrolment in learner.learner_enrolments" class="timeline-item">-->
                                            <!--                                                                    <div class="timeline-status bg-dark-teal"></div>-->
                                            <!--                                                                    <div class="timeline-data">-->
                                            <!--                                                                        <h6 class="timeline-title">{{ enrolment.school.name }} ({{ enrolment.school.emis_number }})</h6>-->
                                            <!--                                                                        <div v-if="enrolment.school.school_type_id <= 5" class="timeline-des">-->
                                            <!--    &lt;!&ndash;                                                                            <p v-if="enrolment.education_grade">{{ enrolment.education_grade.school_type.display_name }}</p>&ndash;&gt;-->
                                            <!--                                                                            <span v-if="enrolment.education_grade !== null" class="time">{{ enrolment.education_grade.name.toUpperCase() }} - ({{ getYear(enrolment.date_created)+" - "+getYear(enrolment.end_date) }})</span>-->
                                            <!--                                                                        </div>-->
                                            <!--                                                                        <div v-if="enrolment.school.school_type_id === 6 && enrolment.course_id !== null" class="timeline-des">-->
                                            <!--                                                                            <p v-if="enrolment.education_grade">{{ enrolment.education_grade.school_type.display_name }}</p>-->
                                            <!--                                                                            <span v-if="enrolment.education_grade" class="time">{{ enrolment.school_course.name.toUpperCase() }} | {{ enrolment.education_grade.name.toUpperCase() }} - ({{ getYear(enrolment.date_created)+" - "+getYear(enrolment.end_date) }})</span>-->
                                            <!--                                                                        </div>-->
                                            <!--                                                                        <div v-if="enrolment.school.school_type_id === 7" class="timeline-des">-->
                                            <!--                                                                            <p v-if="enrolment.international_curriculum">CURRICULUM - {{ enrolment.international_curriculum.name }}</p>-->
                                            <!--                                                                            <span  v-if="enrolment.international_education_grade" class="time">{{ enrolment.international_education_grade.name.toUpperCase() }} - ({{ getYear(enrolment.date_created)+" - "+getYear(enrolment.end_date) }})</span>-->
                                            <!--                                                                        </div>-->
                                            <!--                                                                        <div v-if="enrolment.post_primary_institution_course_id !== null" class="timeline-des">-->
                                            <!--                                                                            <span class="time">{{ enrolment.post_primary_institution_course.name.toUpperCase() }} - ({{ getYear(enrolment.date_created)+" - "+getYear(enrolment.end_date) }})</span>-->
                                            <!--                                                                        </div>-->
                                            <!--                                                                        <div v-if="enrolment.institution_examined_course_id !== null" class="timeline-des">-->
                                            <!--                                                                            <span class="time">{{ enrolment.institution_examined_course.name.toUpperCase() }} - ({{ getYear(enrolment.date_created)+" - "+getYear(enrolment.end_date) }})</span>-->
                                            <!--                                                                        </div>-->
                                            <!--                                                                    </div>-->
                                            <!--                                                                </li>-->

                                            <!--                                                                <li v-if="learner.promotions.length" class="timeline-item">-->
                                            <!--                                                                    <div class="timeline-status bg-primary"></div>-->
                                            <!--                                                                    <div class="timeline-data">-->
                                            <!--                                                                        <div v-for="promotion in learner.promotions" class="">-->
                                            <!--                                                                            <div v-if="promotion.school.school_type_id <= 5" class="timeline-des">-->
                                            <!--                                                                                <span class="time mt-2">{{ promotion.education_grade.name.toUpperCase() }} - ({{ getYear(promotion.start_date)+" - "+getYear(promotion.date_created) }})</span>-->
                                            <!--                                                                            </div>-->
                                            <!--                                                                            <div v-if="promotion.school.school_type_id === 7" class="timeline-des">-->
                                            <!--                                                                                <p v-if="promotion.international_curriculum">CURRICULUM - {{ promotion.international_curriculum.name }}</p>-->
                                            <!--                                                                                <span  v-if="promotion.international_education_grade !== null" class="time">{{ promotion.international_education_grade.name.toUpperCase() }} - ({{ getYear(promotion.start_date)+" - "+getYear(promotion.date_created) }})</span>-->
                                            <!--                                                                            </div>-->
                                            <!--                                                                        </div>-->
                                            <!--                                                                    </div>-->
                                            <!--                                                                </li>-->



                                            <!--                                                                <li v-if="learner.transition !== null" class="timeline-item">-->
                                            <!--                                                                    <div  class="timeline-status bg-primary"></div>-->
                                            <!--                                                                    <div v-for="enrolment in learner.learner_enrolments" class="timeline-data">-->
                                            <!--                                                                        <h6 class="timeline-title">{{ learner.transition.school.name }} ({{ learner.transition.school.emis_number }})</h6>-->
                                            <!--                                                                        <div v-if="learner.transition.school.school_type_id <= 5" class="timeline-des">-->
                                            <!--                                                                            <span class="time mt-2">{{ learner.transition.education_grade.name.toUpperCase() }} - ({{ getYear(learner.transition.start_date)+" - "+getYear(enrolment.date_created) }})</span>-->
                                            <!--                                                                        </div>-->
                                            <!--                                                                    </div>-->
                                            <!--                                                                </li>-->

                                            <!--                                                            </ul>-->
                                            <!--                                                        </div>-->
                                            <!--                                                    </div>-->
                                            <!--                                                </div>-->
                                            <!--                                            </div>&lt;!&ndash; .nk-block &ndash;&gt;-->

                                        </div><!-- .card-inner -->
                                    </div><!-- .card-content -->
                                </div><!-- .card-aside-wrap -->

                            </div><!-- .card -->
                        </div>

                    </div><!-- .card-inner -->
                    <div class="tab-pane card-inner card-inner-lg" id="enrollmentHistory">
                        <div class="nk-block-head nk-block-head-lg mt-3">
                            <div class="nk-block-between">
                                <div class="nk-block-head-content">
                                    <h6 v-if="schoolTypeId >= 4" class="nk-block-title">Student Enrolment History</h6>
                                    <h6 v-else class="nk-block-title">Learner Enrolment History</h6>
                                    <div class="nk-block-des">
                                        <p>Enrolment history information.</p>
                                    </div>
                                </div>
                                <div class="nk-block-head-content align-self-start d-lg-none">
                                    <a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                </div>
                            </div>
                        </div><!-- .nk-block-head -->
                        <div class="nk-block">
                            <div class="nk-block-head nk-block-head-line">
                                <h6 v-if="schoolTypeId <= 3" class="title fs-12px overline-title text-base">Current Class</h6>
                                <h6 v-else class="title fs-12px overline-title text-base">Current Year Of Study</h6>
                            </div>
                            <!--							<div v-if="learner.education_level_id === 1" class="d-flex flex-row justify-contents-between w-100">-->
                            <!--								<div v-for="education_grade in education_grades" class="user-card flex-fill d-flex justify-content-center">-->
                            <!--									<div :class="'badge ' + getCurrentClass(education_grade)">-->
                            <!--										<span :class="getCurrentClass(education_grade) === 'bg-gray-100' ? 'text-dark' : 'text-white'">{{ education_grade.name.toUpperCase() }}</span>-->
                            <!--									</div>-->
                            <!--								</div>-->
                            <!--							</div>-->
                            <!--                            <div class="d-flex flex-row justify-contents-between w-100">-->
                            <!--                                <div v-for="education_grade in education_grades" class="user-card flex-fill d-flex justify-content-center">-->
                            <!--                                    <div :class="'user-avatar ' + getCurrentClass(education_grade)">-->
                            <!--                                        <span :class="getCurrentClass(education_grade) === 'bg-gray-100' ? 'text-dark' : 'text-white'">{{ education_grade.name.toUpperCase() }}</span>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <!--                            <div class="nk-block-head nk-block-head-line mt-5">-->
                            <!--                                <h6 v-if="levelObj !== 'tertiary'" class="title fs-12px overline-title text-base">Enrolment By Term</h6>-->
                            <!--                                <h6 v-else class="title fs-12px overline-title text-base">Enrolment By Semester</h6>-->
                            <!--                            </div>-->
                            <!--                            <div class="table-responsive w-100">-->
                            <!--                                <table class="table table-bordered">-->
                            <!--                                    <thead>-->
                            <!--                                    <tr class="bg-gray-300">-->
                            <!--                                        <th v-if="levelObj !== 'tertiary'" style="vertical-align: middle;" rowspan="2" class="rounded-0 text-dark ucap">Term</th>-->
                            <!--                                        <th v-else style="vertical-align: middle;" rowspan="2" class="rounded-0 text-dark ucap">Semester</th>-->
                            <!--                                        <th v-if="levelObj !== 'tertiary'" :colspan="education_grades.length" class="rounded-0 text-dark text-center ucap">Class</th>-->
                            <!--                                        <th v-else :colspan="education_grades.length" class="rounded-0 text-dark text-center ucap">Year Of Study</th>-->
                            <!--                                    </tr>-->
                            <!--                                    <tr class="bg-gray-100">-->
                            <!--                                        <th v-for="education_grade in education_grades" class="rounded-0 text-dark text-center ucap">{{ education_grade.name.toUpperCase() }}</th>-->
                            <!--                                    </tr>-->
                            <!--                                    </thead>-->
                            <!--                                    <tbody>-->
                            <!--                                    <tr>-->
                            <!--                                        <td class="text-dark ucap">One</td>-->
                            <!--                                        <td v-for="education_grade in education_grades" class="text-dark text-center" v-html="getEnrolmentByTerm(education_grade)"></td>-->
                            <!--                                    </tr>-->
                            <!--                                    <tr>-->
                            <!--                                        <td class="text-dark ucap">Two</td>-->
                            <!--                                        <td v-for="education_grade in education_grades" class="text-dark text-center" v-html="getEnrolmentByTerm(education_grade)"></td>-->
                            <!--                                    </tr>-->
                            <!--                                    <tr>-->
                            <!--                                        <td class="text-dark ucap">Three</td>-->
                            <!--                                        <td v-for="education_grade in education_grades" class="text-dark text-center" v-html="getEnrolmentByTerm(education_grade)"></td>-->
                            <!--                                    </tr>-->
                            <!--                                    </tbody>-->
                            <!--                                </table>-->
                            <!--                            </div>-->
                            <div class="nk-block-head nk-block-head-line mt-5">
                                <h6 class="title fs-12px overline-title text-base">Enrolment By School</h6>
                            </div>
                            <!--                            <div class="table-responsive w-100">-->
                            <!--                                <table class="table table-bordered">-->
                            <!--                                    <thead>-->
                            <!--                                    <tr class="bg-gray-300">-->
                            <!--                                        <th style="vertical-align: middle;" rowspan="2" class="rounded-0 text-dark ucap">School</th>-->
                            <!--                                        <th style="vertical-align: middle;" rowspan="2" class="rounded-0 text-dark ucap">Date</th>-->
                            <!--                                        <th v-if="levelObj !== 'tertiary'" style="vertical-align: middle;" rowspan="2" class="rounded-0 text-dark ucap">Term</th>-->
                            <!--                                        <th v-else style="vertical-align: middle;" rowspan="2" class="rounded-0 text-dark ucap">Semester</th>-->
                            <!--                                        <th v-if="levelObj !== 'tertiary'" :colspan="education_grades.length" class="rounded-0 text-dark text-center ucap">Class</th>-->
                            <!--                                        <th v-else :colspan="education_grades.length" class="rounded-0 text-dark text-center ucap">Year Of Study</th>-->
                            <!--                                    </tr>-->
                            <!--                                    <tr class="bg-gray-100">-->
                            <!--                                        <th v-for="education_grade in education_grades" class="rounded-0 text-dark text-center ucap">{{ education_grade.name.toUpperCase() }}</th>-->
                            <!--                                    </tr>-->
                            <!--                                    </thead>-->
                            <!--                                    <tbody v-for="enrolment in learner.learner_enrolments">-->
                            <!--                                    <tr>-->
                            <!--                                        <td style="vertical-align: top;" rowspan="3" class="text-dark ucap">-->
                            <!--                                            {{ enrolment.school.emis_number }} - {{ enrolment.school.name }}-->
                            <!--                                        </td>-->
                            <!--                                        <td style="vertical-align: top;" rowspan="3" class="text-dark ucap">-->
                            <!--                                            {{ formatDate(enrolment.start_date) }}-->
                            <!--                                        </td>-->
                            <!--                                        <td class="text-dark ucap">One</td>-->
                            <!--                                        <td v-for="education_grade in education_grades" class="text-dark text-center" v-html="getEnrolmentByTerm(education_grade)"></td>-->
                            <!--                                    </tr>-->
                            <!--                                    <tr>-->
                            <!--                                        <td class="text-dark ucap">Two</td>-->
                            <!--                                        <td v-for="education_grade in education_grades" class="text-dark text-center" v-html="getEnrolmentByTerm(education_grade)"></td>-->
                            <!--                                    </tr>-->
                            <!--                                    <tr>-->
                            <!--                                        <td class="text-dark ucap">Three</td>-->
                            <!--                                        <td v-for="education_grade in education_grades" class="text-dark text-center" v-html="getEnrolmentByTerm(education_grade)"></td>-->
                            <!--                                    </tr>-->
                            <!--                                    </tbody>-->
                            <!--                                </table>-->
                            <!--                            </div>-->
                        </div>
                    </div>

                    <!-- parents -->
                    <div class="tab-pane card-inner card-inner-lg" id="parentsGuardian">
                        <parents ref="parents" :learner-obj="learner"></parents>
                    </div>
                    <div class="tab-pane card-inner card-inner-lg" id="specialNeeds">
                        <special-needs ref="specialNeeds" :learner-obj="learner"></special-needs>
                    </div>
                    <div class="tab-pane card-inner card-inner-lg" id="TalentInformation">
                        <talents ref="talents" :learner-obj="learner"></talents>
                    </div>
                    <div class="tab-pane card-inner card-inner-lg" id="healthInformation">
                        <health-issues ref="healthIssues" :learner-obj="learner"></health-issues>
                    </div>
                    <div v-if="schoolTypeId === 1 || schoolTypeId === 2" class="tab-pane card-inner card-inner-lg" id="familiarLanguages">
                        <familiar-languages ref="familiarLanguages" :learner-obj="learner"></familiar-languages>
                    </div>
                    <div v-if="schoolTypeId === 2 || schoolTypeId === 3" class="tab-pane card-inner card-inner-lg" id="practicalSkills">
                        <learner-practical-skills ref="practicalSkills" :learner-obj="learner"></learner-practical-skills>
                    </div>
                    <div v-if="schoolTypeId === 3 && aLevel" class="tab-pane card-inner card-inner-lg" id="subjectsInformation">
                        <learner-subjects ref="subjects" :learner-obj="learner"></learner-subjects>
                    </div>

                    <div :class="{ 'modal': true, 'fade': !showEditModal, 'show': showEditModal }" :style="{ 'display': showEditModal ? 'block' : 'none' }" tabindex="-1" id="updateLearnerInfoModal">
                        <error-notifications ref="notifyError"></error-notifications>
                        <form @submit.prevent="submitForm()">
                            <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
                                <div class="modal-content">
                                    <a @click="resetLearnerInfo()" class="cursor close" data-dismiss="modal" aria-label="Close">
                                        <em class="icon ni ni-cross"></em>
                                    </a>
                                    <div class="modal-header">
                                        <h5 class="modal-title">Update Information</h5>
                                    </div>
                                    <div class="modal-body">

                                        <div class="row">
                                            <div class="col-lg-4 col-md-12">
                                                <div class="form-group d-flex flex-column justify-content-center">
                                                    <label class="align-self-center form-label">Add Photo</label>
                                                    <input
                                                        :disabled="learnerNinYes || learner_verify"
                                                        ref="photo" @change="selectFile"
                                                        accept="image/x-png,image/jpeg"
                                                        data-max-file-size="2M"
                                                        id="learnerEditWithPhoto"
                                                        type="file"
                                                        class="dropify"
                                                        data-height="190"
                                                        data-allowed-file-extensions="jpeg jpg png"
                                                        :data-default-file="learner.person.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg'" />
                                                </div>
                                                <div class="d-flex flex-column justify-content-center">
                                                    <button :disabled="learnerNinYes || learner_verify" @click="form_learner.photo === null || form_learner.photo === '' ? uploadImage() : clearPhoto()" type="button" class="align-self-center btn btn-primary">
                                                        <em class="icon ni ni-camera-fill"></em>
                                                        <span v-if="form_learner.photo === null || form_learner.photo === ''">Choose Photo</span>
                                                        <span v-else>Remove Photo</span>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-lg-8 col-md-12 pb-4 overflow-auto scrollbar-dark-teal h-425px">
                                                <h6 class="overline-title title text-dark-teal">LEARNER DETAILS</h6>
                                                <div class="row mt-3">
                                                    <div class="col-lg-6">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label class="form-label">Does this learner have a NIN?</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6">
                                                        <div class="form-group">
                                                            <div class="custom-control custom-control-inline custom-radio">
                                                                <input disabled="" type="radio" class="custom-control-input" v-model="learner_nin" value="yes" id="learnerWithNIN">
                                                                <!-- <input :disabled="learner_verify || verify" type="radio" class="custom-control-input" v-model="learner_nin" value="yes" id="learnerWithNIN"> -->

                                                                <label class="custom-control-label text-uppercase" for="learnerWithNIN">YES</label>
                                                            </div>
                                                            <div class="custom-control custom-control-inline custom-radio">
                                                                <input disabled="" type="radio" class="custom-control-input" v-model="learner_nin" value="no" id="learnerWithoutNIN">
                                                                <!-- <input :disabled="learner_verify || verify" type="radio" class="custom-control-input" v-model="learner_nin" value="no" id="learnerWithoutNIN"> -->

                                                                <label class="custom-control-label text-uppercase" for="learnerWithoutNIN">NO</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-show="uganda && learner_nin === 'yes'" class="row mt-3">
                                                    <div class="col-lg-12 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="learnerNIN" class="form-label">Enter Learner NIN</label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <div class="input-group">
                                                                    <input v-model.trim="form_learner.nin" id="learnerNIN" minlength="14" maxlength="14" type="text" placeholder="eg. CM0011****4455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                                                    <div class="input-group-append">
                                                                        <button @click="learnerVerifyNIN()" :disabled="form_learner.nin === '' || saveLoading || loading" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                            <span v-if="loading" class="align-self-center">Verifying...</span>
                                                                            <span v-if="loading" class="sr-only">Verifying...</span>
                                                                            <span v-if="!loading" class="">Verify NIN</span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-show="schoolTypeId === 3" class="row mt-3">
                                                    <div class="col-lg-6 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Select Class <span class="text-danger">*</span></label>
                                                            <select :required="schoolTypeId === 3" id="learnerSecEducationGradeId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="grade in education_grades" :value="grade.id">{{ grade.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="learner_nin === 'no' && !learner.uneb_equated_data" class="col-lg-6 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Select Year When Learner Sat {{ aLevel ? "UCE" : "PLE" }} Exams <span class="text-danger">*</span></label>
                                                            <select disabled="" :required="schoolTypeId === 3 && !learner.uneb_equated_data" id="learnerExamYearId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="year in academicYears" :value="year">{{ year }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="learner_nin === 'no' && learner.uneb_equated_data" class="col-lg-6 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Select Equated Year <span class="text-danger">*</span></label>
                                                            <select disabled="" :required="schoolTypeId === 3 && learner.uneb_equated_data" id="learnerEquatedYearId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="year in academicYears" :value="year">{{ year }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="!aLevel && learner_nin === 'no' && !learner.uneb_equated_data" class="col-lg-12 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="learnerPleIndexNumber" class="form-label">PLE Index Number</label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <div class="input-group">
                                                                    <input disabled="" :required="!aLevel && schoolTypeId === 3 && !learner.uneb_equated_data" v-model.trim="form_learner.index_number" id="learnerPleIndexNumber" minlength="10" maxlength="10" type="text" placeholder="eg. 012345/012" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                    <div class="input-group-append">
                                                                        <button disabled="" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                            <span v-if="loading" class="align-self-center">Verifying...</span>
                                                                            <span v-if="loading" class="sr-only">Verifying...</span>
                                                                            <span v-if="!loading" class="">Verify</span>
                                                                        </button>
                                                                        <!-- <button :disabled="form_learner.exam_year === '' || form_learner.index_number === '' || loading || verify" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                            <span v-if="loading" class="align-self-center">Verifying...</span>
                                                                            <span v-if="loading" class="sr-only">Verifying...</span>
                                                                            <span v-if="!loading" class="">Verify</span>
                                                                        </button> -->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-show="!aLevel && learner_nin === 'no' && learner.uneb_equated_data" class="col-lg-12 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="learnerEquatedCode" class="form-label">Equated Code <span class="text-danger">*</span></label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <div class="input-group">
                                                                    <input disabled="" :required="!aLevel && schoolTypeId === 3 && learner.uneb_equated_data" v-model.trim="form_learner.equated_code" id="learnerEquatedCode" minlength="10" maxlength="10" type="text" placeholder="eg. SSD/O/0009" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                    <div class="input-group-append">
                                                                        <button disabled="" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                            <span v-if="loading" class="align-self-center">Verifying...</span>
                                                                            <span v-if="loading" class="sr-only">Verifying...</span>
                                                                            <span v-if="!loading" class="">Verify</span>
                                                                        </button>

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-show="aLevel && learner_nin === 'no'" class="col-lg-12 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="learnerUceIndexNumber" class="form-label">UCE Index Number</label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <div class="input-group">
                                                                    <input disabled="" :required="aLevel && schoolTypeId === 3" v-model.trim="form_learner.index_number" id="learnerUceIndexNumber" minlength="9" maxlength="9" type="text" placeholder="eg. U0123/012" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                    <div class="input-group-append">
                                                                        <button disabled="" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                            <span v-if="loading" class="align-self-center">Verifying...</span>
                                                                            <span v-if="loading" class="sr-only">Verifying...</span>
                                                                            <span v-if="!loading" class="">Verify</span>
                                                                        </button>
                                                                        <!-- <button :disabled="form_learner.exam_year === '' || form_learner.index_number === '' || loading || verify" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                            <span v-if="loading" class="align-self-center">Verifying...</span>
                                                                            <span v-if="loading" class="sr-only">Verifying...</span>
                                                                            <span v-if="!loading" class="">Verify</span>
                                                                        </button> -->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-show="verify" class="row mt-3">
                                                    <div class="col-lg-12 d-flex">
                                                        <div class="table-responsive align-self-end">
                                                            <table class="table table-sm">
                                                                <tbody>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-dark text-uppercase">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">LEARNER</h6>
                                                                        <span class="">{{ uneb_learner.name }}</span>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-dark text-uppercase">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">INDEX NUMBER</h6>
                                                                        <span class="">{{ uneb_learner.index_number }}</span>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-dark text-uppercase">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">EXAM YEAR</h6>
                                                                        <span class="">{{ uneb_learner.exam_year }}</span>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-dark text-uppercase">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                                        <span v-if="uneb_learner.gender === 'F'" class="">FEMALE</span>
                                                                        <span v-if="uneb_learner.gender === 'M'" class="">MALE</span>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-dark text-uppercase">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                                        <span class="">{{ formatDate(uneb_learner.date_of_birth) }}</span>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-dark text-uppercase">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">NATIONALITY</h6>
                                                                        <span class="">{{ uneb_learner.nationality }}</span>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-show="uganda && learner_verify && nira_learner.national_id !== ''" class="row">
                                                    <div class="table-responsive py-3">
                                                        <table class="table table-sm table-hover">
                                                            <tr>
                                                                <td rowspan="5" class="align-middle text-uppercase text-dark text-center w-175px">
                                                                    <div class="user-card">
                                                                        <div class="w-150px">
                                                                            <img id="learnerPhoto" src="@images/default_male.jpg" class="rounded-0" alt="contact person photo">
                                                                        </div>
                                                                    </div><!-- .user-card -->
                                                                </td>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                                                    <span class=""> {{ nira_learner.national_id }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">SURNAME</h6>
                                                                    <span class="">{{ nira_learner.surname }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">GIVEN NAME</h6>
                                                                    <span class="">{{ nira_learner.given_names }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                                    <span class="">{{ nira_learner.gender }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                                    <span class="">{{ nira_learner.date_of_birth }}</span>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-lg-6 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="learnerFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <input :disabled="learnerNinYes || learner_verify || indexNumber" :required="learner_nin === 'no'" v-model.trim="form_learner.first_name" id="learnerFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="learnerSurname" class="form-label">Surname <span class="text-danger">*</span></label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <input :disabled="learnerNinYes || learner_verify || indexNumber" :required="learner_nin === 'no'" v-model.trim="form_learner.surname" id="learnerSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-lg-6 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="learnerOtherNames" class="form-label">Other Names</label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <input :disabled="learnerNinYes || learner_verify || indexNumber" v-model.trim="form_learner.other_names" id="learnerOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="learnerBirthDate">Date Of Birth</label>
                                                            <div class="form-control-wrap">
                                                                <div class="form-icon form-icon-left">
                                                                    <em class="icon ni ni-calendar"></em>
                                                                </div>
                                                                <input disabled="" :required="learner_nin === 'no'" v-model.trim="form_learner.birth_date" placeholder="eg. 14 APRIL, 2022" id="learnerBirthDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                <!-- <input :disabled="learnerNinYes || learner_verify || indexNumber" :required="learner_nin === 'no'" v-model.trim="form_learner.birth_date" placeholder="eg. 14 APRIL, 2022" id="learnerBirthDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off"> -->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-lg-6 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label class="form-label">Sex</label>
                                                            </div>
                                                            <div class="form-group">
                                                                <div class="custom-control custom-control-inline custom-radio">
                                                                    <input disabled="" @change="updateDefaultPhoto()" type="radio" class="custom-control-input" v-model="form_learner.gender" value="M" id="learnerMale">
                                                                    <!-- <input :disabled="learnerNinYes || learner_verify || indexNumber" @change="updateDefaultPhoto()" type="radio" class="custom-control-input" v-model="form_learner.gender" value="M" id="learnerMale"> -->
                                                                    <label class="custom-control-label text-uppercase" for="learnerMale">Male</label>
                                                                </div>
                                                                <div class="custom-control custom-control-inline custom-radio">
                                                                    <input disabled="" @change="updateDefaultPhoto()" type="radio" class="custom-control-input" v-model="form_learner.gender" value="F" id="learnerFemale">
                                                                    <!-- <input :disabled="learnerNinYes || learner_verify || indexNumber" @change="updateDefaultPhoto()" type="radio" class="custom-control-input" v-model="form_learner.gender" value="F" id="learnerFemale"> -->
                                                                    <label class="custom-control-label text-uppercase" for="learnerFemale">Female</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 mt-lg-0 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Learner Nationality <span class="text-danger">*</span></label>
                                                            <select disabled="" :required="learner_nin === 'no'" id="learnerCountryId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                                            </select>
                                                            <!-- <select :disabled="learnerNinYes || learner_verify || indexNumber" :required="learner_nin === 'no'" id="learnerCountryId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                                            </select> -->
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row my-3">
                                                    <div class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label class="form-label">Is learner an orphan?</label>
                                                            </div>
                                                            <div class="form-group">
                                                                <div class="custom-control custom-control-inline custom-radio">
                                                                    <input type="radio" class="custom-control-input" v-model="form_learner.is_orphan" value="yes" id="learnerIsOrphanYes">
                                                                    <label class="custom-control-label text-uppercase" for="learnerIsOrphanYes">YES</label>
                                                                </div>
                                                                <div class="custom-control custom-control-inline custom-radio">
                                                                    <input type="radio" class="custom-control-input" v-model="form_learner.is_orphan" value="no" id="learnerIsOrphanNo">
                                                                    <label class="custom-control-label text-uppercase" for="learnerIsOrphanNo">NO</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-show="form_learner.is_orphan === 'yes'" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-group">
                                                                <label class="form-label">Type Of Orphan <span class="text-danger">*</span></label>
                                                                <select :required="form_learner.is_orphan === 'yes'" id="learnerOrphanType" class="form-select-sm">
                                                                    <option value="">--SELECT--</option>
                                                                    <option value="father">ONLY FATHER DEAD</option>
                                                                    <option value="mother">ONLY MOTHER DEAD</option>
                                                                    <option value="both-dead">BOTH PARENTS DEAD</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-show="schoolTypeId === 7" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Calendar <span class="text-danger">*</span></label>
                                                            <select :required="schoolTypeId === 7" id="learnerCalendarId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="calendar in learner.school.international_calendars" :value="calendar.id">{{ calendar.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="schoolTypeId === 7" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Curriculum <span class="text-danger">*</span></label>
                                                            <select :required="schoolTypeId === 7" disabled="" id="learnerCurriculumId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="curriculum in learner.school.international_curriculums" :value="curriculum.id">{{ curriculum.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="schoolTypeId === 7" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Class <span class="text-danger">*</span></label>
                                                            <select :required="schoolTypeId === 7" disabled="" id="learnerInterSchEducationGradeId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="schoolTypeId !== 7 && schoolTypeId !== 3" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label v-show="schoolTypeId <= 3" class="form-label">Class <span class="text-danger">*</span></label>
                                                            <label v-show="schoolTypeId >= 4" class="form-label">Year <span class="text-danger">*</span></label>
                                                            <select :required="schoolTypeId !== 7 && schoolTypeId !== 3" id="learnerEducationGradeId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="education_grade in education_grades" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="schoolTypeId === 6" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Course <span class="text-danger">*</span></label>
                                                            <select  :required="schoolTypeId === 6" id="learnerCourseId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="course in course_list" :value="course.id">{{ course.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="schoolTypeId === 6" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Sponsor <span class="text-danger">*</span></label>
                                                            <select :required="schoolTypeId === 6" id="learnerSponsorId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="sponsor in learner_sponsors" :value="sponsor.id">{{ sponsor.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="schoolTypeId === 6" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Mode Of Entry <span class="text-danger">*</span></label>
                                                            <select :required="schoolTypeId === 6" id="learnerEntryModeId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="mode in entry_modes" :value="mode.id">{{ mode.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="uganda && !learner.uneb_equated_data" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">District Of Birth <span class="text-danger">*</span></label>
                                                            <select :required="uganda && !learner.uneb_equated_data" id="learnerDistrictOfBirthId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="district in districts" :value="district.id">{{ district.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="schoolTypeId === 1 || schoolTypeId === 2" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label">Familiar Language <span class="text-danger">*</span></label>
                                                            <select :required="schoolTypeId === 1 || schoolTypeId === 2" id="learnerFamiliarLanguageId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="familiar_language in familiar_languages" :value="familiar_language.id">{{ familiar_language.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div v-show="schoolTypeId === 4 || schoolTypeId === 5" class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label class="form-label">Is student offering an examinable course?</label>
                                                            </div>
                                                            <div class="form-group">
                                                                <div class="custom-control custom-control-inline custom-radio">
                                                                    <input type="radio" class="custom-control-input" v-model="form_learner.is_offering_examinable_course" value="yes" id="learnerExaminableCourseYes">
                                                                    <label class="custom-control-label text-uppercase" for="learnerExaminableCourseYes">YES</label>
                                                                </div>
                                                                <div class="custom-control custom-control-inline custom-radio">
                                                                    <input type="radio" class="custom-control-input" v-model="form_learner.is_offering_examinable_course" value="no" id="learnerExaminableCourseNo">
                                                                    <label class="custom-control-label text-uppercase" for="learnerExaminableCourseNo">NO</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-show="form_learner.is_offering_examinable_course === 'yes'" class="col-lg-6 mt-3">
                                                        <div v-show="schoolTypeId === 4 || schoolTypeId === 5" class="form-group">
                                                            <div class="form-group">
                                                                <label class="form-label">Examinable Course <span class="text-danger">*</span></label>
                                                                <select :required="form_learner.is_offering_examinable_course === 'yes' && schoolTypeId === 4 && schoolTypeId === 5" id="learnerExaminableCourseId" class="form-select-sm">
                                                                    <option value="">--SELECT--</option>
                                                                    <option v-if="schoolTypeId === 4 && learner.school.certificate_institution_courses !== null" v-for="course in learner.school.certificate_institution_courses" :value="course.post_primary_institution_course_id">{{ course.post_primary_institution_course.name.toUpperCase() }}</option>
                                                                    <option v-if="schoolTypeId === 5 && learner.school.diploma_institution_courses !== null" v-for="course in learner.school.diploma_institution_courses" :value="course.post_primary_institution_course_id">{{ course.post_primary_institution_course.name.toUpperCase() }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-show="form_learner.is_offering_examinable_course === 'no'" class="col-lg-6 mt-3">
                                                        <div v-show="schoolTypeId === 4 || schoolTypeId === 5" class="form-group">
                                                            <div class="form-group">
                                                                <label class="form-label">Non Examinable Course <span class="text-danger">*</span></label>
                                                                <select :required="form_learner.is_offering_examinable_course === 'no' && schoolTypeId === 4 && schoolTypeId === 5" id="learnerNonExaminableCourseId" class="form-select-sm">
                                                                    <option value="">--SELECT--</option>
                                                                    <option v-for="course in learner.school.institution_examined_courses" :value="course.id">{{ course.name.toUpperCase() }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr class="border-dark-teal my-4">
                                                <h6 class="overline-title title text-dark-teal">SPECIAL NEEDS</h6>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div v-for="disability in disability_types" class="custom-control custom-control-sm custom-checkbox d-block">
                                                                <input v-model="form_learner.special_needs" type="checkbox" :value="disability.id" class="custom-control-input" :id="'learnerDisability'+disability.id">
                                                                <label class="custom-control-label" :for="'learnerDisability'+disability.id">{{ disability.name.toUpperCase() }}</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr class="border-dark-teal my-4">
                                                <h6 class="overline-title title text-dark-teal">HEALTH ISSUES</h6>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div v-for="issue in health_issues" class="custom-control custom-control-sm custom-checkbox d-block">
                                                                <input v-model="form_learner.health_issues" type="checkbox" :value="issue.id" class="custom-control-input" :id="'learnerHealthIssue'+issue.id">
                                                                <label class="custom-control-label" :for="'learnerHealthIssue'+issue.id">{{ issue.name.toUpperCase() }}</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr class="border-dark-teal my-4">
                                                <h6 class="overline-title title text-dark-teal">TALENTS</h6>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div v-for="talent in talents" class="custom-control custom-control-sm custom-checkbox d-block">
                                                                <input v-model="form_learner.talents" type="checkbox" :value="talent.id" class="custom-control-input" :id="'learnerTalent'+talent.id">
                                                                <label class="custom-control-label" :for="'learnerTalent'+talent.id">{{ talent.name.toUpperCase() }}</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr v-show="schoolTypeId === 2 || schoolTypeId === 3" class="border-dark-teal my-4">
                                                <h6 v-show="schoolTypeId === 2 || schoolTypeId === 3" class="overline-title title text-dark-teal">PRACTICAL SKILLS</h6>
                                                <div v-show="schoolTypeId === 2 || schoolTypeId === 3" class="row">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div v-for="skill in practical_skills" class="custom-control custom-control-sm custom-checkbox d-block">
                                                                <input v-model="form_learner.practical_skills" type="checkbox" :value="skill.id" class="custom-control-input" :id="'learnerPracticalSkill'+skill.id">
                                                                <label class="custom-control-label" :for="'learnerPracticalSkill'+skill.id">{{ skill.name.toUpperCase() }}</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr v-show="schoolTypeId === 3 && aLevel" class="border-dark-teal my-4">
                                                <h6 v-show="schoolTypeId === 3 && aLevel" class="overline-title title text-dark-teal">Subject Combination</h6>
                                                <div class="row">
                                                    <div v-if="schoolTypeId === 3 && aLevel" class="col-12">
                                                        <div class="form-group">
                                                            <div v-for="subject in principalSubjects" class="custom-control custom-control-sm custom-checkbox d-block">
                                                                <input :disabled="getPrincipalSubjectDisabled(subject)" v-model="form_learner.principal_subjects" type="checkbox" :value="subject.id" class="custom-control-input" :id="'learnerSubject'+subject.id">
                                                                <label class="custom-control-label" :for="'learnerSubject'+subject.id">{{ subject.name.toUpperCase() }}</label>
                                                            </div>
                                                            <hr class="border-dark-teal my-1">
                                                            <div v-for="subject in subsidiarySubjects" class="custom-control custom-control-sm custom-checkbox d-block">
                                                                <input :disabled="getSubSubjectDisabled(subject)" v-model="form_learner.subsidiary_subject" type="checkbox" :value="subject.id" class="custom-control-input" :id="'learnerSubjectSub'+subject.id">
                                                                <label class="custom-control-label" :for="'learnerSubjectSub'+subject.id">{{ subject.name.toUpperCase() }}</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="d-flex p-3 border-top border-dark-teal justify-content-center">
                                        <button @click="resetLearnerInfo()" :disabled="saveLoading" type="button" data-dismiss="modal" class="btn btn-light me-2">
                                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                                        </button>
                                        <button :disabled="saveLoading" type="submit" class="btn bg-dark-teal d-flex">
                                            <span v-if="saveLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span v-if="saveLoading" class="align-self-center">Saving...</span>
                                            <span v-if="saveLoading" class="sr-only">Saving...</span>
                                            <span v-if="!saveLoading" class="align-self-center">Update</span><em v-if="!saveLoading" class="ni ni-arrow-right ml-2"></em>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div><!-- end modal-->
                </div>
            </div>
        </div><!-- .nk-block -->

        <!-- Delete learner modal -->
        <!-- <div class="modal fade zoom" tabindex="-1" id="deleteModal">
            <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetLearnerDelete()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="deleteLearner()">
                        <div class="modal-header">
                            <h6 class="modal-title">Would You Like To Delete  {{ learner.person.full_name}}</h6>
                        </div>
                        <div class="modal-body">
                                                   <error-notifications ref="notifyErrorNIN"></error-notifications>
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row g-4">
                                 <div class="col-lg-3 d-flex">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label class="form-label text-dark-teal"></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12 d-flex">
                                    <div class="form-group">
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input" v-model="learner_delete" value="yes" id="learnerDeleteYes">
                                            <label class="custom-control-label text-uppercase" for="learnerDeleteYes">YES</label>
                                        </div>
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input" v-model="learner_delete" value="no" id="learnerDeleteNo">
                                            <label class="custom-control-label text-uppercase" for="learnerDeleteNo">NO</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-show="learner_delete === 'yes'" class="row g-4">
                                <div class="col-12">
                                    <div class="row g-4 align-self-center">
                                        <div v-show="learner_delete === 'yes'" class="col-lg-12">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">Select Reason <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <select id="learnerDeleteReasonId" class="form-select-sm" required>
                                                        <option value="">--SELECT--</option>
                                                        <option v-for="reason in deletion_reasons" :value="reason.id">{{ reason.name.toUpperCase() }}</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row g-4 align-self-center">
                                        <div v-show="learner_delete === 'yes'" class="col-lg-12">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">Description <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <textarea v-model.trim="form_delete_learner.description" required maxlength="100" type="text" placeholder="Briefly describe why you would want this learner to be deleted in not more than 100 characters!" class="form-control form-text" autocomplete="off"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div v-show="learner_delete === 'yes'" class="nk-kycfm-action pt-5 row">
                                <div class="col-lg-6">
                                    <button @click="resetLearnerDelete()" :disabled="loading" type="button" class="btn btn-secondary btn-block text-center d-flex">
                                        <span class="align-self-center text-uppercase">CANCEL</span>
                                    </button>
                                </div>
                                <div class="col-lg-6">
                                    <button :disabled="loading" type="submit" class="btn bg-dark-teal text-center btn-block d-flex">
                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span v-if="loading" class="align-self-center">Submitting...</span>
                                        <span v-if="loading" class="sr-only">Loading...</span>
                                        <span v-if="!loading" class="align-self-center text-uppercase">Submit</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                    </button>
                                </div>
                            </div>

                        </div>
                    </form>

                </div>
            </div>
        </div> -->
        <!-- Loading Modal -->

        <!-- Loading Modal -->
        <div style="display:none;" id="loadingMessage" class="card card-preview">
            <div class="card-inner">
                <div class="d-flex align-items-center">
                    <strong>{{ loading_message }}...</strong>
                    <div class="spinner-border ml-auto" role="status" aria-hidden="true"></div>
                </div>
            </div>
        </div>
        <!-- /Loading Modal -->
    </div>
</template>

<script>
import ErrorNotifications from "../../Notifications.vue";
import SuccessNotifications from "../../Notifications.vue";

export default {
    name: "Profile",
    props: [
        'learnerObj',
        'subjectsObj',
        'schoolTypeIdObj',
        'educationGradesObj',
        'courseListObj',
        'learnerSponsorsObj',
        'entryModesObj',
        'identityTypesObj',
        'disabilitiesObj',
        'practicalSkillsObj',
        'healthIssuesObj',
        'familiarLanguagesObj',
        'districtsObj',
        'countriesObj',
        'talentsObj',
        'deletionReasonsObj'
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            saveLoading: false,
            loading_message: "",
            loading: false,
            verify: false,
            nin_verify: false,
            deleteLoading: false,
            api_url: '/institutions/learners/',
            learner_nin: "no",
            learner_delete: "no",
            learner_verify: false,
            uganda: true,
            aLevel: false,
            photoDropify: null,
            showEditModal: false,
            edit: false,
            countries: [],
            districts: [],
            education_grades: [],
            entry_modes: [],
            learner_sponsors: [],
            course_list: [],
            familiar_languages: [],
            disability_types: [],
            health_issues: [],
            talents: [],
            practical_skills: [],
            grades: [],
            deletion_reasons: [],
            subjects: [],
            schoolTypeId: {},
            age: 0,
            current_lin: '',
            education_level_id: '',
            learner: {
                school: {
                    certificate_institution_courses: [],
                    institution_examined_courses: [],
                    diploma_institution_courses: [],
                    international_curriculums: [],
                    international_calendars: [],
                },
                district_of_birth: {
                    name: ''
                },
                candidates: [],
                identities: [],
                index_numbers: [],
                pre_primary_extra_curricular_activities: [],
                primary_extra_curricular_activities: [],
                secondary_extra_curricular_activities: [],
                post_primary_extra_curricular_activities: [],
                tertiary_extra_curricular_activities: [],
                learner_enrolments: [],
                promotions: [],
                contacts: [],
                addresses: [],
                person: {
                    first_name: '',
                    surname: '',
                    other_names: '',
                    gender: 'M',
                    photo: null,
                    birth_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                    country_id: 221,
                    country: {
                        name: ''
                    },
                    current_id: {
                        identity_type: {
                            name: ''
                        },
                    },
                    nira_person: {
                        national_id: '',
                        surname: '',
                        given_names: '',
                        date_of_birth: '',
                        gender: '',
                        nationality: '',
                    },
                },
                current_education_grade_id: '',
                inter_sch_curriculum_id: '',
                current_post_primary_institution_course_id: '',
                current_institution_examined_course_id: '',
                education_level_id: '',
                sponsor_id: '',
                entry_mode_id: '',
                course_id: '',
                familiar_language_id: '',
                is_orphan: 'no',
                orphan_type: '',
                education_grade: {name: ''},
                international_education_grade: {name: ''},
                photo_url: '',
                candidate: {
                    name: '',
                    date_of_birth: '',
                    gender: '',
                    nationality: '',
                    district: '',
                    index_number: '',
                    exam_year: '',
                    exam_level: '',
                },
                ple_uneb: {
                    name: '',
                    date_of_birth: '',
                    gender: '',
                    nationality: '',
                    district: '',
                    index_number: '',
                    exam_year: '',
                    exam_level: '',
                },
                uce_uneb: {
                    name: '',
                    date_of_birth: '',
                    gender: '',
                    nationality: '',
                    district: '',
                    index_number: '',
                    exam_year: '',
                    exam_level: '',
                },
                uace_uneb: {
                    name: '',
                    date_of_birth: '',
                    gender: '',
                    nationality: '',
                    district: '',
                    index_number: '',
                    exam_year: '',
                    exam_level: '',
                },
                uneb_equated_data: {
                    learner_id: '',
                    education_grade_id: '',
                    code_type: '',
                    equated_code: '',
                    equated_year: '',
                    exam_year: '',
                    exam_level: '',
                },
            },
            form_learner: {
                nin: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                index_number: '',
                exam_year: '',
                equated_year: '',
                equated_code: '',
                birth_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                country_id: 221,
                current_education_grade_id: '',
                inter_sch_education_grade_id: '',
                inter_sch_curriculum_id: '',
                sponsor_id: '',
                entry_mode_id: '',
                course_id: '',
                post_primary_institution_course_id: '',
                institution_examined_course_id: '',
                is_offering_examinable_course: 'yes',
                familiar_language_id: '',
                is_orphan: 'no',
                orphan_type: '',
                photo_url: '',
                special_needs: [],
                talents: [],
                health_issues: [],
                practical_skills: [],
                principal_subjects: [],
                subsidiary_subject: [],
            },
            uneb_learner: {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                index_number: '',
                gender: '',
                date_of_birth: '',
                name: '',
                exam_level: '',
                exam_year: '',
            },
            nira_learner: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            form_delete_learner: {
                reason_id: '',
                description: '',
            }
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.learner = this.learnerObj;
            this.schoolTypeId = this.schoolTypeIdObj;
            this.countries = this.countriesObj;
            this.education_grades = this.educationGradesObj;
            this.learner_sponsors = this.learnerSponsorsObj;
            this.course_list = this.courseListObj;
            this.entry_modes = this.entryModesObj;
            this.familiar_languages = this.familiarLanguagesObj;
            this.disability_types = this.disabilitiesObj;
            this.districts = this.districtsObj;
            this.health_issues = this.healthIssuesObj;
            this.talents = this.talentsObj;
            this.practical_skills = this.practicalSkillsObj;
            this.deletion_reasons = this.deletionReasonsObj;
            this.current_lin = this.learner.lin;
            this.subjects = this.subjectsObj;

            $('#learnerBirthDate').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().subtract(1, 'years').toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.form_learner.birth_date = moment(e.date).format("D MMMM, YYYY");
            });

            $('#learnerCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.uganda = data.text === 'UGANDA';
                    self.form_learner.country_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#learnerCalendarId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.inter_sch_calendar_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerCurriculumId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.inter_sch_curriculum_id = data.id !== "" ? Number(data.id) : data.id;
                    self.loadEducationGrades();
                    return data.text;
                },
            });
            $('#learnerInterSchEducationGradeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.inter_sch_education_grade_id = data.id.length > 0 ? Number(data.id) : "";
                    self.updateEducationLevel();
                    return data.text;
                },
            });

            $('#learnerEducationGradeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.current_education_grade_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });
            $('#learnerExamYearId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.exam_year = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerEquatedYearId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.equated_year = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerSecEducationGradeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.current_education_grade_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#learnerDistrictOfBirthId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.district_of_birth_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#learnerOrphanType').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.orphan_type = data.id;
                    if (self.form_learner.is_orphan === 'no') {
                        self.form_learner.orphan_type = '';
                    }
                    return data.text;
                },
            });

            $('#learnerFamiliarLanguageId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.familiar_language_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#learnerExaminableCourseId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.post_primary_institution_course_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerNonExaminableCourseId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.institution_examined_course_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#learnerCourseId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.course_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerSponsorId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.sponsor_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerEntryModeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateLearnerInfoModal'),
                templateSelection: function (data, container) {
                    self.form_learner.entry_mode_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#learnerDeleteReasonId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#deleteModal'),
                templateSelection: function (data, container) {
                    self.form_delete_learner.reason_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            //init is alevel learner
            this.aLevel = this.aLevelClasses.find(grade=>{return grade.id === Number(this.learner.current_education_grade_id)}) !== undefined;

            this.fillCheckboxes();

            this.photoDropify = $('#learnerEditWithPhoto').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Oops, something wrong uploaded.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max allowed).'
                }
            });
            self.photoDropify.on('dropify.afterClear', function(event, element){
                self.form_learner.photo = null;
                self.$refs.photo.value=null;
            });
            $('.dropify-clear').click(()=>{
                self.clearPhoto();
            });

            this.age = Math.floor(moment().diff(this.learner.person.birth_date, 'years', true));
            this.learner.age = this.getAge(this.learner.person.birth_date);

            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let english = this.familiar_languages.find(language => {
                    return language.name.includes('ENGLISH');
                });
                $('#learnerCountryId').val(ug.id).change();
                $('#learnerFamiliarLanguageId').val(english.id).change();
            }, 50);

            //Delay edit modal
            window.setTimeout(()=>{
                this.checkEditQueryParam(this.learner);
            }, 60);
        },
        selectFile() {
            this.form_learner.photo = this.$refs.photo.files[0];
        },

        uploadImage: function () {
            $('.dropify').click();
        },
        clearPhoto: function () {
            let fileDropper = this.photoDropify.data('dropify');
            fileDropper.resetPreview();
            fileDropper.clearElement();
            fileDropper.settings['defaultFile'] = this.form_learner.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg';
            fileDropper.destroy();
            fileDropper.init();
            this.learner.person.photo = null;
        },
        updateDefaultPhoto: function () {
            if (this.form_learner.photo === null) {
                this.clearPhoto();
            } else {
                let fileDropper = this.photoDropify.data('dropify');
                fileDropper.resetPreview();
                fileDropper.clearElement();
                fileDropper.settings['defaultFile'] = '/images/nira-photos/'+ this.form_learner.photo;
                fileDropper.destroy();
                fileDropper.init();
            }
        },
        getPrincipalSubjectDisabled: function (subject) {
            return this.form_learner.principal_subjects.length >= 3 && !this.form_learner.principal_subjects.includes(subject.id);
        },
        getSubSubjectDisabled: function (subject) {
            return this.form_learner.subsidiary_subject.length >= 1 && !this.form_learner.subsidiary_subject.includes(subject.id);
        },
        verifyUnebLearner: function () {
            this.loading = true;
            let apiUrl = '';
            if (this.aLevel) {
                apiUrl = '/uneb/uce-learner-info';
            } else {
                apiUrl = '/uneb/ple-learner-info';
            }
            axios.post(apiUrl, {index_number: this.form_learner.index_number, exam_year: this.form_learner.exam_year})
                .then(response => {
                    this.loading = false;
                    this.uneb_learner = response.data;
                    this.form_learner.first_name = this.uneb_learner.first_name;
                    this.form_learner.surname = this.uneb_learner.surname;
                    this.form_learner.gender = this.uneb_learner.gender;
                    this.form_learner.birth_date = moment(this.uneb_learner.date_of_birth).format('D MMMM, YYYY').toUpperCase();
                    this.uganda = this.uneb_learner.nationality === "UGANDA";
                    this.verify = true;
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        learnerVerifyNIN: function () {
            this.loading = true;
            axios.post('/nira/user-info', {id_number: this.form_learner.nin.toUpperCase()})
                .then(response => {
                    this.loading = false;
                    this.uganda = true;
                    this.nira_learner = response.data;
                    this.form_learner.first_name = this.nira_learner.given_names;
                    this.form_learner.surname = this.nira_learner.surname;
                    this.form_learner.gender = this.nira_learner.gender;
                    this.form_learner.birth_date = moment(this.nira_learner.birth_date).format("D MMMM, YYYY");
                    if (this.nira_learner.photo !== null) {
                        if (this.nira_learner.photo.includes('.png')) {
                            $('#learnerPhoto').attr('src', '/images/nira-photos/' + this.nira_learner.photo);
                        } else {
                            $('#learnerPhoto').attr('src', 'data:image/png;base64,' + this.nira_learner.photo);
                        }
                    }
                    this.learner_verify = true;
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        checkEditQueryParam: function(learner) {
            const urlParams = new URLSearchParams(window.location.search);
            // Check if the edit=true parameter is present
            if (urlParams.has('edit') && urlParams.get('edit') === 'true') {
                this.showEditModal = true;
                this.editLearner(learner);
                window.addEventListener('popstate', this.handlePopState);
            }
        },
        handlePopState: function() {
            this.showEditModal = false;
            // Remove the event listener to avoid memory leaks
            window.removeEventListener('popstate', this.handlePopState);
        },
        editLearner: function (learner) {
            this.edit = true;
            this.learner.id = learner.id;
            this.form_learner.first_name = learner.person.first_name;
            this.form_learner.surname = learner.person.surname;
            this.form_learner.other_names = learner.person.other_names;
            this.form_learner.gender = learner.person.gender;
            if (this.schoolTypeId === 3 && this.indexNumber !== null) {
                this.form_learner.index_number = this.indexNumber.index_number;
                $('#learnerExamYearId').val(this.indexNumber.exam_year).change();
            }
            if (this.schoolTypeId === 3 && this.learner.uneb_equated_data !== null) {
                this.form_learner.equated_code = this.learner.uneb_equated_data.equated_code;
                $('#learnerEquatedYearId').val(this.learner.uneb_equated_data.equated_year).change();
            }
            $('#learnerBirthDate').datepicker('setDate', moment(learner.person.birth_date).toDate());
            this.form_learner.birth_date = moment(learner.person.birth_date).format('D MMMM, YYYY');
            $('#learnerCountryId').val(learner.person.country_id).change();
            $('#learnerEducationGradeId').val(learner.current_education_grade_id).change();
            $('#learnerSecEducationGradeId').val(learner.current_education_grade_id).change();
            $('#learnerCalendarId').val(learner.inter_sch_calendar_id).change();
            $('#learnerCurriculumId').val(learner.inter_sch_curriculum_id).change();
            $('#learnerInterSchEducationGradeId').val(learner.inter_sch_education_grade_id).change();
            $('#learnerDistrictOfBirthId').val(learner.district_of_birth_id).change();
            $('#learnerExaminableCourseId').val(learner.current_post_primary_institution_course_id).change();
            $('#learnerNonExaminableCourseId').val(learner.current_institution_examined_course_id).change();

            if(learner.current_post_primary_institution_course_id) {
                this.form_learner.is_offering_examinable_course = 'yes';
            } else {
                this.form_learner.is_offering_examinable_course = 'no';
            }

            let currentEnrolment = this.learner.learner_enrolments.find(item => {
                return item.end_date === null && item.school_id === this.learner.school.id;
            });

            if (currentEnrolment !== undefined) {
                $('#learnerFamiliarLanguageId').val(currentEnrolment.familiar_language_id).change();
                $('#learnerCourseId').val(currentEnrolment.course_id).change();
                $('#learnerSponsorId').val(currentEnrolment.sponsor_id).change();
                $('#learnerEntryModeId').val(currentEnrolment.entry_mode_id).change();

                if (learner.is_father_dead === true && learner.is_mother_dead === true) {
                    this.form_learner.is_orphan = 'yes';
                    this.form_learner.orphan_type = 'both-dead';
                } else if (learner.is_father_dead === true) {
                    this.form_learner.is_orphan = 'yes';
                    this.form_learner.orphan_type = 'father';
                } else if (learner.is_mother_dead === true) {
                    this.form_learner.is_orphan = 'yes';
                    this.form_learner.orphan_type = 'mother';
                } else {
                    this.form_learner.is_orphan = 'no';
                    this.form_learner.orphan_type = '';
                }
                $('#learnerOrphanType').val(this.form_learner.orphan_type).trigger('change');
            }
            this.resetPhoto();
            this.fillCheckboxes();
            //$('#updateLearnerInfoModal').modal({backdrop: "static"});
        },

        deleteLearner: function () {
            let self = this;
            Swal.fire({
                title: 'Are you sure?',
                text: "You are about to flag "+self.learner.person.full_name+" for deleting",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, Flag!',
            }).then(function (result) {
                if (result.value) {
                    self.startLoading('Flagging Learner');
                    axios.post(self.api_url+'delete/'+self.learner.lin, self.form_delete_learner)
                        .then(()=>{
                            window.location.href = '/institution/data-update/flagged-learners-for-deleting';
                        })
                        .catch(error=>{
                            $.unblockUI();
                            self.renderError(error)
                        });
                }
            });
        },

        submitForm() {
            // if (this.form_learner.nin.length) {
            //     return this.learner_verify ? this.updateLearnerInfo() : this.learnerVerifyNIN();
            // }
            // if (this.form_learner.index_number.length) {
            //     return this.verify ?  this.updateLearnerInfo() : this.verifyUnebLearner();
            // }
            return this.learner_verify ? this.learnerVerifyNIN() : this.updateLearnerInfo();
            // if (this.form_learner.nin !== '') {
            //     return this.learner_verify ? this.updateLearnerInfo() : this.learnerVerifyNIN();
            // }
            // return this.learner_verify ? this.learnerVerifyNIN() : this.updateLearnerInfo();
        },

        loadEducationGrades: function () {
            let self = this;
            // clean grades
            this.grades = [];
            let select = $("#learnerInterSchEducationGradeId");
            select.empty().trigger('change');
            let newOption = new Option("--SELECT--", "", false, false);
            select.append(newOption).trigger('change');
            self.form_learner.inter_sch_education_grade_id = "";

            //load new options
            if (self.form_learner.inter_sch_curriculum_id !== "") {
                self.grades = self.learner.school.international_curriculums.find(curriculum=>{
                    return curriculum.id === self.form_learner.inter_sch_curriculum_id
                }).grades;

                self.grades.forEach(grade=>{
                    let gradeOption = new Option(grade.name, grade.id, false, false);
                    select.append(gradeOption).trigger('change');
                });
            }
        },
        updateEducationLevel: function () {
            let education_grade = this.grades.find(grade=> {
                return grade.id === this.form_learner.inter_sch_education_grade_id;
            })
            this.education_level_id = education_grade === undefined ? '' : education_grade.inter_sch_education_level_id;
        },
        updateLearnerInfo: function () {
            this.saveLoading = true;
            let formData = new FormData();
            let self = this;
            if (moment(this.form_learner.birth_date).isAfter(moment())) {
                self.saveLoading = false;
                self.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error: ',
                    message:'This learner has not yet been born. Provide an accurate date of birth!'
                });
            } else if (this.schoolTypeId === 3 && this.aLevel && !self.form_learner.principal_subjects.length) {
                self.saveLoading = false;
                self.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error: ',
                    message: 'Select at least 3 principal subjects to proceed!'
                });
            } else if (this.schoolTypeId === 3 && this.aLevel && !self.form_learner.subsidiary_subject.length) {
                self.saveLoading = false;
                self.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error: ',
                    message: 'Select a subsidiary subject to proceed!'
                });
            }
            else if (this.schoolTypeId === 1 && moment(this.form_learner.birth_date).isAfter(moment().subtract(1, 'year'))) {
                self.saveLoading = false;
                this.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error: ',
                    message: 'Learner must be at least 1 year to be updated!'
                });
            }
            else if (this.schoolTypeId === 2 && moment(this.form_learner.birth_date).isAfter(moment().subtract(4, 'year'))) {
                self.saveLoading = false;
                this.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error: ',
                    message: 'Learner must be at least 4 years to be updated!'
                });
            }
            else if (this.schoolTypeId === 3 && moment(this.form_learner.birth_date).isAfter(moment().subtract(11, 'year'))) {
                self.saveLoading = false;
                this.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error: ',
                    message: 'Learner must be at least 11 years to be updated!'
                });
            }
            else if (this.schoolTypeId === 4 && moment(this.form_learner.birth_date).isAfter(moment().subtract(14, 'year'))) {
                self.saveLoading = false;
                this.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error: ',
                    message: 'Learner must be at least 14 years to be updated!'
                });
            }
            else if (this.schoolTypeId >= 5 && this.schoolTypeId !== 7  && moment(this.form_learner.birth_date).isAfter(moment().subtract(18, 'year'))) {
                self.saveLoading = false;
                this.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error: ',
                    message: 'Learner must be at least 18 years to be updated!'
                });
            }
            else if (this.schoolTypeId === 7 && moment(this.form_learner.birth_date).isAfter(moment().subtract(1, 'year'))) {
                self.saveLoading = false;
                this.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error!',
                    message: 'Learner must be at least 1 year to be updated!'
                });
            }
            else {
                self.saveLoading = true;
                //checking if learner has an id number
                if (self.learner.person.id_number !== null && self.form_learner.nin === '' || self.form_learner.student_pass === '') {
                    self.id_available = self.learner.person.id_number
                } else {
                    self.id_available =  self.form_learner.nin;
                    //self.id_available =  self.form_learner.student_pass;
                }
                formData.append('nin', (self.uganda ? self.id_available : ''));
                formData.append('learner_nin_verify', self.learner_verify);
                formData.append('first_name', self.form_learner.first_name);
                formData.append('surname', self.form_learner.surname);
                formData.append('other_names', self.form_learner.other_names ?? '');
                // formData.append('birth_date', self.form_learner.birth_date);
                formData.append('gender', self.form_learner.gender);
                formData.append('photo', self.form_learner.photo);
                formData.append('education_grade_id', self.form_learner.current_education_grade_id);
                formData.append('inter_sch_education_grade_id', self.form_learner.inter_sch_education_grade_id);
                formData.append('inter_sch_calendar_id', self.form_learner.inter_sch_calendar_id);
                formData.append('inter_sch_curriculum_id', self.form_learner.inter_sch_curriculum_id);
                formData.append('inter_sch_education_level_id', self.education_level_id);
                formData.append('familiar_language_id', self.form_learner.familiar_language_id);
                formData.append('district_of_birth_id', self.form_learner.district_of_birth_id);
                formData.append('orphan_type', self.form_learner.orphan_type);
                formData.append('disabilities', self.form_learner.special_needs);
                formData.append('learner_health_issues', self.form_learner.health_issues);
                formData.append('learner_practical_skills', self.form_learner.practical_skills);
                formData.append('learner_talents', self.form_learner.talents);
                formData.append('country_id', self.form_learner.country_id);
                formData.append('is_offering_examinable_course', self.form_learner.is_offering_examinable_course);
                formData.append('post_primary_institution_course_id', self.form_learner.post_primary_institution_course_id = 'yes' ? self.form_learner.post_primary_institution_course_id : '');
                formData.append('institution_examined_course_id', self.form_learner.institution_examined_course_id = 'no' ? self.form_learner.institution_examined_course_id : '');
                formData.append('sponsor_id', self.form_learner.sponsor_id);
                formData.append('course_id', self.form_learner.course_id);
                formData.append('entry_mode_id', self.form_learner.entry_mode_id);
                formData.append('index_number', self.form_learner.index_number);
                formData.append('exam_year', self.form_learner.exam_year);
                formData.append('uneb_id', self.uneb_learner.id);
                formData.append('uneb_level', self.aLevel ? "UCE" : "PLE");
                formData.append('principal_subjects', self.form_learner.principal_subjects);
                formData.append('subsidiary_subject', self.form_learner.subsidiary_subject);

                axios.post(self.api_url + 'update-profile/' + this.learner.person.id, formData, {headers: {'Content-Type': 'multipart/form-data'}})
                    .then(response => {
                        self.learner = response.data;
                        self.$refs.specialNeeds.learner = self.learner;
                        self.$refs.healthIssues.learner = self.learner;
                        self.$refs.talents.learner = self.learner;
                        if (self.schoolTypeId === 2 || self.schoolTypeId === 3) {
                            self.$refs.practicalSkills.learner = self.learner;
                        }
                        if (self.schoolTypeId === 1 || self.schoolTypeId === 2) {
                            self.$refs.familiarLanguages.learner = self.learner;
                        }
                        if (self.schoolTypeId === 3 && self.aLevel) {
                            self.$refs.subjects.learner = self.learner;
                        }
                        self.$refs.notifySuccess.messages.push({
                            status: 'success',
                            title: 'Success: ',
                            message: 'Learner Updated Successfully'
                        });
                        if (self.current_lin !== response.data.lin) {
                            window.location.href = "/institution/learners/profile/"+response.data.lin.toLowerCase()+"?edit=true";
                        }
                        self.resetLearnerInfo();
                    })
                    .catch(error => {
                        self.saveLoading = false;
                        self.renderError(error);
                    });

            }
        },
        getTransferInfo(transfer) {
            if (transfer.education_grade !== null) {
                return `${transfer.education_grade.name.toUpperCase()} - (${this.getYear(transfer.start_date)} - ${this.getYear(transfer.date_created)})`;
            } else if (transfer.learner !== null) {
                return `${transfer.learner.education_grade.name.toUpperCase()} - (${this.getYear(transfer.start_date)} - ${this.getYear(transfer.date_created)})`;
            }
            return ''; // Handle other cases as needed
        },
        getYear: function (enrolment_date) {
            if (enrolment_date === null) {
                return "Now";
            }
            return moment(enrolment_date).format("D MMMM, YYYY");
        },
        getAge: function (birth_date) {
            return moment().diff(birth_date, 'years', false);
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        fillCheckboxes: function () {
            //special needs
            this.form_learner.special_needs = [];
            this.learner.learner_disabilities.forEach(item => {
                this.form_learner.special_needs.push(item.disability_type_id);
            });
            //health issues
            this.form_learner.health_issues = [];
            this.learner.learner_health_issues.forEach(item => {
                this.form_learner.health_issues.push(item.health_issue_id);
            });
            //talents
            this.form_learner.talents = [];
            this.learner.learner_talents.forEach(item => {
                this.form_learner.talents.push(item.talent_id);
            });
            //practical skills
            this.form_learner.practical_skills = [];
            this.learner.learner_practical_skills.forEach(item => {
                this.form_learner.practical_skills.push(item.practical_skill_id);
            });

            //principal_subjects
            this.form_learner.principal_subjects = [];
            //subsidiary_subject
            this.form_learner.subsidiary_subject = [];
            if (this.schoolTypeId === 3) {
                this.learner.learner_secondary_school_subjects.forEach(item => {
                    if (item.subject.is_principal_subject) {
                        this.form_learner.principal_subjects.push(item.subject_id);
                    } else {
                        this.form_learner.subsidiary_subject.push(item.subject_id);
                    }
                });
            }
        },
        resetPhoto: function () {
            let filedropper = this.photoDropify.data('dropify');
            filedropper.resetPreview();
            filedropper.clearElement();
            filedropper.settings['defaultFile'] = this.learner.person.photo_url;
            filedropper.destroy();
            filedropper.init();
        },
        resetLearnerInfo: function () {
            this.showEditModal = false;
            //$('#updateLearnerInfoModal').modal('hide');
            this.saveLoading = false;
            this.loading = false;
            this.verify = false;
            this.learner_nin = "no";
            this.learner_verify = false;
            this.uganda = true;
            this.fillCheckboxes();
            this.form_learner.nin = '';
            this.nira_learner = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
            this.uneb_learner = {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                index_number: '',
                gender: '',
                date_of_birth: '',
                name: '',
                exam_level: '',
                exam_year: '',
            };

            this.$refs.notifyError.messages = [];
            // this.$refs.notifySuccess.messages = [];
            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let english = this.familiar_languages.find(language => {
                    return language.name.includes('ENGLISH');
                });
                $('#learnerCountryId').val(this.form_learner.country_id).change();
                $('#learnerOrphanType').val(this.form_learner.orphan_type).change();
                $('#learnerEducationGradeId').val(this.form_learner.current_education_grade_id).change();
                $('#learnerExamYearId').val(this.form_learner.exam_year).change();
                $('#learnerSecEducationGradeId').val(this.form_learner.current_education_grade_id).change();
                $('#learnerInterSchEducationGradeId').val(this.form_learner.inter_sch_education_grade_id).change();
                $('#learnerCalendarId').val(this.form_learner.inter_sch_calendar_id).change();
                $('#learnerCurriculumId').val(this.form_learner.inter_sch_curriculum_id).change();
                $('#learnerDistrictOfBirthId').val(this.form_learner.district_of_birth_id).change();
                $('#learnerFamiliarLanguageId').val(this.form_learner.familiar_language_id).change();
                $('#learnerBirthDate').datepicker('setDate', moment(this.form_learner.birth_date).toDate());
                $('#learnerExaminableCourseId').val(this.form_learner.post_primary_institution_course_id).change();
                $('#learnerNonExaminableCourseId').val(this.form_learner.institution_examined_course_id).change();
                $('#learnerCourseId').val(this.form_learner.course_id).change();
                $('#learnerSponsorId').val(this.form_learner.sponsor_id).change();
                $('#learnerEntryModeId').val(this.form_learner.entry_mode_id).change();
                this.clearPhoto();
            }, 50);
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
        // resetLearnerDelete: function () {
        //     $('#deleteModal').modal('hide');
        //     this.saveLoading = false;
        //     this.loading = false;
        //     this.verify = false;
        //     this.learner_delete = "no";
        //     this.form_delete_learner.description = '';

        //     this.$refs.notifyError.messages = [];
        //     // this.$refs.notifySuccess.messages = [];
        //     window.setTimeout(()=>{
        //         $('#learnerDeleteReasonId').val(this.form_delete_learner.reason_id).change();
        //     }, 50);
        //     $("html, body").animate({ scrollTop: 0 }, "slow");
        // },
        startLoading: function(message) {
            if (message.length) {
                this.loading_message = message;
                $.blockUI({
                    message: $('#loadingMessage'),
                    css: {
                        padding:0,
                        margin:0,
                        width:'30%',
                        top:'40%',
                        left:'35%',
                        textAlign:'center',
                        color:'#364a63',
                        wordWrap: 'break-word',
                        backgroundColor: '#fff',
                        backgroundClip: 'border-box',
                        border: '0 solid rgba(0, 0, 0, 0.125)',
                        borderRadius: '4px',
                        cursor:'wait'
                    },
                });
            }
        },
        renderError: function (error) {
            const notifyError = (title, message) => {
                if (this.modal) {
                    this.$refs.modalNotify.messages.push({ status: 'error', title, message });
                } else {
                    this.$refs.notifyError.messages.push({ status: 'error', title, message });
                }
            };

            if (error.response) {
                const { status, data } = error.response;
                switch (status) {
                    case 500:
                    case 405:
                        notifyError('System Error: ', data.message);
                        break;
                    case 401:
                        notifyError('Permission Error: ', 'You are not authorised to perform this action');
                        break;
                    case 404:
                        notifyError('Resource Not Found: ', 'You are trying to reach a URL that does not exist');
                        break;
                    case 422:
                        for (let field in data.errors) {
                            this.showError(data.errors[field]);
                        }
                        break;
                    default:
                        notifyError('Other Error: ', error.message);
                }
            } else {
                notifyError('Other Error: ', error.message);
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
        },
    },
    computed: {
        academicYears: function () {
            let years = [];

            for (let i = Number(moment().format("YYYY"))-1; i >= 2015; i--) {
                years.push(i);
            }

            return years;
        },
        indexNumber: function () {
            let number = null;

            if (this.learner.index_numbers !== undefined && this.learner.index_numbers.length && this.schoolTypeId === 3) {
                this.learner.index_numbers.sort((a,b) => (a.exam_year > b.exam_year) ? 1 : ((b.exam_year > a.exam_year) ? -1 : 0));
                number = this.learner.index_numbers[this.learner.index_numbers.length - 1];
            }
            return number;
        },
        learnerIndexNumbers: function () {
            let numbers = [];

            if (this.learner.index_numbers && this.learner.index_numbers.length && this.schoolTypeId === 3) {
                // Filter out undefined or null values and sort index numbers by exam year
                const validIndexNumbers = this.learner.index_numbers.filter(index => index && typeof index === 'object' && index.index_number);
                validIndexNumbers.sort((a, b) => (a.exam_year > b.exam_year) ? 1 : ((b.exam_year > a.exam_year) ? -1 : 0));

                // Extract index numbers
                numbers = validIndexNumbers.map(index => ({
                    level: index.level,
                    index_number: index.index_number,
                    exam_year: index.exam_year
                }));
            }
            return numbers;
        },
        hasUNEBDetails: function() {
            return this.learner.candidates && this.learner.candidates.length > 0 ||
                this.learner.ple_uneb !== null ||
                this.learner.uce_uneb !== null ||
                this.learner.uace_uneb !== null;
        },
        showUnebDetails: function() {
            return (
                    (this.schoolTypeId === 2 && this.learner.current_education_grade_id === 11) ||
                    this.schoolTypeId > 2
                ) &&
                this.schoolTypeId !== 7 &&
                !this.learner.uneb_equated_data;
        },
        learnerNinYes: function () {
            return this.learner.person.identity_type_id === 1;
        },
        aLevelClasses: function () {
            return this.education_grades.filter(grade => {
                return grade.grade_rank > 4;
            });
        },
        hasTransfers() {
            return this.learner.learner_enrolments.some(enrolment => enrolment.transfer !== null);
        },
        hasTransfersWithGrade() {
            return this.learner.learner_enrolments.some(
                enrolment => enrolment.transfer !== null && enrolment.transfer.education_grade_id !== null
            );
        },
        hasTransfersWithoutGrade() {
            return this.learner.learner_enrolments.some(
                enrolment => enrolment.transfer !== null && enrolment.transfer.education_grade_id === null
            );
        },
        subsidiarySubjects: function () {
            return this.subjects.filter(subject=>{
                return !subject.is_principal_subject && subject.name.startsWith('SUB');
            });
        },
        principalSubjects: function () {
            return this.subjects.filter(subject=>{
                return subject.is_principal_subject;
            });
        },
    }
}
</script>

<style scoped>
.user-avatar {
    border-radius: 5%;
    width: 80% ;
}
.vertical-line {
    border-left: 1px solid black;
    height: 100%;
    position: absolute;
    left: 50%; /* Adjust this value to position the line */
    transform: translateX(-50%);
}

/* Styles for the modal */
.modal {
    display: none;
    /* position: fixed; */
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal.fade .modal-dialog {
    transform: translate(0, -50%);
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: translate(0, 0);
    transition: transform 0.3s ease-out;
}
</style>
