<template>
    <div class="w-100">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="table-responsive d-flex">
            <table class="table table-sm table-hover table-bordered border-dark-teal mx-auto">
                <thead>
                    <tr class="bg-secondary">
                        <td class="border-secondary border-bottom-0 border-right-0 text-white text-uppercase rounded-0">Download File</td>
                        <td class="border-white border-bottom-0 text-white text-uppercase">Date</td>
                        <td class="border-white border-bottom-0 text-white text-uppercase text-center">Success</td>
                        <td class="border-white border-bottom-0 text-white text-uppercase text-center">Failed</td>
                        <td class="border-secondary border-bottom-0 text-white text-uppercase text-center rounded-0">Status</td>
                        <td class="border-secondary border-bottom-0 text-white text-uppercase text-center rounded-0">Actions</td>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="fileUpload in file_uploads.data" :key="fileUpload.id">
                        <td class="text-dark border-secondary">
                            <span @click="downloadExcel(fileUpload)" class="text-dark-teal cursor">{{ fileUpload.file_name }}</span>
                        </td>
                        <td class="text-dark text-uppercase border-secondary">{{ formatDate(fileUpload.file_name) }}</td>
                        <td class="text-dark text-uppercase border-secondary text-center">{{ fileUpload.successful_records || 0 }}</td>
                        <td class="text-dark text-uppercase border-secondary text-center">{{ fileUpload.failed_records || 0 }}</td>
                        <td class="text-dark text-lowercase border-secondary text-center">
                            <div class="badge-container">
                                <span v-if="fileUpload.status === 'failed'" class="badge bg-danger cursor">
                                    <i class="fas fa-exclamation-triangle"></i> failed
                                </span>
                                <span v-else-if="fileUpload.status === 'pending'" class="badge bg-gray cursor">
                                    <i class="fas fa-hourglass-start"></i> pending
                                </span>
                                <span v-else-if="fileUpload.status === 'processing'" class="badge bg-primary cursor">
                                    <i class="fas fa-spinner"></i> processing
                                </span>
                                <span v-else-if="fileUpload.status === 'complete'" class="badge bg-success cursor">
                                    <i class="fas fa-check-circle"></i> complete
                                </span>
                                <span v-else class="badge badge-secondary cursor">
                                    unknown
                                </span>
                            </div>
                        </td>
                        <td class="text-dark text-lowercase border-secondary text-center">
                            <a :href="'/institution/file-uploads/details/' + fileUpload.id" class="text-muted" v-tooltip="'View file details'">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../Notifications.vue";
import SuccessNotifications from "../../Notifications.vue";
import moment from 'moment';

export default {
    name: "FileUploads",
    props: ['fileUploadsObj'],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    data() {
        return {
            file_uploads: { current_page: -1, data: [], links: [], total: 0 },
            statuses: ['failed', 'pending', 'processing', 'complete'],
        };
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins() {
            this.file_uploads = this.fileUploadsObj;
            this.file_uploads.data.forEach(fileUpload => {
                fileUpload.status = this.randomStatus();
            });
        },
        formatDate(fileName) {
            const match = fileName.match(/_(\d+)\./);
            if (match) {
                const timestamp = parseInt(match[1], 10);
                return moment.unix(timestamp).format('YYYY-MM-DD');
            }
            return '';
        },
        randomStatus() {
            return this.statuses[Math.floor(Math.random() * this.statuses.length)];
        },
        downloadExcel(fileUpload) {
            axios.get(`${fileUpload.file_url}?x=${moment().unix()}`, { responseType: 'arraybuffer' })
                .then(response => {
                    const blob = new Blob([response.data], { type: 'application/octet-stream' });
                    const a = document.createElement('a');
                    a.href = URL.createObjectURL(blob);
                    a.download = fileUpload.file_name;
                    document.body.appendChild(a);
                    a.click();
                    URL.revokeObjectURL(a.href);
                    document.body.removeChild(a);
                })
                .catch(this.renderError);
        },
        renderError(error) {
            if (error.response) {
                if (error.response.status === 500 || error.response.status === 405) {
                    this.$refs.notifyError.messages.push({ status: 'error', title: 'System Error:', message: error.response.data.message });
                } else if (error.response.status === 401) {
                    this.$refs.notifyError.messages.push({ status: 'error', title: 'Permission Error:', message: 'You are not authorised to perform this action' });
                }
            } else {
                this.$refs.notifyError.messages.push({ status: 'error', title: 'Network Error:', message: 'Network Error! Please check your internet connection and try again' });
            }
        },
    }
};
</script>

<style scoped>
.cursor {
    cursor: pointer;
}
.badge-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px; /* Reduced width */
    padding: 3px 8px; /* Smaller padding */
    margin-right: 5px;
    font-size: 0.75rem; /* Smaller font size */
}

.badge i {
    margin-right: 4px; /* Adjusted spacing between icon and text */
}

.badge-success {
    background-color: var(--success);
    color: var(--white);
}

.badge-dark-red {
    background-color: var(--red);
    color: var(--white);
}

.badge-dark-yellow {
    background-color: var(--gray);
    color: var(--white);
}

.badge-dark-blue {
    background-color: var(--blue);
    color: var(--white);
}

.badge-dark-gray {
    background-color: var(--gray);
    color: var(--white);
}
.text-lowercase {
    text-transform: lowercase;
}
.table td {
    border: 1px solid #dee2e6;
}
.table thead td {
    border-bottom: 0;
}
.table .actions {
    text-align: center;
}
</style>
