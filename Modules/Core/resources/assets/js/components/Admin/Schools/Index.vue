<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <notifications ref="notifyError"></notifications>
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Manage {{ schoolLabel }}</h3>
                    <nav class="nk-block-des">
                        <ul class="breadcrumb breadcrumb-arrow">
                            <li class="breadcrumb-item">
                                <a :href="'/admin/dashboard'" class="text-primary">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a v-if="schoolTypeIdObj === 1" :href="'/admin/manage-institutions/view-pre-primary-schools'" class="text-primary">{{ schoolLabel }}</a>
                                <a v-if="schoolTypeIdObj === 2" :href="'/admin/manage-institutions/view-primary-schools'" class="text-primary">{{ schoolLabel }}</a>
                                <a v-if="schoolTypeIdObj === 3" :href="'/admin/manage-institutions/view-secondary-schools'" class="text-primary">{{ schoolLabel }}</a>
                                <a v-if="schoolTypeIdObj === 4" :href="'/admin/manage-institutions/view-certificate-schools'" class="text-primary">{{ schoolLabel }}</a>
                                <a v-if="schoolTypeIdObj === 5" :href="'/admin/manage-institutions/view-diploma-schools'" class="text-primary">{{ schoolLabel }}</a>
                                <a v-if="schoolTypeIdObj === 6" :href="'/admin/manage-institutions/view-degree-schools'" class="text-primary">{{ schoolLabel }}</a>
                                <a v-if="schoolTypeIdObj === 7" :href="'/admin/manage-institutions/view-international-schools'" class="text-primary">{{ schoolLabel }}</a>
                            </li>
                            <li class="breadcrumb-item active text-soft">
                                Manage
                            </li>
                        </ul>
                    </nav>
                    <div class="nk-block-des text-soft">
                        <p v-if="schoolTypeIdObj > 3 && schoolTypeIdObj !== 7">Total Institutions: {{ schools.total }}</p>
                        <p v-else>Total Schools: {{ schools.total }}</p>
                    </div>
                </div><!-- .nk-block-head-content -->

                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                        <div v-if="userRoleObj" class="toggle-expand-content" data-content="pageMenu">
                            <ul class="nk-block-tools g-3">
                                <li v-if="hasPermission('institutions-update-operation-status')" class="nk-block-tools-opt">
                                    <button :disabled="loading || selected_schools.length === 0" @click="showOperationalStatusModal" type="button" class="btn btn-primary">
                                        <em class="icon ni ni-setting"></em>
                                        <span>Update Operation Status</span>
                                    </button>
                                </li>
                                <li v-if="hasPermission('institutions-update-ownership-status')" class="nk-block-tools-opt">
                                    <button :disabled="loading || selected_schools.length === 0" @click="showOwnershipStatusModal" type="button" class="btn btn-primary">
                                        <em class="icon ni ni-setting"></em>
                                        <span>Update Ownership Status</span>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner-group">
                    <div class="p-3">
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="form-control-wrap">
                                    <select id="district_id" class="form-select form-control form-control-xl">
                                        <option value="">ALL DISTRICTS</option>
                                        <option v-for="district in districts" :value="district.id">{{ district.name.toUpperCase() }}</option>
                                    </select>
                                    <label class="form-label-outlined emis-label" for="district_id">Select District</label>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-control-wrap">
                                    <select id="county_id" class="form-select form-control form-control-xl">
                                        <option value="">ALL COUNTIES</option>
                                        <option v-for="county in counties" :value="county.id">{{ county.name.toUpperCase() }}</option>
                                    </select>
                                    <label class="form-label-outlined emis-label" for="county_id">Select County</label>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-control-wrap">
                                    <select id="sub_county_id" class="form-select form-control form-control-xl">
                                        <option value="">ALL SUB-COUNTIES</option>
                                        <option v-for="sub_county in sub_counties" :value="sub_county.id">{{ sub_county.name.toUpperCase() }}</option>
                                    </select>
                                    <label class="form-label-outlined emis-label" for="sub_county_id">Select Sub-County</label>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-control-wrap">
                                    <select id="parish_id" class="form-select form-control form-control-xl">
                                        <option value="">ALL PARISHES</option>
                                        <option v-for="parish in parishes" :value="parish.id">{{ parish.name.toUpperCase() }}</option>
                                    </select>
                                    <label class="form-label-outlined emis-label" for="parish_id">Select Parish</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-lg-2">
                                <div class="form-control-wrap">
                                    <select id="school_ownership_status_id" class="form-select form-control form-control-xl">
                                        <option value="">ALL OWNERSHIP</option>
                                        <option value="1">GOVERNMENT</option>
                                        <option value="2">PRIVATE</option>
                                    </select>
                                    <label class="form-label-outlined emis-label" for="school_ownership_status_id">Select Ownership</label>
                                </div>
                            </div>
                            <div class="col-lg-2">
                                <div class="form-control-wrap">
                                    <select id="school_operational_status_id" class="form-select form-control form-control-xl">
                                        <option value="">ALL OPERATION STATUS</option>
                                        <option v-for="(status, idx) in operational_statuses" :key="idx" :value="status.id">{{ status.name }}</option>
                                    </select>
                                    <label class="form-label-outlined emis-label" for="school_operational_status_id">Select Operation Status</label>
                                </div>
                            </div>
                            <div class="col-lg-2">
                                <div class="form-control-wrap">
                                    <select id="emis_status" class="form-select form-control form-control-xl">
                                        <option value="">ALL EMIS STATUS</option>
                                        <option value="-1">NOT UPDATED</option>
                                        <option value="1">UPDATED</option>
                                    </select>
                                    <label class="form-label-outlined emis-label" for="emis_status">Select EMIS Status</label>
                                </div>
                            </div>
                            <div class="col-lg-2">
                                <div class="form-control-wrap">
                                    <input v-model.trim="filter.emis_number" type="text" class="form-control text-uppercase border-dark-teal" placeholder="Search By EMIS No.">
                                    <label class="form-label-outlined emis-label" for="emis_number">Enter EMIS Number</label>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="form-wrap w-auto">
                                    <div class="input-group">
                                        <input v-model.trim="filter.search_term" type="text" class="form-control text-uppercase border-dark-teal" placeholder="Search By School Name">
                                        <label class="form-label-outlined emis-label" for="emis_number">Enter School Name</label>
                                        <div class="input-group-append">
                                            <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                <em class="icon ni ni-cross"></em>
                                            </button>
                                            <button @click.prevent="filterSchools()" class="btn rounded-right text-white bg-dark-teal" type="button">
                                                <em class="icon ni ni-filter mr-1"></em>Apply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="loading" class="text-center py-5">
                        <div class="py-5"></div>
                        <div class="py-3"></div>
                        <div class="spinner-border" style="width: 2rem; height: 2rem;" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="py-3"></div>
                        <div class="py-5"></div>
                    </div>
                    <div v-if="!loading" class="card-inner p-0">
                        <div class="table-responsive-lg">
                            <table class="nk-tb-list nk-tb-ulist is-compact">
                                <thead>
                                <tr class="nk-tb-item nk-tb-head bg-secondary">
                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-left-0 text-uppercase text-center border-1">
                                        <div class="custom-control custom-control-sm custom-checkbox notext">
                                            <input @change="toggleAllSchools()" v-model="select_all_schools" type="checkbox" class="custom-control-input" id="uid">
                                            <label class="custom-control-label" for="uid"></label>
                                        </div>
                                    </th>
                                    <th @click="sortSchools('sort_name')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase border-1">
                                        <em v-if="filter.sort_name === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_name === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span v-if="schoolTypeIdObj > 3 && schoolTypeIdObj !== 7" class="sub-text ucap text-white">INSTITUTION NAME</span>
                                        <span v-else class="sub-text ucap text-white">SCHOOL NAME</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-left border-1">
                                        <span class="sub-text ucap text-white">EMIS NUMBER</span>
                                    </th>
                                    <th  @click="sortSchools('sort_district_id')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-left border-1">
                                        <em v-if="filter.sort_district_id === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_district_id === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">DISTRICT</span>
                                    </th>

                                    <th  @click="sortSchools('sort_ownership_status_id')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-left border-1">
                                        <em v-if="filter.sort_ownership_status_id === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_ownership_status_id === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">OWNERSHIP</span>
                                    </th>
                                    <th @click="sortSchools('sort_operational_status_id')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-left border-1">
                                        <em v-if="filter.sort_operational_status_id === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_operational_status_id === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">OPERATION STATUS</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-left border-1">
                                        <span class="sub-text ucap text-white">EMIS STATUS</span>
                                    </th>

                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-right-0 text-uppercase border-1 text-center">
                                        <span class="sub-text ucap text-white">ACTIONS</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="border-bottom">
                                <tr v-for="school in schools.data" class="nk-tb-item">
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <div class="custom-control custom-control-sm custom-checkbox notext">
                                            <input @change="toggleOneSchool(school.id)" v-model="selected_schools" :value="school.id" type="checkbox" class="custom-control-input" :id="'uid'+school.id">
                                            <label class="custom-control-label" :for="'uid'+school.id"></label>
                                        </div>
                                    </td>
                                    <td class="nk-tb-col px-1 text-uppercase">
                                        <a :href="linkSchoolProfile+school.emis_number.toUpperCase()" target="_blank" class="tb-lead cursor text-dark-teal">
                                            <div class="user-card">
                                                <div class="user-avatar">
                                                    <span class="text-white">{{ school.initials }}</span>
                                                </div>
                                                <div class="user-name text-uppercase">
                                                    <span class="text-dark-teal">{{ school.name }}</span>
                                                </div>
                                            </div>
                                        </a>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-left">
                                        <span class="text-dark tb-lead">{{ school.emis_number }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-left">
                                        <span v-if="school.district !== null" class="text-dark tb-lead">{{ school.district.name }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-left">
                                        <span class="text-dark tb-lead">{{ school.ownership_status.name }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-left">
                                        <span v-if="school.operational_status.name === 'ACTIVE'" class="tb-status text-dark-teal">{{ school.operational_status.name }}</span>
                                        <span v-if="school.operational_status.name === 'SUSPENDED'" class="tb-status text-danger">{{ school.operational_status.name }}<br/></span>
                                        <span v-if="school.operational_status.name === 'SUSPENDED'" @click="viewReason(school)" class="tb-status cursor rounded-0 badge badge-outline-dark-teal"> REASON</span>
                                        <span v-if="school.operational_status.name === 'CLOSED'" class="tb-status font-italic text-muted">{{ school.operational_status.name }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-left">
                                        <span v-if="school.user_account === null" class="tb-status text-danger">Not Updated</span>
                                        <span v-else class="tb-status text-dark-teal">Updated</span>
                                    </td>

                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <a :href="linkSchoolProfile+school.emis_number.toUpperCase()" target="_blank" data-toggle="tooltip" data-placement="top" title="View Details" class="cursor lead mr-1 text-primary">
                                            <em class="icon ni ni-eye-fill"></em>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div><!-- .card-inner -->
                    <div v-if="schools.data.length && schools.total > schools.per_page" class="card-inner d-flex flex-row justify-content-between">
                        <nav>
                            <ul class="pagination">
                                <li :class="[schools.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="schools.current_page > 1 ? loadSchools(1) : null" :class="[schools.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                                    </a>
                                </li>
                                <li :class="[schools.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="schools.current_page > 1 ? loadSchools(schools.current_page-1) : null" :class="[schools.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                                    </a>
                                </li>
                                <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                                    <a @click="loadSchools(link.label)" class="page-link cursor" v-html="link.label"></a>
                                </li>
                                <li :class="[schools.current_page === schools.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="schools.current_page < schools.last_page ? loadSchools(schools.current_page+1) : null" :class="[schools.current_page === schools.last_page ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                                    </a>
                                </li>
                                <li :class="[schools.current_page === schools.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="schools.current_page < schools.last_page ? loadSchools(schools.last_page) : null" :class="[schools.current_page === schools.last_page ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <div class="d-flex ml-4">
                          <span class="align-self-center">
                              Showing <span class="text-primary">{{ schools.from }}</span> to <span class="text-primary">{{ schools.to }}</span> of <span class="text-primary">{{ schools.total }}</span>
                          </span>
                        </div>
                    </div><!-- .card-inner -->
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->
        <div v-if="!schools.data.length && !loading" class="card card-stretch">
            <div class="card-inner-group">
                <div class="card-body">
                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                        <em class="icon ni ni-alert-circle"></em> There are no {{ schoolLabel.toLowerCase() }} to display at the moment.
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="updateOperationStatusModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Update Institution Operation Status</h5>
                    </div>
                    <div class="modal-body">
                        <notifications ref="notify"></notifications>
                        <div class="example-alert">
                            <div class="alert alert-dark-teal alert-icon">
                                <em class="icon ni ni-alert-circle"></em> You are about to update the Operation Status of {{ operationPreview }}.
                            </div>
                        </div>
                        <form @submit.prevent="updateSchoolOperationStatus">
                            <div class="form-group mt-3">
                                <label class="form-label" for="current_password">Operation Status <span class="text-danger">*</span></label>
                                <div class="form-wrap w-auto">
                                    <select required id="formOperationalStatusId">
                                        <option value="">-- SELECT --</option>
                                        <option v-for="(status, idx) in operational_statuses" :key="idx" :value="status.id">{{ status.name }}</option>
                                    </select>
                                </div>
                            </div>
                            <div v-show="operationStatus.status === 2" class="form-group mt-3">
                                <label class="form-label" for="formOperationalSuspensionReasonId">Suspension Reason <span class="text-danger">*</span></label>
                                <div class="form-wrap w-auto">
                                    <select :required="operationStatus.status === 2" id="formOperationalSuspensionReasonId">
                                        <option value="">-- SELECT --</option>
                                        <option v-for="(reason, idx) in suspension_reasons" :key="idx" :value="reason.id">{{ reason.name }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group mt-3">
                                <label class="form-label" for="effectDate">Since <span class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input required v-model.trim="operationStatus.effect_date" id="effectDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                            <div class="modal-footer px-0 pb-0 d-flex justify-content-between">
                                <button @click="resetOperationalDetails" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                    <em class="icon ni ni-cross"></em><span>CANCEL</span>
                                </button>
                                <button :disabled="loading || operationStatus.status === ''" type="submit" class="btn bg-dark-teal d-flex">
                                    <span v-if="loading" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                    <span v-if="loading" class="align-self-center">Updating...</span>
                                    <span v-if="loading" class="sr-only">Updating...</span>
                                    <span v-if="!loading" class="align-self-center">Update Operation Status</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- suspension reason modal -->
        <div class="modal fade zoom" tabindex="-1" id="reasonsModal">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <div class="team">
                                <div class="user-card user-card-s2">
                                    <div class="user-info">
                                        <h6 v-if="school.operational_status.name === 'SUSPENDED'" class="text-uppercase">{{ school.operational_status.name }}</h6>
                                        <span v-if="school.suspension_reason !== null" class="sub-text">{{ school.suspension_reason.name }}</span>
                                    </div>
                                </div>
                                <div class="team-view row">
                                    <div class="col-lg-6">
                                        <a data-dismiss="modal" class="btn btn-outline-dark-teal btn-block"><span>Close</span></a>
                                    </div>
                                </div>
                            </div><!-- .team -->
                        </div><!-- .card-inner -->
                    </div>
                </div>
            </div>
        </div>
        <!-- New Modal for Ownership Status -->
        <div class="modal fade zoom" tabindex="-1" id="updateOwnershipStatusModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Update Institution Ownership Status</h5>
                    </div>
                    <div class="modal-body">
                        <notifications ref="notify"></notifications>
                        <div class="example-alert">
                            <div class="alert alert-dark-teal alert-icon">
                                <em class="icon ni ni-alert-circle"></em> You are about to update the ownership status of {{ ownershipPreview }}.
                            </div>
                        </div>
                        <form @submit.prevent="updateSchoolOwnershipStatus">
                            <div class="form-group mt-3">
                                <label class="form-label" for="formOwnershipStatusId">Ownership Status <span class="text-danger">*</span></label>
                                <div class="form-wrap w-auto">
                                    <select required id="formOwnershipStatusId">
                                        <option value="">-- SELECT --</option>
                                        <option value="1">GOVT AIDED</option>
                                        <option value="2">PRIVATE</option>
                                    </select>
                                </div>
                            </div>

                            <div class="modal-footer px-0 pb-0 d-flex justify-content-between">
                                <button @click="resetOwnershipDetails" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                    <em class="icon ni ni-cross"></em><span>CANCEL</span>
                                </button>
                                <button :disabled="loading || ownershipStatus.status === ''" type="submit" class="btn bg-dark-teal d-flex">
                                    <span v-if="loading" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                    <span v-if="loading" class="align-self-center">Updating...</span>
                                    <span v-if="!loading" class="align-self-center">Update Ownership Status</span>
                                    <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import Notifications from "../../Notifications.vue";
import moment from "moment/moment";
import fetchUserPermissionsMixin from "../../../mixins/fetchUserPermissionsMixin";

export default {
    name: "Index",
    props: ['districtsObj', 'schoolsObj', 'schoolTypeIdObj','userRoleObj'],
    mixins:[fetchUserPermissionsMixin],
    mounted() {
        this.initPlugins();
        this.loadOperationalStatuses();
        this.loadOperationalSuspensionReasons();
    },
    components: {
        Notifications
    },
    data: function () {
        return {
            select_all_schools: false,
            selected_schools: [],
            loading: false,
            filtering: false,
            exportLoading: false,
            api_url: '/admin/schools/',
            bulk_action: '',
            schools: {
                data: []
            },
            districts: [],
            counties: [],
            sub_counties: [],
            parishes: [],
            operational_statuses: [],
            suspension_reasons: [],
            filter: {
                sort_name: 'asc',
                sort_ownership_status_id: '',
                sort_operational_status_id: '',
                sort_district_id: '',
                district_id: '',
                county_id: '',
                sub_county_id: '',
                parish_id: '',
                school_ownership_status_id: '',
                operational_status_id: '',
                emis_status: '',
                emis_number: '',
                search_term: '',
            },
            operationStatus: {
                status: '',
                suspension_reason_id: '',
                effect_date: moment().format("D MMMM, YYYY"),
                selected_schools: [],
            },
            ownershipStatus: {
                status: '',
                effect_date: moment().format("D MMMM, YYYY"),
                selected_schools: []
            },
            school: {
                operational_status: {
                    name: '',
                },
                suspension_reason: {
                    name: '',
                },
            }
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;
            this.schools = this.schoolsObj;
            this.school_type_id = this.schoolTypeIdObj;
            this.districts = this.districtsObj;

            $('#bulk_action').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.bulk_action = data.id;
                    return data.text;
                },
            });

            $('#district_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.district_id = data.id.length > 0 ? Number(data.id) : "";
                    self.filter.county_id = '';
                    $("#sub_county_id").val("").change();
                    $("#county_id").val("").change();
                    self.loadCounties();
                    return data.text;
                },
            });

            $('#county_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.county_id = data.id.length > 0 ? Number(data.id) : "";
                    $("#sub_county_id").val("").change();
                    self.loadSubCounties();
                    return data.text;
                },
            });

            $('#sub_county_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.sub_county_id = data.id.length > 0 ? Number(data.id) : "";
                    $("#parish_id").val("").change();
                    self.loadParishes();
                    return data.text;
                },
            });

            $('#parish_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.parish_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#school_ownership_status_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.school_ownership_status_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#school_operational_status_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.operational_status_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#formOperationalStatusId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.operationStatus.status = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#formOperationalSuspensionReasonId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.operationStatus.suspension_reason_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#emis_status').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.emis_status = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#effectDate').datepicker({
                format: 'd MM, yyyy',
                endDate: '0D',
                autoclose: true,
            }).on('hide', e=>{
                self.operationStatus.effect_date = moment(e.date).format('D MMMM, YYYY');
            });

            $('#formOwnershipStatusId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.ownershipStatus.status = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#ownershipEffectDate').datepicker({
                format: 'd MM, yyyy',
                endDate: '0D',
                autoclose: true,
            }).on('hide', e=>{
                self.ownershipStatus.effect_date = moment(e.date).format('D MMMM, YYYY');
            });
        },
        showOperationalStatusModal() {
            $('#updateOperationStatusModal').modal({backdrop:"static"});
        },
        showOwnershipStatusModal() {
            $('#updateOwnershipStatusModal').modal({backdrop:"static"});
        },
        viewReason: function (school) {
            this.school = school;
            $('#reasonsModal').modal("show");
        },
        loadOperationalStatuses() {
            axios.get('/lists/operational-statuses')
                .then(response=>{
                    this.operational_statuses = response.data;
                })
                .catch(error=>{
                    this.renderError(error)
                });
        },
        loadOperationalSuspensionReasons() {
            axios.get('/lists/operational-suspension-reasons')
                .then(response=>{
                    this.suspension_reasons = response.data;
                })
                .catch(error=>{
                    this.renderError(error)
                });
        },
        loadCounties: function () {
            this.counties = [];
            // this.resetCountySelect();
            if (this.filter.district_id !== "") {
                this.counties = this.districts.find(district=>{
                    return district.id === this.filter.district_id;
                }).county;
            }
        },
        loadSubCounties: function () {
            this.sub_counties = [];
            this.resetSubCountySelect();
            if (this.filter.county_id !== "") {
                this.sub_counties = this.counties.find(county=>{
                    return county.id === this.filter.county_id;
                }).sub_county;
            }
        },
        loadParishes: function () {
            this.parishes = [];
            this.resetParishSelect();
            if (this.filter.sub_county_id !== "") {
                this.parishes = this.sub_counties.find(sub_county=>{
                    return sub_county.id === this.filter.sub_county_id;
                }).parish;
            }
        },
        // resetCountySelect: function() {
        //     let self = this;
        //     let select = $("#county_id");
        //     if (select.hasClass("select2-hidden-accessible")) {
        //         select.select2('destroy');
        //         select.select2({
        //             minimumResultsForSearch: 0,
        //             templateSelection: function (data, container) {
        //                 self.filter.county_id = data.id.length > 0 ? Number(data.id) : "";
        //                 self.loadSubCounties();
        //                 return data.text;
        //             },
        //         });
        //     }
        // },
        resetSubCountySelect: function() {
            let self = this;
            let select = $("#sub_county_id");
            if (select.hasClass("select2-hidden-accessible")) {
                select.select2('destroy');
                select.select2({
                    minimumResultsForSearch: 0,
                    containerCssClass: 'border-dark-teal',
                    templateSelection: function (data, container) {
                        self.filter.sub_county_id = data.id.length > 0 ? Number(data.id) : "";
                        self.loadParishes();
                        return data.text;
                    },
                });
            }
        },
        resetParishSelect: function() {
            let self = this;
            let select = $("#parish_id");
            if (select.hasClass("select2-hidden-accessible")) {
                select.select2('destroy');
                select.select2({
                    minimumResultsForSearch: 0,
                    containerCssClass: 'border-dark-teal',
                    templateSelection: function (data, container) {
                        self.filter.parish_id = data.id.length > 0 ? Number(data.id) : "";
                        return data.text;
                    },
                });
            }
        },
        resetOperationalDetails() {
            $('#updateOperationStatusModal').modal('hide');
            $('#formOperationalStatusId').val('').change();
            $('#formOperationalSuspensionReasonId').val('').change();
            this.selected_schools = [];
            $('#effectDate').datepicker('setDate', moment().toDate());
            this.operationStatus.effect_date = moment().format('D MMMM, YYYY');
        },
        resetOwnershipDetails() {
            $('#updateOwnershipStatusModal').modal('hide');
            $('#formOwnershipStatusId').val('').change();
            this.selected_schools = [];
            $('#ownershipEffectDate').datepicker('setDate', moment().toDate());
            this.ownershipStatus.effect_date = moment().format('D MMMM, YYYY');
        },
        updateSchoolOperationStatus() {
            this.loading = true;
            let payload = {};
            if (this.filtering) {
                payload = {...this.filter, ...this.operationStatus}
            } else {
                payload = this.operationStatus
            }
            axios.post(this.api_url+this.school_type_id+'/update-operation-status?year='+this.selectedYear, payload)
                .then(response=>{
                    this.schools = response.data;
                    this.loading = false;
                    this.resetOperationalDetails();
                })
                .catch(error=>{
                    this.loading = false;
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        updateSchoolOwnershipStatus() {
            this.loading = true;
            // Prepare payload
            this.ownershipStatus.selected_schools = this.selected_schools;
            axios.post('/admin/schools/update-ownership-status?year='+this.selectedYear, this.ownershipStatus)
                .then(response => {
                  //  this.$refs.notify.messages.push({status: 'success', title: 'Success', message:"Ownership status updated successfully."});
                    this.loading = false;
                    this.resetOwnershipDetails();
                    // Optionally reload schools data
                    this.loadSchools(this.schools.current_page, this.filtering);
                })
                .catch(error => {
                    this.loading = false;
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        toggleAllSchools: function () {
            this.selected_schools =[];

            if (this.select_all_schools) {
                this.schools.data.forEach(school=>{
                    this.selected_schools.push(school.id)
                });
            }
        },
        toggleOneSchool: function (id) {
            this.select_all_schools = this.selected_schools.length === this.schools.length
        },
        sortSchools: function (section) {
            if (section === 'sort_name') {
                this.filter.sort_name = this.filter.sort_name.length ? (this.filter.sort_name === 'asc' ? this.filter.sort_name = 'desc': this.filter.sort_name = 'asc') : this.filter.sort_name = 'asc';
                this.filter.sort_district_id = '';
                this.filter.sort_ownership_status_id = '';
            }

            if (section === 'sort_district_id') {
                this.filter.sort_name = '';
                this.filter.sort_district_id = this.filter.sort_district_id.length ? (this.filter.sort_district_id === 'asc' ? this.filter.sort_district_id = 'desc': this.filter.sort_district_id = 'asc') : this.filter.sort_district_id = 'asc';
                this.filter.sort_ownership_status_id = '';
            }

            if (section === 'sort_ownership_status_id') {
                this.filter.sort_name = '';
                this.filter.sort_district_id = '';
                this.filter.sort_ownership_status_id = this.filter.sort_ownership_status_id.length ? (this.filter.sort_ownership_status_id === 'asc' ? this.filter.sort_ownership_status_id = 'desc': this.filter.sort_ownership_status_id = 'asc') : this.filter.sort_ownership_status_id = 'asc';
            }

            if (section === 'sort_operational_status_id') {
                this.filter.sort_name = '';
                this.filter.sort_district_id = '';
                this.filter.sort_operational_status_id = this.filter.sort_operational_status_id.length ? (this.filter.sort_operational_status_id === 'asc' ? this.filter.sort_operational_status_id = 'desc': this.filter.sort_operational_status_id = 'asc') : this.filter.sort_operational_status_id = 'asc';
            }

            this.loading = true;
            axios.post(this.api_url+this.school_type_id+'/filter?year='+this.selectedYear, this.filter)
                .then(response=>{
                    this.schools = response.data;
                    this.loading = false;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        filterSchools: function () {
            this.loading = true;
            axios.post(this.api_url+this.school_type_id+'/filter?year='+this.selectedYear, this.filter)
                .then(response=>{
                    this.schools = response.data;
                    this.loading = false;
                    this.filtering = true;
                    this.resetOperationalDetails();
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        resetFilter: function () {
            this.loading = true;
            axios.get(this.api_url+this.school_type_id+'?year='+this.selectedYear)
                .then(response=>{
                    this.schools = response.data;
                    this.loading = false;
                    this.filter.emis_number = '';
                    this.filter.search_term = '';
                    $('#district_id').val('').change();
                    $('#county_id').val('').change();
                    $('#sub_county_id').val('').change();
                    $('#parish_id').val('').change();
                    $('#school_ownership_status_id').val('').change();
                    $('#school_operational_status_id').val('').change();
                    $('#emis_status').val('').change();
                    this.filtering = false;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error)
                });
        },
        loadSchools: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+this.school_type_id+'/filter?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.schools = response.data;
                        if (filtering) {
                            this.filtering = true;
                        }
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        exportSchools: function () {
            this.exportLoading = true;
            axios.post(this.api_url+this.school_type_id+'/export-excel',this.filter)
                .then(()=>{
                    this.$refs.notifyError.messages.push({status: 'success', title: 'Success', message:"Export has started, we will notify you through your email when the excel file is ready."});
                    this.exportLoading = false;
                })
                .catch(error=>{
                    this.loading = false;
                    this.exportLoading = false;
                    this.renderError(error)
                });
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active-dark-teal disabled";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        selectedYear: function () {
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);

            if (urlParams.has('year')) {
                return urlParams.get('year');
            } else {
                return moment().format("YYYY");
            }
        },
        getPaginationLinks: function () {
            let arr = this.schools.links;
            arr.pop();
            arr.shift();
            return arr;
        },
        schoolLabel: function () {
            if (this.schoolTypeIdObj === 1) {
                return "Pre-Primary Schools";
            }
            if (this.schoolTypeIdObj === 2) {
                return "Primary Schools";
            }
            if (this.schoolTypeIdObj === 3) {
                return "Secondary Schools";
            }
            if (this.schoolTypeIdObj === 4) {
                return "Certificate Institutions";
            }
            if (this.schoolTypeIdObj === 5) {
                return "Diploma Institutions";
            }
            if (this.schoolTypeIdObj === 6) {
                return "Degree Institutions";
            }
            if (this.schoolTypeIdObj === 7) {
                return "International Schools";
            }
        },
        linkSchoolProfile: function () {
            if (this.schoolTypeIdObj === 1) {
                return "view-pre-primary-institution/";
            }
            if (this.schoolTypeIdObj === 2) {
                return "view-primary-institution/";
            }
            if (this.schoolTypeIdObj === 3) {
                return "view-secondary-institution/";
            }
            if (this.schoolTypeIdObj === 4) {
                return "view-certificate-institution/";
            }
            if (this.schoolTypeIdObj === 5) {
                return "view-diploma-institution/";
            }
            if (this.schoolTypeIdObj === 6) {
                return "view-degree-institution/";
            }
            if (this.schoolTypeIdObj === 7) {
                return "view-international-institution/";
            }
        },
        operationPreview() {
            if (this.selected_schools.length === 1) {
                let school = this.schools.data.find(s=>{
                    return s.id === this.selected_schools[0]
                })

                if (school !== undefined) {
                    return school.name
                }

                return '';
            } else if (this.selected_schools.length > 1) {
                return this.selected_schools.length+' '+this.schoolLabel;
            }

            return '';
        },
        ownershipPreview() {
            if (this.selected_schools.length === 1) {
                let school = this.schools.data.find(s => s.id === this.selected_schools[0]);
                return school ? school.name : '';
            } else if (this.selected_schools.length > 1) {
                return this.selected_schools.length + ' schools';
            }
            return '';
        }
    },
    watch: {
        selected_schools(list) {
            this.select_all_schools = list.length > 0 && list.length === this.schools.data.length
            this.operationStatus.selected_schools = list;
            this.ownershipStatus.selected_schools = list;
        }
    }
}
</script>

<style scoped>

</style>
