<template>
    <div class="">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Manage User Roles</h3>
                </div><!-- .nk-block-head-content -->
                <div v-if="hasPermission('roles-create')" class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a class="cursor btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                        <div class="toggle-expand-content" data-content="pageMenu">
                            <ul class="nk-block-tools g-3">
                                <li>
                                    <span @click="newRole" class="cursor btn bg-dark-teal">
                                        <em class="icon ni ni-plus text-white"></em>
                                        <span class="">Add Role</span>
                                    </span>
                                </li>
                            </ul>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <Notifications ref="notify"/>
                <div class="card-inner-group">
                    <div class="row card-inner border-0">
                        <div class="col-lg-10">
                            <form @submit.prevent="loadRoles(1, true)">
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="form-control-wrap">
                                            <select id="filterRoleCategory" class="form-select form-control form-control-xl">
                                                <option value="">ALL</option>
                                                <option value="admin">ADMIN ROLES</option>
                                                <option value="district">DISTRICT ROLES</option>
                                                <option value="sub_county">SUB-COUNTY ROLES</option>
                                                <option value="institution">INSTITUTION ROLES</option>
                                            </select>
                                            <label class="form-label-outlined emis-label" for="filterRoleCategory">CATEGORY</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-wrap">
                                            <div class="input-group">
                                                <input v-model.trim="filter.search_term" type="text" class="form-control text-uppercase border-dark-teal" :placeholder="`Role Name`">
                                                <div class="input-group-append">
                                                    <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                        <em class="icon ni ni-cross"></em>
                                                    </button>
                                                    <button class="btn rounded-right bg-dark-teal" type="submit">
                                                        <em class="icon ni ni-filter mr-1"></em>Apply
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="card-inner p-0">
                        <div class="table-responsive">
                            <table class="nk-tb-list nk-tb-ulist is-compact">
                                <tr class="nk-tb-item nk-tb-head bg-secondary">
                                    <th class="nk-tb-col nk-tb-col-check">
                                        <div class="custom-control custom-control-sm custom-checkbox notext">
                                            <input @change="toggleAllRoles()" v-model="select_all_roles"  type="checkbox" class="custom-control-input" id="uid">
                                            <label class="custom-control-label" for="uid"></label>
                                        </div>
                                    </th>
                                    <th class="nk-tb-col text-uppercase">
                                        <span class="sub-text text-white">Role</span>
                                    </th>
                                    <th class="nk-tb-col text-uppercase text-center">
                                        <span class="sub-text text-white">CATEGORY</span>
                                    </th>
                                    <th class="nk-tb-col text-uppercase text-center">
                                        <span class="sub-text text-white">Users</span>
                                    </th>
                                    <th class="nk-tb-col text-uppercase text-center">
                                        <span class="sub-text text-white">Permissions</span>
                                    </th>
                                    <th class="nk-tb-col text-uppercase text-center">
                                        <span class="sub-text text-white">ACTIONS</span>
                                    </th>
                                </tr>
                                <tr v-for="role in roles.data" class="nk-tb-item">
                                    <td class="nk-tb-col nk-tb-col-check">
                                        <div class="custom-control custom-control-sm custom-checkbox notext">
                                            <input @change="toggleOneRole()" v-model="selected_roles" :value="role.id" type="checkbox" class="custom-control-input" :id="'role_'+role.id">
                                            <label class="custom-control-label" :for="'role_'+role.id"></label>
                                        </div>
                                    </td>
                                    <td class="nk-tb-col">
                                        <span class="text-dark">{{ role.display_name }}</span>
                                    </td>
                                    <td class="nk-tb-col text-center">
                                    <span v-if="role.is_admin_role_yn && !role.is_at_district_level && !role.is_at_sub_county_level" class="badge badge-dark-teal ">
                                        ADMIN ROLE
                                    </span>
                                        <span v-if="role.is_admin_role_yn && role.is_at_district_level" class="badge badge-secondary ">
                                        DISTRICT ROLE
                                    </span>
                                    <span v-if="role.is_admin_role_yn && role.is_at_sub_county_level" class="badge badge-primary">
                                        SUB-COUNTY ROLE
                                    </span>
                                        <span v-if="!role.is_admin_role_yn && !role.is_at_district_level" class="badge badge-amaranth ">
                                        INSTITUTION ROLE
                                    </span>
                                    </td>
                                    <td class="nk-tb-col text-center">
                                        <span class="text-dark">{{ formatNumber(role.users_count) }}</span>
                                    </td>
                                    <td class="nk-tb-col text-center">
                                        <span v-if="role.permissions.length > 0" class="text-dark-teal">Already Set</span>
                                        <span v-else class="text-danger">Not Set</span>
                                    </td>
                                    <td class="nk-tb-col text-uppercase text-center">
                                        <span v-if="hasPermission('roles-edit')" @click="editRole(role)" class="cursor badge rounded-0 badge-dark-teal">EDIT</span>
                                        <button
                                            v-if="hasPermission('roles-permissions-assign')"
                                            :disabled="!isNotAdminRole(role)"
                                            @click="editPermissions(role)"
                                            class="cursor badge rounded-0 badge-primary ml-2"
                                            :class="{ 'permission-btn-disabled': !isNotAdminRole(role) }">
                                            <em class="icon ni ni-shield text-white"></em> PERMISSIONS
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div v-if="!roles.data.length" class="p-5">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> No roles to display at the moment...
                            </div>
                        </div>
                    </div><!-- .card-inner -->
                    <div class="card-inner d-flex flex-row justify-content-between">
                        <nav v-if="roles.data.length">
                            <ul class="pagination">
                                <li :class="[roles.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="roles.current_page > 1 ? loadRoles(1) : null" :class="[roles.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-first"></em>
                                    </a>
                                </li>
                                <li :class="[roles.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="roles.current_page > 1 ? loadRoles(roles.current_page-1) : null" :class="[roles.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-left"></em>
                                    </a>
                                </li>
                                <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                                    <a @click="loadRoles(link.label)" class="page-link cursor" v-html="link.label"></a>
                                </li>
                                <li :class="[roles.current_page === roles.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="roles.current_page < roles.last_page ? loadRoles(roles.current_page+1) : null" :class="[roles.current_page === roles.last_page ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-right"></em>
                                    </a>
                                </li>
                                <li :class="[roles.current_page === roles.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="roles.current_page < roles.last_page ? loadRoles(roles.last_page) : null" :class="[roles.current_page === roles.last_page ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-last"></em>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <div class="d-flex flex-row ml-4">
                            <span class="align-self-center mr-1">Show</span>
                            <div class="form-wrap align-self-center">
                                <select id="filterPerPage" class="form-select-sm">
                                    <option value="15">15</option>
                                    <option value="30">30</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                        <div v-if="roles.data.length" class="d-flex ml-4">
                            <span class="align-self-center">
                                Showing
                                <span class="text-primary">{{ roles.from }}</span> to
                                <span class="text-primary">{{ roles.to }}</span> of
                                <span class="text-primary">{{ roles.total }}</span> Roles
                            </span>
                        </div>
                    </div><!-- .card-inner -->
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->

        <!-- Role Modal -->
        <div class="modal fade zoom" tabindex="-1" id="roleModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetRole()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="edit ? updateRole() : createRole()">
                        <div class="modal-header">
                            <h5 v-if="edit" class="modal-title">Edit Role</h5>
                            <h5 v-else class="modal-title">Add Role</h5>
                        </div>
                        <div class="modal-body">
                            <notifications ref="modalNotify"></notifications>
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label class="form-label">Category</label>
                                        </div>
                                        <div class="custom-control custom-control-lg custom-radio">
                                            <input v-model="role_form.role_category" type="radio" id="adminRoleCategory" value="admin" name="roleCategory" class="custom-control-input">
                                            <label class="custom-control-label" for="adminRoleCategory">Admin Role</label>
                                        </div>
                                        <div class="custom-control custom-control-lg custom-radio ml-lg-3">
                                            <input v-model="role_form.role_category" type="radio" id="districtRoleCategory" value="district" name="roleCategory" class="custom-control-input">
                                            <label class="custom-control-label" for="districtRoleCategory">District Role</label>
                                        </div>
                                        <div class="custom-control custom-control-lg custom-radio ml-lg-3">
                                            <input v-model="role_form.role_category" type="radio" id="subCountyRoleCategory" value="sub_county" name="roleCategory" class="custom-control-input">
                                            <label class="custom-control-label" for="subCountyRoleCategory">Sub-County Role</label>
                                        </div>
                                    </div>
                                </div><!-- .col -->
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label for="roleName" class="form-label">Name <span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-control-group">
                                            <input required v-model="role_form.display_name" id="roleName" type="text" placeholder="Enter Role Name" class="form-control bg-primary-dim" autocomplete="off">
                                        </div>
                                    </div>
                                </div><!-- .col -->
                            </div><!-- .row -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetRole()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>CANCEL</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Saving...</span>
                                <span v-if="!loading" class="align-self-center">SAVE</span>
                                <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="permissionsModal">
            <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetPermissions()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updatePermissions()">
                        <div class="modal-header">
                            <h5 class="modal-title">Update Permissions</h5>

                        </div>
                        <div class="modal-body scrollable-table-container">
                            <notifications ref="modalNotify"></notifications>
                            <div class="row g-4">

                                <div class="col-12">
                                    <div class="card card-bordered card-full">
                                        <div class="card-inner">
                                            <div class="card-title-group">
                                                <div class="card-title">
                                                    <h6 class="title"><span class="mr-2">Role:</span> <a href="#" class="link d-none d-sm-inline">{{ role.display_name.toUpperCase() }}</a></h6>
                                                </div>

                                            </div>
                                            <div class="form-group p-4">
                                                <label class="form-label">Select Modules (select all that apply) <span class="text-danger">*</span></label>
                                                <div class="row">
                                                    <div class="col-lg-4 mb-2">
                                                        <div class="form-group">
                                                            <div class="custom-control custom-control-sm custom-checkbox d-block">
                                                                <input @change="toggleAllModules()" v-model="select_all_modules" type="checkbox" class="custom-control-input" id="selectAllSections">
                                                                <label class="custom-control-label text-uppercase" for="selectAllSections">Select All</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-for="module in modules" :key="module.id" class="col-lg-4 mb-2">
                                                        <div class="form-group">
                                                            <div class="custom-control custom-control-sm custom-checkbox d-block">
                                                                <input
                                                                    @change="toggleModule(module)"
                                                                    v-model="role_form.selected_modules"
                                                                    :value="module.id"
                                                                    type="checkbox"
                                                                    class="custom-control-input"
                                                                    :id="'selectModuleCheck'+module.id"
                                                                    :disabled="module.permissions.some(permission => shouldDisablePermission(permission))"
                                                                >
                                                                <label class="custom-control-label text-uppercase" :for="'selectModuleCheck'+module.id">{{ module.name.toUpperCase() }}</label>
                                                            </div>
                                                            <div v-if="module.permissions.length" class="form-group ml-4">
                                                                <div v-for="permission in module.permissions" :key="permission.id"  class="custom-control custom-control-sm custom-checkbox d-block">
                                                                    <input
                                                                        @change="toggleModulePermission(module, permission)"
                                                                        v-model="role_form.selected_permissions"
                                                                        :value="permission.id"
                                                                        type="checkbox"
                                                                        class="custom-control-input"
                                                                        :id="'selectModulePermissionCheck'+permission.id"
                                                                        :disabled="shouldDisablePermission(permission)"
                                                                    >
                                                                    <label class="custom-control-label text-uppercase" :for="'selectModulePermissionCheck'+permission.id">{{ permission.display_name.toUpperCase() }}</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div><!-- .card -->
                                </div><!-- .col -->
                            </div><!-- .row -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetPermissions()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>CANCEL</span>
                            </button>
                            <button :disabled="!anySelected || loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Updating...</span>
                                <span v-if="loading" class="sr-only">Updating...</span>
                                <span v-if="!loading" class="align-self-center">UPDATE</span>
                                <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- /Role Modal -->
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";
import fetchUserPermissionsMixin from "../../../mixins/fetchUserPermissionsMixin";

export default {
    name: "Roles",
    mixins:[fetchUserPermissionsMixin],
    props: {
        rolesObj: {
            type: Object,
            required: true,
        },
        modulesObj: {
            required: true,
        },
    },
    components: {
        Notifications
    },
    mounted() {
        this.initPlugins();
    },
    data () {
        return {
            api_url: '/admin/user-manager/roles',
            modal: false,
            edit: false,
            loading: false,
            filtering: false,
            select_all_roles: false,
            select_all_modules: false,
            roles: {
                total: 0,
                data: [],
                links: [],
            },
            role: {
                id: '',
                name: '',
                display_name: '',
                is_admin_role_yn: '',
                is_at_district_level: '',
                is_at_sub_county_level: '',
                permissions: {
                    modules: []
                }
            },
            role_form: {
                id: '',
                display_name: '',
                role_category: 'admin',
                per_page: '',
                selected_modules: [],
                selected_permissions: [],
            },
            filter: {
                search_term: '',
                category: '',
                per_page: '',
            },
            selected_roles: [],
            modules: [],
        }
    },
    methods: {
        initPlugins () {
            let self = this;
            this.roles = this.rolesObj;
            this.modules = this.modulesObj;

            $('#filterRoleCategory').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.category = data.id;
                    return data.text;
                },
            });

            $('#filterPerPage').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.per_page = data.id !== "" ? Number(data.id) : data.id;
                    self.role_form.per_page = data.id !== "" ? Number(data.id) : data.id;
                    self.loadRoles(1, self.filtering);
                    return data.text;
                },
            });
        },
        formatNumber (raw) {
            return moneyFormat.to(raw)
        },
        toggleAllRoles: function () {
            this.selected_roles =[];

            if (this.select_all_roles) {
                this.roles.data.forEach(role=>{
                    this.selected_roles.push(role.id)
                });
            }
        },
        toggleOneRole: function () {
            this.select_all_roles = this.selected_roles.length === this.roles.data.length
        },
        // Toggle all modules
        toggleAllModules() {
            if (this.select_all_modules) {
                // Select all modules
                this.modules.forEach(module => {
                    if (!this.role_form.selected_modules.includes(module.id)) {
                        this.role_form.selected_modules.push(module.id);
                    }
                    module.permissions.forEach(permission => {
                        // Only select permissions that are not disabled
                        if (!this.shouldDisablePermission(permission) && !this.role_form.selected_permissions.includes(permission.id)) {
                            this.role_form.selected_permissions.push(permission.id);
                        }
                    });
                });
            } else {
                // Deselect all modules
                this.role_form.selected_modules = [];
                // Only deselect permissions that are not disabled
                this.role_form.selected_permissions = this.role_form.selected_permissions.filter(permissionId => {
                    return this.modules.some(module =>
                        module.permissions.some(permission =>
                            permission.id === permissionId && this.shouldDisablePermission(permission)
                        )
                    );
                });
            }
        },

        // Toggle individual module and its permissions
        toggleModule(module) {
            // Check if the module has permissions
            if (module.permissions.length) {
                let exists = this.role_form.selected_modules.find(entry=>{
                    return entry === module.id;
                });
                if (exists === undefined) {
                    let sub = [];
                    module.permissions.forEach(permission => {
                        sub.push(permission.id);
                    });
                    this.role_form.selected_permissions = this.role_form.selected_permissions.filter(n => !sub.includes(n))
                } else {
                    module.permissions.forEach(permission=>{
                        if (this.role_form.selected_permissions.includes(permission.id) === false) {
                            this.role_form.selected_permissions.push(permission.id);
                        }
                    });
                }
            }

        },
        // Toggle individual module permission
        toggleModulePermission(module) {
            let permissionsIds = [];
            // Collect all permission IDs for the current module
            module.permissions.forEach(permission => {
                permissionsIds.push(permission.id);
            });
            // If any permission for the module is selected, ensure the module is also selected
            if (this.goodMix(permissionsIds)) {
                if (!this.role_form.selected_modules.includes(module.id)) {
                    this.role_form.selected_modules.push(module.id);
                }
            } else {
                // If no permissions are selected, remove the module from selected modules
                let moduleIndex = this.role_form.selected_modules.findIndex(m => m === module.id);
                if (moduleIndex !== -1) {
                    this.role_form.selected_modules.splice(moduleIndex, 1);
                }
            }
        },
        goodMix: function (permissionsIds) {
            return permissionsIds.some(permission => this.role_form.selected_permissions.includes(permission));
        },
        parentMissing(moduleId) {
            // Check if the module ID is missing from the selected modules
            return !this.role_form.selected_modules.includes(moduleId);
        },
        isNotAdminRole(role) {
            // Check if the passed role is neither 'institution-admin' nor 'institution-user'
            return role.name !== 'institution-admin' && role.name !== 'institution-user';
        },
        // List of permissions to disable for 'emis-super-admin'
        getDisabledPermissions() {
            return ['roles-permissions-assign','roles-view','users-view'];
        },
        shouldDisablePermission(permission) {
            // Check if the role is 'emis-super-admin' and if the permission is in the array of disabled permissions
            return this.role.name === 'emis-super-admin' && this.getDisabledPermissions().includes(permission.name);
        },
        loadRoles: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.roles = response.data;
                        if (filtering) {
                            this.filtering = true;
                        }
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            this.filter = {
                search_term: '',
                category: '',
                per_page: '',
            };
            $('#filterRoleCategory').val('').change();
            $('#filterStatus').val('').change();
            this.loadRoles(1, false);
        },
        newRole: function () {
            this.edit = false;
            this.modal = true;
            this.role_form = {
                id: '',
                display_name: '',
                role_category: 'admin',
                per_page: this.filter.per_page,
                selected_modules: [],
                selected_permissions: [],
            }
            $('#roleModal').modal({backdrop: "static"});
        },
        editRole: function (role) {
            this.edit = true;
            this.modal = true;
            this.role = role;
            this.role_form = {
                id: role.id,
                display_name: role.display_name,
                role_category: role.is_admin_role_yn && role.is_at_district_level ? 'district' : role.is_at_sub_county_level ? 'sub_county' : 'admin',
                per_page: this.filter.per_page,
                selected_modules: [],
                selected_permissions: [],
                select_all_modules: ''
            }
            $('#roleModal').modal({backdrop: "static"});
        },
        editPermissions: function (role) {
            this.edit = true;
            this.modal = true;
            this.role = role;
            this.role.id = role.id;
            this.role.display_name = role.display_name;
            if (role.permissions !== undefined) {
                let moduleSet = new Set();  // To track unique modules
                role.permissions.forEach(permission => {
                    // Add permissions to selected permissions array
                    this.role_form.selected_permissions.push(permission.id);

                    // Check if the permission has modules
                    if (permission.modules !== undefined) {
                        permission.modules.forEach(module => {
                            // Add module to the selected modules array
                            moduleSet.add(module.id);
                        });
                    }
                });
                // Convert the Set to an array for selected modules
                this.role_form.selected_modules = Array.from(moduleSet);
            }
            $('#permissionsModal').modal({backdrop: "static"});
        },
        createRole: function () {
            this.loading = true;
            axios.post(this.api_url+"/create", this.role_form)
                .then(response => {
                    Swal.fire('SUCCESS!', 'Role has been created successfully.', 'success');
                    this.roles = response.data;
                    this.resetRole();
                })
                .catch(errors=>{
                    this.loading = false;
                    this.renderError(errors);
                });
        },
        updateRole: function () {
            this.loading = true;
            axios.post(this.api_url+"/update/"+this.role.id, this.role_form)
                .then(response => {
                    Swal.fire('SUCCESS!', 'Role has been updated successfully.', 'success');
                    this.roles = response.data;
                    this.resetRole();
                })
                .catch(errors=>{
                    this.loading = false;
                    this.renderError(errors);
                });
        },
        updatePermissions: function () {
            this.loading = true;
            axios.post(this.api_url+"/permissions/update/"+this.role.id, this.role_form)
                .then(response => {
                    Swal.fire('SUCCESS!', 'Permissions have been updated successfully.', 'success');
                    this.roles = response.data;
                    this.resetPermissions();
                })
                .catch(errors=>{
                    this.loading = false;
                    this.renderError(errors);
                });
        },
        resetRole() {
            $('#roleModal').modal("hide");
            this.modal = false;
            this.edit = false;
            this.loading = false;
            this.select_all_modules = '';
            this.role_form = {
                id: '',
                display_name: '',
                category: 'admin',
                per_page: this.filter.per_page,
                selected_modules: [],
                selected_permissions: [],
            }
        },
        resetPermissions() {
            $('#permissionsModal').modal("hide");
            this.modal = false;
            this.edit = false;
            this.loading = false;
            this.select_all_modules = '';
            this.role_form = {
                id: '',
                display_name: '',
                category: 'admin',
                per_page: this.filter.per_page,
                selected_modules: [],
                selected_permissions: [],
            }
        },
        renderError: function (error) {
            const notifyError = (title, message) => {
                if (this.modal) {
                    this.$refs.modalNotify.messages.push({ status: 'error', title, message });
                } else {
                    this.$refs.notify.messages.push({ status: 'error', title, message });
                }
            };

            if (error.response) {
                const { status, data } = error.response;
                switch (status) {
                    case 500:
                    case 405:
                        notifyError('System Error: ', data.message);
                        break;
                    case 401:
                        notifyError('Permission Error: ', 'You are not authorised to perform this action');
                        break;
                    case 404:
                        notifyError('Resource Not Found: ', 'You are trying to reach a URL that does not exist');
                        break;
                    case 422:
                        for (let field in data.errors) {
                            this.showError(data.errors[field]);
                        }
                        break;
                    default:
                        notifyError('Other Error: ', error.message);
                }
            } else {
                notifyError('Other Error: ', error.message);
            }
        },

        showError: function (message) {
            let text = message.join('<br>');
            const notifyDataError = (title, message) => {
                if (this.modal) {
                    this.$refs.modalNotify.messages.push({ status: 'error', title, message });
                } else {
                    this.$refs.notify.messages.push({ status: 'error', title, message });
                }
            };
            notifyDataError('Data Error!: ', text);
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active-dark-teal disabled";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
    },
    computed: {
        getPaginationLinks: function () {
            let arr = this.roles.links;
            arr.pop();
            arr.shift();
            return arr;
        },
        // Check if any module or permission is selected
        anySelected() {
            return this.role_form.selected_modules.length > 0 || this.role_form.selected_permissions.length > 0;
        }
    },
}
</script>

<style scoped>
.scrollable-table-container {
    max-height: 800px;
    overflow-y: auto;
}
.permission-btn-disabled {
    pointer-events: none;
    opacity: 0.6;
}
</style>
