<?php

namespace Modules\Core\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;

class LearnerEnrolmentApplicationFactory extends Factory
{
    protected $model = LearnerEnrolmentApplication::class;

    public function definition()
    {
        return [
            'application_number' => $this->faker->unique()->numerify('APP#####'),
            'first_name' => $this->faker->firstName,
            'surname' => $this->faker->lastName,
            'birth_date' => $this->faker->date('Y-m-d'),
            'gender' => $this->faker->randomElement(['male', 'female']),
            'country_id' => 221,
            'parent_country_id' => 221,
            'parent_first_name' => $this->faker->firstName,
            'parent_surname' => $this->faker->lastName,
            'parent_birth_date' => $this->faker->date('Y-m-d'),
            'parent_gender' => $this->faker->randomElement(['male', 'female']),
            'parent_relationship' => $this->faker->randomElement(['parent', 'guardian']),
            'parent_phone_1' => $this->faker->phoneNumber,
            'parent_phone_2' => $this->faker->phoneNumber,
            'parent_email' => $this->faker->safeEmail,
            'approval_status' => 'pending',
            'reject_reason' => null,
            'school_id' => 1,
            'is_orphan' => false,
            'survey_id' => 1,
            'academic_year_id' => 1,
            'teaching_period_id' => 1,
            'orphan_type' => 'none',
            'education_grade_id' => 1,
            'in_national_curriculum' => true,
            'inter_sch_calendar_id' => null,
            'inter_sch_curriculum_id' => null,
            'inter_sch_education_grade_id' => null,
            'inter_sch_education_level_id' => null,
            'district_of_birth_id' => null,
            'is_offering_examinable_course' => 'no',
            'post_primary_institution_course_id' => null,
            'institution_examined_course_id' => null,
            'learner_special_needs' => json_encode([]),
            'learner_health_issues' => json_encode([]),
            'learner_talents' => json_encode([]),
            'learner_practical_skills' => json_encode([]),
            'principal_subjects' => json_encode([]),
            'subsidiary_subject' => json_encode([]),
            'familiar_language_id' => null,
        ];
    }

    public function pending()
    {
        return $this->state([
            'approval_status' => 'pending',
            'reject_reason' => null,
        ]);
    }

    public function approved()
    {
        return $this->state([
            'approval_status' => 'approved',
            'reject_reason' => null,
        ]);
    }

    public function rejected($reason = 'Test reason')
    {
        return $this->state([
            'approval_status' => 'rejected',
            'reject_reason' => $reason,
        ]);
    }
}
