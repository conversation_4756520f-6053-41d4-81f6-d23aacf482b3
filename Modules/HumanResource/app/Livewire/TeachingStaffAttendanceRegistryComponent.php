<?php

namespace Modules\HumanResource\Livewire;

use Livewire\Component;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Modules\HumanResource\Models\TeachingStaffAttendance;
use Modules\HumanResource\Traits\{HasInstitution, WithTeachingStaff};
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TeachingStaffAttendanceRegistryComponent extends Component
{
    use HasInstitution, WithTeachingStaff;

    public $selectedYear;
    public $selectedMonth;
    public $selectedWeek;
    public $weekDates = [];
    public $attendanceData = [];
    public $weekOptions = [];
    public $attendanceStats = [];
    public $canEditAttendance = false;
    public $months = [
        1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
        5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
        9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
    ];

    public function mount()
    {
        $this->canEditAttendance = Auth::user()->hasRole('institution-admin');
        $this->selectedYear = Carbon::now()->year;
        $this->selectedMonth = Carbon::now()->month;
        $this->generateWeekOptions();
        $this->setCurrentWeek();
        $this->loadAttendanceData();
    }

    /**
     * Generate week options for the selected month and year
     */
    public function generateWeekOptions()
    {
        $this->weekOptions = [];
        $startOfMonth = Carbon::create($this->selectedYear, $this->selectedMonth, 1);
        $endOfMonth = $startOfMonth->copy()->endOfMonth();
        
        $currentDate = $startOfMonth->copy()->startOfWeek(Carbon::MONDAY);
        $weekNumber = 1;
        
        while ($currentDate->lte($endOfMonth)) {
            $weekStart = $currentDate->copy();
            $weekEnd = $currentDate->copy()->endOfWeek(Carbon::SUNDAY);
            
            // If week end goes beyond the month, limit it to end of month
            if ($weekEnd->gt($endOfMonth)) {
                $weekEnd = $endOfMonth->copy();
            }
            
            $this->weekOptions[] = [
                'number' => $weekNumber,
                'label' => "Week {$weekNumber} ({$weekStart->format('M d')} - {$weekEnd->format('M d')})",
                'start_date' => $weekStart->format('Y-m-d'),
                'end_date' => $weekEnd->format('Y-m-d'),
            ];
            
            $currentDate->addWeek();
            $weekNumber++;
        }
    }

    /**
     * Set the current week based on today's date
     */
    private function setCurrentWeek()
    {
        $today = Carbon::now();
        
        foreach ($this->weekOptions as $week) {
            if ($today->between($week['start_date'], $week['end_date'])) {
                $this->selectedWeek = $week['number'];
                return;
            }
        }
        
        // If current date is not in any week of the selected month, default to week 1
        $this->selectedWeek = 1;
    }

    /**
     * Update weeks when year or month changes
     */
    public function updatedSelectedYear()
    {
        $this->generateWeekOptions();
        $this->selectedWeek = 1;
        $this->loadAttendanceData();
    }

    public function updatedSelectedMonth()
    {
        $this->generateWeekOptions();
        $this->selectedWeek = 1;
        $this->loadAttendanceData();
    }

    /**
     * Update attendance data when week changes
     */
    public function updatedSelectedWeek()
    {
        $this->loadAttendanceData();
    }

    /**
     * Generate dates for the selected week
     */
    private function generateWeekDates()
    {
        $this->weekDates = [];
        
        if (empty($this->weekOptions) || !isset($this->weekOptions[$this->selectedWeek - 1])) {
            return;
        }
        
        $selectedWeekData = $this->weekOptions[$this->selectedWeek - 1];
        $period = CarbonPeriod::create($selectedWeekData['start_date'], $selectedWeekData['end_date']);
        
        foreach ($period as $date) {
            $this->weekDates[] = [
                'date' => $date->format('Y-m-d'),
                'day' => $date->format('d'),
                'dayName' => $date->format('D'),
                'fullDayName' => $date->format('l'),
                'isWeekend' => $date->isWeekend(),
            ];
        }
    }

    /**
     * Load teaching staff and their attendance records for the selected week
     */
    private function loadAttendanceData()
    {
        $this->generateWeekDates();
        
        if (empty($this->weekDates)) {
            $this->attendanceData = [];
            return;
        }
        
        $institutionId = $this->getInstitution()->id;
        
        $teachingStaff = $this->getTeachingStaff([
            'paginate' => false,
            'relationships' => ['person'],
        ]);

        // Initialize attendance matrix
        $attendanceMatrix = [];
        $staffIds = $teachingStaff->pluck('employee_id')->toArray();
        
        foreach ($this->weekDates as $dateInfo) {
            $date = $dateInfo['date'];
            foreach ($staffIds as $staffId) {
                $attendanceMatrix[$staffId][$date] = '-';
            }
        }

        // Get existing attendance records for the week
        $startDate = $this->weekDates[0]['date'];
        $endDate = end($this->weekDates)['date'];
        
        $attendanceRecords = TeachingStaffAttendance::forSchool($institutionId)
            ->inDateRange($startDate, $endDate)
            ->whereIn('teaching_staff_id', $staffIds)
            ->select('teaching_staff_id', 'attendance_date', 'status')
            ->get();

        // Populate the matrix with existing records
        foreach ($attendanceRecords as $record) {
            $date = Carbon::parse($record->attendance_date)->format('Y-m-d');
            $staffId = $record->teaching_staff_id;
            
            if (isset($attendanceMatrix[$staffId][$date])) {
                $attendanceMatrix[$staffId][$date] = $record->status;
            }
        }

        // Build final attendance data structure
        $this->attendanceData = [];
        foreach ($teachingStaff as $staff) {
            $staffData = [
                'staff_id' => $staff->employee_id,
                'full_name' => $staff->person?->full_name ?? 'Unknown',
                'attendance' => $attendanceMatrix[$staff->employee_id] ?? [],
            ];

            $this->attendanceData[] = $staffData;
        }
        
        $this->calculateAttendanceStats();
    }
    
    /**
     * Calculate attendance statistics for the week
     */
    private function calculateAttendanceStats()
    {
        $totalStaff = count($this->attendanceData);
        $totalWorkingDays = count(array_filter($this->weekDates, fn($date) => !$date['isWeekend']));
        $totalPossibleAttendance = $totalStaff * $totalWorkingDays;
        
        $presentCount = 0;
        $absentCount = 0;
        $unmarkedCount = 0;
        
        foreach ($this->attendanceData as $staff) {
            foreach ($this->weekDates as $dateInfo) {
                if (!$dateInfo['isWeekend']) {
                    $status = $staff['attendance'][$dateInfo['date']] ?? '-';
                    match ($status) {
                        'present' => $presentCount++,
                        'absent' => $absentCount++,
                        default => $unmarkedCount++,
                    };
                }
            }
        }
        
        $this->attendanceStats = [
            'total_staff' => $totalStaff,
            'total_working_days' => $totalWorkingDays,
            'total_possible_attendance' => $totalPossibleAttendance,
            'present_count' => $presentCount,
            'absent_count' => $absentCount,
            'unmarked_count' => $unmarkedCount,
            'attendance_rate' => $totalPossibleAttendance > 0 ? round(($presentCount / $totalPossibleAttendance) * 100, 1) : 0,
        ];
    }

    /**
     * Update attendance status for a specific staff member and date
     */
    public function updateAttendance($staffId, $date, $status)
    {
        // Check if user has permission to edit attendance
        if (!$this->canEditAttendance) {
            $this->dispatch('notify', [
                'title' => 'Access Denied',
                'message' => 'You do not have permission to update attendance records.',
                'status' => 'error'
            ]);
            return;
        }

        try {
            DB::beginTransaction();

            $institutionId = $this->getInstitution()->id;
            logger()->info("Updating attendance for Staff ID: {$staffId}, Date: {$date}, Status: {$status}, Institution ID: {$institutionId}");

            if ($status === 'unmarked' || $status === '-') {
                TeachingStaffAttendance::where([
                    'school_id' => $institutionId,
                    'teaching_staff_id' => $staffId,
                    'attendance_date' => $date,
                ])->delete();
                
                $finalStatus = '-';
            } else {
                $attendance = TeachingStaffAttendance::updateOrCreate(
                    [
                        'school_id' => $institutionId,
                        'teaching_staff_id' => $staffId,
                        'attendance_date' => $date,
                    ],
                    [
                        'status' => $status,
                        'recorded_by' => Auth::user()->id,
                    ]
                );
                
                $finalStatus = $status;
            }

            // Update the local data
            foreach ($this->attendanceData as &$staff) {
                if ($staff['staff_id'] == $staffId) {
                    $staff['attendance'][$date] = $finalStatus;
                    break;
                }
            }
            
            // Recalculate statistics
            $this->calculateAttendanceStats();

            DB::commit();
            
            $statusText = match($finalStatus) {
                'present' => 'Present',
                'absent' => 'Absent',
                default => 'Unmarked'
            };
            
            $this->dispatch('notify', [
                'title' => 'Attendance Updated',
                'message' => "Attendance marked as {$statusText}",
                'status' => 'success'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            $this->dispatch('notify', [
                'title' => 'Error',
                'message' => 'Failed to update attendance: ' . $e->getMessage(),
                'status' => 'error'
            ]);
        }
    }

    // #[Layout('core::institution.layouts.design')]
    public function render()
    {
        return view('humanresource::livewire.teaching-staff-attendance-registry-component')
        ->extends('core::institution.layouts.design')
        ->section('content');
    }
}