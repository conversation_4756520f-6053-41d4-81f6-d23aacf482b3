<?php

namespace Modules\HumanResource\Livewire;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Modules\HumanResource\Models\TeachingStaffAttendance;
use Modules\HumanResource\Traits\{HasInstitution, WithTeachingStaff};

class TeachingStaffAttendanceComponent extends Component
{
    use HasInstitution, WithTeachingStaff;

    public $startDate;
    public $endDate;
    public $dates = [];
    public $attendanceData = [];
    public $daysToShow = 14;
    public $selectedYear;
    public $selectedMonth;
    public $filterMode = 'last_days';
    public $selectedStaff = [];
    public $availableStaff = [];
    public $showAllStaff = true;
    public $months = [
        1 => 'January',
        2 => 'February',
        3 => 'March',
        4 => 'April',
        5 => 'May',
        6 => 'June',
        7 => 'July',
        8 => 'August',
        9 => 'September',
        10 => 'October',
        11 => 'November',
        12 => 'December'
    ];

    public function mount()
    {
        $this->selectedYear = Carbon::now()->year;
        $this->selectedMonth = Carbon::now()->month;
        $this->loadAvailableStaff();
        $this->setDateRange();
        $this->generateDates();
        $this->loadAttendanceData();
    }

    /**
     * Set date range based on filter mode
     */
    private function setDateRange()
    {
        if ($this->filterMode === 'month') {
            // Set to selected month
            $startOfMonth = Carbon::create($this->selectedYear, $this->selectedMonth, 1);
            $this->startDate = $startOfMonth->format('Y-m-d');
            $this->endDate = $startOfMonth->endOfMonth()->format('Y-m-d');
        } else {
            // Default: last X days
            $this->endDate = Carbon::today()->format('Y-m-d');
            $this->startDate = Carbon::today()->subDays($this->daysToShow - 1)->format('Y-m-d');
        }
    }

    /**
     * Update when filter mode changes
     */
    public function updatedFilterMode()
    {
        $this->setDateRange();
        $this->generateDates();
        $this->loadAttendanceData();
    }

    /**
     * Update when year changes
     */
    public function updatedSelectedYear()
    {
        if ($this->filterMode === 'month') {
            $this->setDateRange();
            $this->generateDates();
            $this->loadAttendanceData();
        }
    }

    /**
     * Update when month changes
     */
    public function updatedSelectedMonth()
    {
        if ($this->filterMode === 'month') {
            $this->setDateRange();
            $this->generateDates();
            $this->loadAttendanceData();
        }
    }

    /**
     * Update when days to show changes
     */
    public function updatedDaysToShow()
    {
        if ($this->filterMode === 'last_days') {
            $this->setDateRange();
            $this->generateDates();
            $this->loadAttendanceData();
        }
    }

    /**
     * Update when staff selection changes
     */
    public function updatedSelectedStaff()
    {
        $this->loadAttendanceData();
    }

    /**
     * Update when show all staff toggle changes
     */
    public function updatedShowAllStaff()
    {
        if ($this->showAllStaff) {
            $this->selectedStaff = [];
        }
        $this->loadAttendanceData();
    }

    /**
     * Load available staff for selection
     */
    private function loadAvailableStaff()
    {
        $institutionId = $this->getInstitution()->id;

        $teachingStaff = $this->getTeachingStaff([
            'paginate' => false,
            'relationships' => ['person'],
            'order_by' => 'date_created',
            'order_direction' => 'asc',
        ]);

        $this->availableStaff = $teachingStaff->map(function ($staff) {
            return [
                'id' => $staff->employee_id,
                'name' => $staff->person?->full_name ?? 'Unknown',
                'email' => $staff->person?->email ?? null,
            ];
        })->toArray();
    }

    /**
     * Select all available staff
     */
    public function selectAllStaff()
    {
        $this->selectedStaff = collect($this->availableStaff)->pluck('id')->toArray();
        $this->showAllStaff = false;
        $this->loadAttendanceData();
    }

    /**
     * Clear all staff selections
     */
    public function clearAllStaff()
    {
        $this->selectedStaff = [];
        $this->showAllStaff = true;
        $this->loadAttendanceData();
    }

    /**
     * Toggle individual staff selection
     */
    public function toggleStaff($staffId)
    {
        if (in_array($staffId, $this->selectedStaff)) {
            $this->selectedStaff = array_diff($this->selectedStaff, [$staffId]);
        } else {
            $this->selectedStaff[] = $staffId;
        }

        $this->showAllStaff = empty($this->selectedStaff);
        $this->loadAttendanceData();
    }

    /**
     * Generate array of dates for the table header
     */
    private function generateDates()
    {
        $period = CarbonPeriod::create($this->startDate, $this->endDate);
        $this->dates = [];

        foreach ($period as $date) {
            $this->dates[] = [
                'date' => $date->format('Y-m-d'),
                'day' => $date->format('d'),
                'month' => $date->format('m'),
                'year' => $date->format('Y'),
                'monthName' => $date->format('M'),
                'dayName' => $date->format('D'),
                'fullDayName' => $date->format('l'),
                'isWeekend' => $date->isWeekend(),
            ];
        }
    }

    /**
     * Load teaching staff and their attendance records
     */
    private function loadAttendanceData()
    {
        $institutionId = $this->getInstitution()->id;

        $teachingStaff = $this->getTeachingStaff([
            'paginate' => false,
            'relationships' => ['person'],
        ]);

        // Filter by selected staff if not showing all
        if (!$this->showAllStaff && !empty($this->selectedStaff)) {
            $teachingStaff = $teachingStaff->whereIn('employee_id', $this->selectedStaff);
        }

        $dateStaffMatrix = [];
        $staffIds = $teachingStaff->pluck('employee_id')->toArray();

        foreach ($this->dates as $dateInfo) {
            $date = $dateInfo['date'];
            foreach ($staffIds as $staffId) {
                $dateStaffMatrix[$date][$staffId] = '-';
            }
        }

        $attendanceRecords = TeachingStaffAttendance::forSchool($institutionId)
            ->inDateRange($this->startDate, $this->endDate)
            ->whereIn('teaching_staff_id', $staffIds)
            ->select('teaching_staff_id', 'attendance_date', 'status')
            ->get();

        foreach ($attendanceRecords as $record) {
            $date = Carbon::parse($record->attendance_date)->format('Y-m-d');
            $staffId = $record->teaching_staff_id;

            if (isset($dateStaffMatrix[$date][$staffId])) {
                $status = match ($record->status) {
                    'present' => 'P',
                    'absent' => 'A',
                    default => '-',
                };
                $dateStaffMatrix[$date][$staffId] = $status;
            }
        }

        $this->attendanceData = [];
        foreach ($teachingStaff as $staff) {
            $staffAttendance = [
                'staff_id' => $staff->employee_id,
                'full_name' => $staff->person?->full_name ?? 'Unknown',
                'attendance' => [],
            ];

            foreach ($this->dates as $dateInfo) {
                $date = $dateInfo['date'];
                $staffAttendance['attendance'][$date] = $dateStaffMatrix[$date][$staff->employee_id] ?? '-';
            }

            $this->attendanceData[] = $staffAttendance;
        }

        // Dispatch event to notify frontend
        $this->dispatch('attendance-data-updated', [
            'message' => 'Attendance data loaded successfully',
            'filter_mode' => $this->filterMode,
            'date_range' => [
                'start' => $this->startDate,
                'end' => $this->endDate
            ]
        ]);
    }

    /**
     * Get month color class for styling
     */
    public function getMonthColorClass($month)
    {
        $monthColors = [
            '01' => 'bg-primary-dim', // January
            '02' => 'bg-success-dim', // February
            '03' => 'bg-info-dim',    // March
            '04' => 'bg-warning-dim', // April
            '05' => 'bg-danger-dim',  // May
            '06' => 'bg-gray-dim',    // June
            '07' => 'bg-purple-dim',  // July
            '08' => 'bg-azure-dim',   // August
            '09' => 'bg-pink-dim',    // September
            '10' => 'bg-orange-dim',  // October
            '11' => 'bg-teal-dim',    // November
            '12' => 'bg-blue-dim',    // December
        ];

        return $monthColors[$month] ?? 'bg-light';
    }

    /**
     * Get summary statistics for the current date range
     */
    public function getAttendanceSummary()
    {
        $totalDays = count($this->dates);
        $weekendDays = collect($this->dates)->where('isWeekend', true)->count();
        $weekdays = $totalDays - $weekendDays;

        return [
            'total_days' => $totalDays,
            'weekdays' => $weekdays,
            'weekend_days' => $weekendDays,
        ];
    }

    // #[Layout('core::institution.layouts.design')]
    public function render()
    {
        // Register Livewire components
        return view('humanresource::livewire.teaching-staff-attendance-component')
            ->extends('core::institution.layouts.design')
            ->section('content');
    }
}
