<?php

namespace Modules\HumanResource\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Core\Models\Institutions\{TeachingStaff, School};
use Modules\Core\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TeachingStaffAttendance extends Model
{
    protected $guarded = [];

    protected $table = 'teaching_staff_attendances';

    const CREATED_AT = 'date_created';

    const UPDATED_AT = 'date_updated';

    protected $hidden = ['date_created', 'date_updated'];

    protected $casts = [
        'attendance_date' => 'date:Y-m-d',
        'date_created' => 'datetime',
        'date_updated' => 'datetime',
    ];

    /**
     * The teaching staff this attendance record belongs to
     */
    public function teachingStaff(): BelongsTo
    {
        return $this->belongsTo(TeachingStaff::class, 'teaching_staff_id', 'employee_id');
    }

    /**
     * The school where attendance is recorded
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * The user who recorded this attendance
     */
    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

    /**
     * Scope to filter by date range
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('attendance_date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by specific month and year
     */
    public function scopeInMonth($query, $year, $month)
    {
        $startDate = \Carbon\Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        return $query->inDateRange($startDate, $endDate);
    }

    /**
     * Scope to filter by teaching staff
     */
    public function scopeForTeachingStaff($query, $teachingStaffId)
    {
        return $query->where('teaching_staff_id', $teachingStaffId);
    }

    /**
     * Scope to filter by school
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope to filter by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}