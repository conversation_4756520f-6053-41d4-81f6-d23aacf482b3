<?php

namespace Modules\HumanResource\Traits;

use Modules\Core\Models\Institutions\TeachingStaff;

trait WithTeachingStaff
{
    /**
     * Get teaching staff for an institution with filtering and relationship loading options.
     *
     * @param array $options Array of options to customize the query
     * @return \Illuminate\Pagination\LengthAwarePaginator|\Illuminate\Database\Eloquent\Collection
     */
    public function getTeachingStaff(array $options = [])
    {
        // Default options
        $defaults = [
            'institution_id' => null,
            'only_active' => true,
            'relationships' => ['person.teacher.highest_qualification', 'person'],
            'paginate' => true,
            'per_page' => $this->perPage ?? 25,
            'order_by' => 'date_created',
            'order_direction' => 'desc',
            'additional_where' => null,
        ];

        // Merge provided options with defaults
        $options = array_merge($defaults, $options);

        // Get institution ID
        $institutionId = $options['institution_id'] ?? 
            (method_exists($this, 'getInstitution') ? $this->getInstitution()->id : null);

        if (!$institutionId) {
            throw new \Exception('Institution ID is required to get teaching staff');
        }

        // Start building the query
        $query = TeachingStaff::query()->where('school_id', $institutionId);

        // Filter for active employees only
        if ($options['only_active']) {
            $query->whereHas('school_employee', function ($query) {
                $query->whereNull('date_ended');
            });
        }

        // Load relationships
        if (!empty($options['relationships'])) {
            $query->with($options['relationships']);
        }

        // Add any additional where clauses if provided
        if (is_callable($options['additional_where'])) {
            $options['additional_where']($query);
        }

        // Apply ordering
        if ($options['order_by']) {
            $query->orderBy($options['order_by'], $options['order_direction']);
        }

        // Return either a paginated result or collection based on options
        if ($options['paginate']) {
            return $query->paginate($options['per_page']);
        }

        return $query->get();
    }
}