<?php

namespace Modules\HumanResource\Traits;

use Illuminate\Support\Facades\Auth;

trait HasInstitution
{
    /**
     * Get the institution associated with the authenticated user.
     * 
     * @return \App\Models\Institutions\School|null
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    protected function getInstitution()
    {
        $user = Auth::user();
        
        if ($user->school) {
            return $user->school->load(['school_type', 'district']);
        } elseif ($user->school_contact && $user->school_contact->school) {
            return $user->school_contact->school->load(['school_type', 'district']);
        }
        
        abort(401, 'You do not have the rights to perform this action');
    }
}