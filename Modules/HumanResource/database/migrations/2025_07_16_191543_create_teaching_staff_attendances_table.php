<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teaching_staff_attendances', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('teaching_staff_id')->comment('ID of the teacher referencing the employee_id in teaching_staff table');
            $table->unsignedBigInteger('school_id')->comment('ID of the school where the attendance is recorded');
            $table->date('attendance_date')->nullable()->comment('Date of attendance');
            $table->enum('status', ['present', 'absent', 'unmarked'])->comment('Attendance status: present ,absent, or unmarked');
            $table->unsignedBigInteger('recorded_by')->nullable()->comment('User who recorded the attendance');
            $table->timestamp('date_created')->comment('Date-time timestamp assigned at creation of the record')->nullable();
            $table->timestamp('date_updated')->comment('Date-time timestamp assigned at creation of the record')->nullable();
            $table->foreign('teaching_staff_id')->references('employee_id')->on('teaching_staff')->onDelete('set null');
            $table->foreign('school_id')->references('id')->on('schools')->onDelete('set null');
            $table->foreign('recorded_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teaching_staff_attendances');
    }
};