@section('title', 'Teaching Staff Attendance Registry')
<div class="w-100" {{-- x-data="attendanceRegistry()" --}}>
    <div class="nk-block-head nk-block-head-sm">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">Teaching Staff Attendance Registry</h3>
                <div class="nk-block-des text-soft">
                    <p>Record and manage teaching staff attendance</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Section -->
    @if (!empty($attendanceStats))
        <div class="nk-block">
            <div class="card card-bordered">
                <div class="card-inner">
                    <div class="card-title-group">
                        <div class="card-title">
                            <h6 class="title">Weekly Attendance Statistics</h6>
                        </div>
                    </div>
                    <div class="row g-4 mt-1">
                        <div class="col-sm-6 col-lg-3">
                            <div class="statbox">
                                <div class="amount">{{ $attendanceStats['total_staff'] }}</div>
                                <div class="subtitle">Total Staff</div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-lg-3">
                            <div class="statbox">
                                <div class="amount text-dark-teal">{{ $attendanceStats['present_count'] }}</div>
                                <div class="subtitle">Present</div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-lg-3">
                            <div class="statbox">
                                <div class="amount text-danger">{{ $attendanceStats['absent_count'] }}</div>
                                <div class="subtitle">Absent</div>
                            </div>
                        </div>
                        {{-- <div class="col-sm-6 col-lg-3">
                        <div class="statbox">
                            <div class="amount text-warning">{{ $attendanceStats['unmarked_count'] }}</div>
                            <div class="subtitle">Unmarked</div>
                        </div>
                    </div> --}}
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="progress-wrap">
                                <div class="progress-text">
                                    <span>Attendance Rate</span>
                                    <span class="progress-amount">{{ $attendanceStats['attendance_rate'] }}%</span>
                                </div>
                                <div class="progress progress-md">
                                    <div class="progress-bar" data-progress="{{ $attendanceStats['attendance_rate'] }}"
                                        style="width: {{ $attendanceStats['attendance_rate'] }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Filters Section -->
    <div class="nk-block">
        <div class="card card-bordered">
            <div class="card-inner">
                <div class="row g-4">
                    <!-- Year Filter -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Year</label>
                            <select class="form-control" wire:model.live="selectedYear">
                                @for ($year = Carbon\Carbon::now()->year - 2; $year <= Carbon\Carbon::now()->year + 1; $year++)
                                    <option value="{{ $year }}">{{ $year }}</option>
                                @endfor
                            </select>
                        </div>
                    </div>

                    <!-- Month Filter -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Month</label>
                            <select class="form-control" wire:model.live="selectedMonth">
                                @foreach ($months as $monthNum => $monthName)
                                    <option value="{{ $monthNum }}">{{ $monthName }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Week Filter -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Week</label>
                            <select class="form-control" wire:model.live="selectedWeek">
                                @foreach ($weekOptions as $week)
                                    <option value="{{ $week['number'] }}">{{ $week['label'] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Banner -->
    <livewire:human-resource::notification-banner />
    @if (!$canEditAttendance)
        <div class="alert alert-warning alert-icon mt-2" style="padding: 0.5rem;">
            <strong>Read-Only Mode:</strong> You can view attendance records but cannot make changes. Contact an
            administrator for edit access.
        </div>
    @endif
    <!-- Attendance Table -->
    <div class="nk-block">
        <div class="card card-stretch card-bordered border-dark-teal">
            <div class="card-inner-group">
                <div class="card-inner p-0">
                    <div class="attendance-registry-wrapper">
                        <div class="nk-tb-list attendance-registry-table">
                            <!-- Table Header -->
                            <div class="nk-tb-item nk-tb-head bg-secondary sticky-top">
                                <!-- Staff Name Column -->
                                <div class="nk-tb-col sticky-name-col bg-secondary"
                                    style="min-width: 250px; border: 1px solid #e5e9f2;">
                                    <span class="sub-text text-white fw-bold">STAFF NAME</span>
                                </div>

                                <!-- Date Columns -->
                                @foreach ($weekDates as $dateInfo)
                                    <div class="nk-tb-col text-center {{ $dateInfo['isWeekend'] ? 'bg-warning-dim' : 'bg-primary-dim' }}"
                                        style="min-width: 120px; border: 1px solid #e5e9f2;">
                                        <div class="d-flex flex-column">
                                            <span class="sub-text text-dark fw-bold">{{ $dateInfo['dayName'] }}</span>
                                            <span class="fs-12px text-dark">{{ $dateInfo['day'] }}</span>
                                            @if ($dateInfo['isWeekend'])
                                                <small class="text-warning fw-bold">Weekend</small>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Table Body -->
                            @forelse($attendanceData as $staff)
                                <div class="nk-tb-item">
                                    <!-- Staff Name -->
                                    <div class="nk-tb-col sticky-name-col bg-white"
                                        style="min-width: 250px; border: 1px solid #e5e9f2;">
                                        <div class="d-flex flex-column">
                                            <span class="tb-lead fw-bold">{{ $staff['full_name'] }}</span>
                                        </div>
                                    </div>

                                    <!-- Attendance Cells -->
                                    @foreach ($weekDates as $dateInfo)
                                        @php
                                            $date = $dateInfo['date'];
                                            $status = $staff['attendance'][$date] ?? '-';
                                            $isWeekend = $dateInfo['isWeekend'];
                                        @endphp

                                        <div class="nk-tb-col text-center {{ $isWeekend ? 'bg-gray-50' : 'bg-white' }}"
                                            style="min-width: 120px; border: 1px solid #e5e9f2;">

                                            @if ($isWeekend)
                                                <!-- Weekend - Show N/A -->
                                                <span class="badge bg-gray">N/A</span>
                                            @else
                                                @if ($canEditAttendance)
                                                    <!-- Attendance Buttons (Editable) -->
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button type="button"
                                                            class="btn {{ $status === 'present' ? 'btn-dark-teal' : 'btn-outline-dark-teal' }}"
                                                            wire:click="updateAttendance('{{ $staff['staff_id'] }}', '{{ $date }}', 'present')"
                                                            {{-- x-on:click="showToast('Marking as Present...')" --}}>
                                                            P
                                                        </button>
                                                        <button type="button"
                                                            class="btn {{ $status === 'absent' ? 'btn-danger' : 'btn-outline-danger' }}"
                                                            wire:click="updateAttendance('{{ $staff['staff_id'] }}', '{{ $date }}', 'absent')"
                                                            {{-- x-on:click="showToast('Marking as Absent...')" --}}>
                                                            A
                                                        </button>
                                                        <button type="button"
                                                            class="btn {{ $status === '-' ? 'btn-light border border-secondary' : 'btn-outline-secondary' }}"
                                                            wire:click="updateAttendance('{{ $staff['staff_id'] }}', '{{ $date }}', 'unmarked')"
                                                            {{-- x-on:click="showToast('Clearing attendance...')" --}}>
                                                            -
                                                        </button>
                                                    </div>
                                                @else
                                                    <!-- Read-only Status Display -->
                                                    @if ($status === 'present')
                                                        <span class="badge bg-dark-teal">P</span>
                                                    @elseif ($status === 'absent')
                                                        <span class="badge bg-danger text-white">A</span>
                                                    @else
                                                        <span class="badge bg-light text-dark">-</span>
                                                    @endif
                                                @endif
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            @empty
                                <div class="p-5">
                                    <div class="alert alert-secondary alert-icon">
                                        <em class="icon ni ni-alert-circle"></em> No teaching staff found for this
                                        institution.
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Legend -->
    <div class="nk-block mt-3">
        <div class="card card-bordered">
            <div class="card-inner">
                <div class="card-title-group">
                    <div class="card-title">
                        <h6 class="title">Legend & Instructions</h6>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex g-3 mb-2">
                                @if ($canEditAttendance)
                                    <div class="me-4">
                                        <button class="btn btn-sm btn-dark-teal">P</button> - Present
                                    </div>
                                    <div class="me-4">
                                        <button class="btn btn-sm btn-danger">A</button> - Absent
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-light border border-secondary">-</button> -
                                        Unmarked
                                    </div>
                                @else
                                    <div class="me-4">
                                        <span class="badge bg-success">Present</span> - Present
                                    </div>
                                    <div class="me-4">
                                        <span class="badge bg-danger">Absent</span> - Absent
                                    </div>
                                    <div>
                                        <span class="badge bg-light text-dark">-</span> - Unmarked
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <p class="text-muted small mb-0">
                                @if ($canEditAttendance)
                                    <strong>Instructions:</strong> Click on the P, A, or - buttons to mark attendance.
                                    Weekend days are marked as N/A and cannot be edited.
                                @else
                                    <strong>View Only:</strong> You are viewing attendance records in read-only mode.
                                    Only users with institution-admin role can edit attendance records.
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    {{-- <div id="toast-container" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
        <div class="toast align-items-center border-0"
            :class="toastType === 'success' ? 'bg-success text-white' : 'bg-danger text-white'" role="alert"
            aria-live="assertive" aria-atomic="true" x-show="showToastNotification" x-transition.opacity
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform translate-x-full"
            x-transition:enter-end="opacity-100 transform translate-x-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform translate-x-0"
            x-transition:leave-end="opacity-0 transform translate-x-full" style="display: none;">
            <div class="d-flex">
                <div class="toast-body" x-text="toastMessage">
                    Loading...
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto"
                    x-on:click="hideToast()"></button>
            </div>
        </div>
    </div> --}}
</div>
