@section('title', 'Teaching Staff Attendance')
<div class="w-100" x-data="attendanceFilters()">
    <div class="nk-block-head nk-block-head-sm">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">Teaching Staff Attendance</h3>
                <div class="nk-block-des text-soft">
                    @php
                        $summary = $this->getAttendanceSummary();
                    @endphp
                    <p>Showing attendance for 
                        <span x-show="$wire.filterMode === 'last_days'">the last {{ count($dates) }} days</span>
                        <span x-show="$wire.filterMode === 'month'">{{ $this->months[$this->selectedMonth] ?? 'Selected Month' }} {{ $this->selectedYear }}</span>
                        with day names 
                       ({{ $summary['weekdays'] }} weekdays, {{ $summary['weekend_days'] }} weekend days)</p>
                    <p class="text-muted"><em><small>Scroll horizontally to view all dates. Weekend days are highlighted.</small></em></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="nk-block">
        <div class="card card-bordered">
            <div class="card-inner">
                <div class="card-title-group">
                    <div class="card-title">
                        <h6 class="title">
                            <em class="icon ni ni-filter"></em> Attendance Filters
                        </h6>
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <!-- Filter Mode Selection -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Filter Mode</label>
                            <select class="form-control" wire:model.live="filterMode" x-model="$wire.filterMode">
                                <option value="last_days">Last X Days</option>
                                <option value="month">Specific Month</option>
                            </select>
                        </div>
                    </div>

                    <!-- Days to Show (for last_days mode) -->
                    <div class="col-md-3" x-show="$wire.filterMode === 'last_days'" x-transition>
                        <div class="form-group">
                            <label class="form-label">Days to Show</label>
                            <select class="form-control" wire:model.live="daysToShow">
                                <option value="7">Last 7 Days</option>
                                <option value="14">Last 14 Days</option>
                                <option value="21">Last 21 Days</option>
                                <option value="31">Last 31 Days</option>
                            </select>
                        </div>
                    </div>

                    <!-- Year Selection (for month mode) -->
                    <div class="col-md-2" x-show="$wire.filterMode === 'month'" x-transition>
                        <div class="form-group">
                            <label class="form-label">Year</label>
                            <select class="form-control" wire:model.live="selectedYear">
                                @for($year = Carbon\Carbon::now()->year - 2; $year <= Carbon\Carbon::now()->year + 1; $year++)
                                    <option value="{{ $year }}">{{ $year }}</option>
                                @endfor
                            </select>
                        </div>
                    </div>

                    <!-- Month Selection (for month mode) -->
                    <div class="col-md-4" x-show="$wire.filterMode === 'month'" x-transition>
                        <div class="form-group">
                            <label class="form-label">Month</label>
                            <select class="form-control" wire:model.live="selectedMonth">
                                @foreach($months as $monthNum => $monthName)
                                    <option value="{{ $monthNum }}">{{ $monthName }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Staff Selection Section -->
                <div class="row g-3 mt-3">
                    <div class="col-12">
                        <div class="card card-bordered bg-light">
                            <div class="card-inner p-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="card-title mb-0">
                                        <em class="icon ni ni-users"></em> Teaching Staff Filter
                                    </h6>
                                </div>

                                <!-- Show All Toggle -->
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" 
                                           wire:model.live="showAllStaff" id="showAllStaff">
                                    <label class="form-check-label fw-bold" for="showAllStaff">
                                        Show All Teaching Staff
                                        <small class="text-muted d-block">
                                            ({{ count($availableStaff) }} total staff members)
                                        </small>
                                    </label>
                                </div>

                                <!-- Staff Selection Grid -->
                                <div x-show="!$wire.showAllStaff" x-transition>
                                    <!-- Search Box -->
                                    <div class="form-group mb-3">
                                        <label class="form-label">Search Staff</label>
                                        <input type="text" class="form-control" 
                                               x-model="staffSearchTerm"
                                               placeholder="Search by name or email...">
                                    </div>

                                    <div class="row g-2 staff-selection-grid">
                                        @foreach($availableStaff as $staff)
                                            <div class="col-md-3 col-sm-6" 
                                                 x-show="filterStaff({{ json_encode($staff) }})"
                                                 x-transition>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           wire:model.live="selectedStaff" 
                                                           value="{{ $staff['id'] }}"
                                                           id="staff_{{ $staff['id'] }}">
                                                    <label class="form-check-label" for="staff_{{ $staff['id'] }}">
                                                        <span class="fw-medium">{{ $staff['name'] }}</span>
                                                        @if($staff['email'])
                                                            <small class="text-muted d-block">{{ $staff['email'] }}</small>
                                                        @endif
                                                    </label>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>

                                    <!-- Selected Staff Summary -->
                                    <div class="mt-3 p-2 bg-white rounded" x-show="$wire.selectedStaff.length > 0">
                                        <small class="text-muted">
                                            <em class="icon ni ni-check-circle text-success"></em>
                                            <strong>Selected:</strong> 
                                            <span x-text="$wire.selectedStaff.length"></span> 
                                            of {{ count($availableStaff) }} staff members
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filter Summary -->
                <div class="mt-3 p-2 bg-light rounded">
                    <small class="text-muted">
                        <em class="icon ni ni-calendar"></em>
                        <strong>Current Filter:</strong>
                        <span x-show="$wire.filterMode === 'last_days'">
                            Showing last <span x-text="$wire.daysToShow"></span> days from {{ Carbon\Carbon::today()->format('M d, Y') }}
                        </span>
                        <span x-show="$wire.filterMode === 'month'">
                            Showing {{ $this->months[$this->selectedMonth] ?? 'Month' }} {{ $this->selectedYear }} 
                            ({{ Carbon\Carbon::create($this->selectedYear, $this->selectedMonth, 1)->format('M 1') }} - 
                            {{ Carbon\Carbon::create($this->selectedYear, $this->selectedMonth, 1)->endOfMonth()->format('M d, Y') }})
                        </span>
                        <br>
                        <em class="icon ni ni-users"></em>
                        <strong>Staff:</strong>
                        <span x-show="$wire.showAllStaff">
                            All {{ count($availableStaff) }} teaching staff members
                        </span>
                        <span x-show="!$wire.showAllStaff">
                            <span x-text="$wire.selectedStaff.length"></span> of {{ count($availableStaff) }} selected staff members
                        </span>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="nk-block">
        <div class="card card-stretch card-bordered border-dark-teal">
            <div class="card-inner-group">
                <div class="card-inner p-0 position-relative">
                    <!-- Loading Overlay -->
                    <div class="loading-overlay" wire:loading wire:target="filterMode,selectedYear,selectedMonth,daysToShow,selectedStaff,showAllStaff,selectAllStaff,clearAllStaff,loadAttendanceData">
                        <div class="text-center">
                            <div class="text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">...</small>
                            </div>
                        </div>
                    </div>

                    <div class="attendance-table-wrapper">
                        <div class="nk-tb-list attendance-table"
                             wire:loading.class="opacity-50"
                             wire:target="filterMode,selectedYear,selectedMonth,daysToShow,selectedStaff,showAllStaff,selectAllStaff,clearAllStaff,loadAttendanceData">
                            <!-- Table Header -->
                            <div class="nk-tb-item nk-tb-head bg-secondary sticky-top">
                                <!-- Name column -->
                                <div class="nk-tb-col sticky-name-col bg-secondary" style="min-width: 240px; border: 1px solid #e5e9f2;">
                                    <div class="d-flex flex-column">
                                        <span class="sub-text text-white">STAFF NAME</span>
                                        <small class="text-white-50 fs-11px">P | A | Unmarked</small>
                                    </div>
                                </div>

                                <!-- Date columns - one for each day -->
                                @php
                                    $currentMonth = null;
                                    $currentMonthClass = '';
                                @endphp

                                @foreach ($dates as $dateInfo)
                                    @php
                                        // Check if month has changed to apply different styling
                                        if ($currentMonth !== $dateInfo['month']) {
                                            $currentMonth = $dateInfo['month'];
                                            $currentMonthClass = $this->getMonthColorClass($currentMonth);
                                        }
                                        
                                        // Add weekend styling
                                        $weekendClass = $dateInfo['isWeekend'] ? 'weekend-header' : '';
                                    @endphp

                                    <div class="nk-tb-col text-center {{ $currentMonthClass }} {{ $weekendClass }}"
                                        style="min-width: 50px; max-width: 50px; width: 50px; border: 0.3px solid #282828;"
                                        title="{{ $dateInfo['fullDayName'] }}, {{ $dateInfo['monthName'] }} {{ $dateInfo['day'] }}">
                                        <div class="d-flex flex-column">
                                            <span class="sub-text text-dark fw-bold fs-12px">{{ $dateInfo['dayName'] }}</span>
                                            <span class="sub-text text-dark fw-bold">{{ $dateInfo['day'] }}</span>
                                            <div class="fs-10px text-dark">{{ $dateInfo['monthName'] }}</div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Table Body - One row per staff -->
                            @forelse($attendanceData as $staff)
                                <div class="nk-tb-item">
                                    <!-- Staff Name -->
                                    <div class="nk-tb-col sticky-name-col bg-white" style="min-width: 240px; border: 0.3px solid #282828;">
                                        <div class="d-flex flex-column">
                                            <span class="tb-lead fw-bold">{{ $staff['full_name'] }}</span>
                                            @php
                                                $presentCount = collect($staff['attendance'])->filter(fn($status) => $status === 'P')->count();
                                                $absentCount = collect($staff['attendance'])->filter(fn($status) => $status === 'A')->count();
                                                $unmarkedCount = collect($staff['attendance'])->filter(fn($status) => $status === '-')->count();
                                            @endphp
                                            <div class="mt-1">
                                                <span class="me-2 mr-1 text-teal fw-bold">P: {{ $presentCount }}</span>
                                                <span class="me-2 mr-1 text-danger fw-bold">A: {{ $absentCount }}</span>
                                                <span class="text-gray fw-bold">U: {{ $unmarkedCount }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Attendance Cells - One for each date -->
                                    @php
                                        $currentMonth = null;
                                        $currentMonthClass = '';
                                    @endphp

                                    @foreach ($dates as $dateInfo)
                                        @php
                                            // Check if month has changed to apply different styling
                                            if ($currentMonth !== $dateInfo['month']) {
                                                $currentMonth = $dateInfo['month'];
                                                $currentMonthClass = $this->getMonthColorClass($currentMonth);
                                            }

                                            $date = $dateInfo['date'];
                                            $status = $staff['attendance'][$date];

                                            // Determine status class
                                            $statusClass = match ($status) {
                                                'P' => 'text-teal',
                                                'A' => 'text-danger',
                                                default => 'text-muted',
                                            };
                                            
                                            // Add weekend styling
                                            $weekendClass = $dateInfo['isWeekend'] ? 'weekend-cell' : '';
                                        @endphp

                                        <div class="nk-tb-col text-center {{ $currentMonthClass }} {{ $weekendClass }}"
                                            style="min-width: 50px; max-width: 50px; width: 50px; border: 0.3px solid #282828;"
                                            title="{{ $dateInfo['fullDayName'] }}, {{ $dateInfo['monthName'] }} {{ $dateInfo['day'] }} - {{ $status === 'P' ? 'Present' : ($status === 'A' ? 'Absent' : 'Unmarked') }}">
                                            <div class="dropdown">
                                                <p class="{{ $statusClass }} fw-bold fs-8 mb-0">{{ $status }}</p>
                                                @if($dateInfo['isWeekend'])
                                                    <small class="text-muted fs-10px">{{ $dateInfo['dayName'] }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @empty
                                <div class="p-5">
                                    <div class="alert alert-secondary alert-icon">
                                        <em class="icon ni ni-alert-circle"></em> No teaching staff found...
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nk-block mt-3">
        <div class="card card-bordered">
            <div class="card-inner">
                <div class="card-title-group">
                    <div class="card-title">
                        <h6 class="title">Legend & Information</h6>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-base">Attendance Status</h6>
                            <div class="d-flex g-3 mb-3">
                                <div class="me-4">
                                    <span class="badge bg-success">P</span> - Present
                                </div>
                                <div class="me-4">
                                    <span class="badge bg-danger">A</span> - Absent
                                </div>
                                <div>
                                    <span class="badge bg-gray">-</span> - Unmarked
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-base">Day Information</h6>
                            <div class="d-flex g-3">
                                <div class="me-4">
                                    <span class="badge bg-light text-dark border">Mon-Fri</span> - Weekdays
                                </div>
                                <div>
                                    <span class="badge bg-warning">Sat-Sun</span> - Weekends (Highlighted)
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <em class="icon ni ni-info"></em>
                            Hover over date columns to see full day names and attendance status details.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notification for Filter Updates -->
    <div class="position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
        <div class="toast align-items-center border-0 bg-success text-white" 
             role="alert" aria-live="assertive" aria-atomic="true"
             x-show="showMessage" 
             x-transition.opacity
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-x-full"
             x-transition:enter-end="opacity-100 transform translate-x-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-x-0"
             x-transition:leave-end="opacity-0 transform translate-x-full"
             style="display: none;">
            <div class="d-flex">
                <div class="toast-body">
                    <em class="icon ni ni-check-circle me-2"></em>
                    <span x-text="filterMessage"></span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" x-on:click="showMessage = false"></button>
            </div>
        </div>
    </div>

    <script>
        function attendanceFilters() {
            return {
                filterMessage: '',
                showMessage: false,
                staffSearchTerm: '',

                init() {
                    // Listen for Livewire updates
                    this.$wire.on('attendance-data-updated', () => {
                        this.showFilterMessage('Attendance data updated successfully!');
                    });
                },

                showFilterMessage(message = 'Applying filters...') {
                    this.filterMessage = message;
                    this.showMessage = true;
                    
                    // Auto-hide after 3 seconds
                    setTimeout(() => {
                        this.showMessage = false;
                    }, 3000);
                },

                getCurrentFilterDescription() {
                    if (this.$wire.filterMode === 'last_days') {
                        return `Last ${this.$wire.daysToShow} days`;
                    } else {
                        const months = {
                            1: 'January', 2: 'February', 3: 'March', 4: 'April',
                            5: 'May', 6: 'June', 7: 'July', 8: 'August',
                            9: 'September', 10: 'October', 11: 'November', 12: 'December'
                        };
                        return `${months[this.$wire.selectedMonth]} ${this.$wire.selectedYear}`;
                    }
                },

                toggleStaffSelection(staffId) {
                    this.$wire.toggleStaff(staffId);
                },

                isStaffSelected(staffId) {
                    return this.$wire.selectedStaff.includes(staffId);
                },

                getSelectedStaffCount() {
                    return this.$wire.selectedStaff.length;
                },

                filterStaff(staff) {
                    if (!this.staffSearchTerm) return true;
                    return staff.name.toLowerCase().includes(this.staffSearchTerm.toLowerCase()) ||
                           (staff.email && staff.email.toLowerCase().includes(this.staffSearchTerm.toLowerCase()));
                }
            }
        }

        // Livewire event listeners
        document.addEventListener('livewire:init', () => {
            Livewire.on('filter-applied', (event) => {
                // console.log('Filter applied:', event);
            });
        });
    </script>
</div>