// Teaching Staff Attendance Component Styles

.attendance-table-wrapper {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: auto;
    scroll-behavior: smooth;
}

.attendance-table {
    min-width: 100%;
    width: max-content;
}

.sticky-name-col {
    position: sticky;
    left: 0;
    z-index: 5;
    border-right: 1px solid #e5e9f2;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
}

/* Weekend styling */
.weekend-header {
    background-color: #fff3cd !important;
    border-left: 2px solid #ffc107 !important;
}

.weekend-cell {
    background-color: #fff3cd !important;
    opacity: 0.8;
}

/* Day name styling */
.fs-12px {
    font-size: 0.75rem !important;
}

.fs-10px {
    font-size: 0.625rem !important;
}

/* Hover effects for better UX */
.nk-tb-col:not(.sticky-name-col):hover {
    background-color: rgba(106, 122, 142, 0.1) !important;
    transition: background-color 0.2s ease;
}

/* Weekend day name styling */
.weekend-cell .text-muted {
    color: #856404 !important;
    font-weight: 500;
}

/* Filter animations */
[x-cloak] {
    display: none !important;
}

.filter-transition {
    transition: all 0.3s ease-in-out;
}

/* Loading state styling */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Staff selection styling */
.form-check-label {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: rgba(106, 122, 142, 0.1);
    }
}

.form-check-input:checked + .form-check-label {
    background-color: rgba(25, 135, 84, 0.1);
    border-color: #198754;
}

.staff-selection-grid {
    max-height: 300px;
    overflow-y: auto;
}

.gap-2 {
    gap: 0.5rem !important;
}
