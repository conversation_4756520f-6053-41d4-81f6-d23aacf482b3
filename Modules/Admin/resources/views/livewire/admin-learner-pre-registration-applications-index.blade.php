<div class="w-100">
    <div class="nk-block-head nk-block-head-sm">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">{{ $type }} Learner Pre-registration Applications</h3>
                <div class="nk-block-des text-soft">
                    <p>You have a total of {{ $this->applications->total() }} applications</p>
                </div>
            </div>
        </div>
    </div>
    <div class="nk-block">
        <div class="card card-stretch card-bordered border-dark-teal">
            <div class="card-inner-group">
                <div class="row card-inner">
                    <div class="col-lg-12">
                        <form wire:submit.prevent="applyFilter">
                            <div class="row">
                                <div class="col-lg-2">
                                    <!-- Local Government Filter -->
                                    <div class="form-control-wrap">
                                        <select wire:model="filter.local_government_id" class="form-control">
                                            <option value="">All</option>
                                            @foreach ($localGovernments as $gov)
                                                <option value="{{ $gov->id }}">{{ $gov->name }}</option>
                                            @endforeach
                                        </select>
                                        <label class="form-label-outlined emis-label">LOCAL GOVERNMENT</label>
                                    </div>
                                </div>
                                <div class="col-lg-2">
                                    <div class="form-control-wrap">
                                        <select wire:model="filter.school_type_id" class="form-control">
                                            <option value="">All</option>
                                            @foreach ($schoolTypes as $school)
                                                <option value="{{ $school->id }}">
                                                    {{ $school->display_name ?? $school->name }}</option>
                                            @endforeach
                                        </select>
                                        <label class="form-label-outlined emis-label">EDUCATION LEVEL</label>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-wrap">
                                        <div class="input-group">
                                            <input wire:model.lazy="filter.application_number" type="text"
                                                class="form-control text-uppercase border-dark-teal"
                                                placeholder="SEARCH APPLICATION NUMBER">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-5">
                                    <div class="form-wrap">
                                        <div class="input-group">
                                            <input wire:model.lazy="filter.search_term" type="text"
                                                class="form-control text-uppercase border-dark-teal"
                                                placeholder="SEARCH LEARNER NAME">
                                            <div class="input-group-append">
                                                @if (
                                                    $filter['search_term'] ||
                                                        $filter['application_number'] ||
                                                        $filter['school_type_id'] ||
                                                        $filter['local_government_id']
                                                )
                                                    <button wire:click.prevent="resetFilter"
                                                        class="btn rounded-0 bg-secondary px-2 text-white"
                                                        type="button">
                                                        <em class="icon ni ni-cross"></em>
                                                    </button>
                                                @endif
                                                <button class="btn rounded-right bg-dark-teal" type="submit">
                                                    <em class="icon ni ni-filter mr-1"></em>Apply
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-inner p-0 position-relative">
                    <div class="nk-tb-list nk-tb-ulist is-compact">
                        <div class="nk-tb-item nk-tb-head bg-secondary">
                            <div class="nk-tb-col nk-tb-col-check">
                                <div class="custom-control custom-control-sm custom-checkbox notext">
                                    <input wire:model="select_all_applications" wire:change="toggleAllApplications"
                                        type="checkbox" class="custom-control-input" id="uid">
                                    <label class="custom-control-label" for="uid"></label>
                                </div>
                            </div>
                            <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">APPLICATION
                                    NUMBER</span></div>
                            <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">LEARNER</span></div>
                            <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">SEX</span></div>
                            <div class="nk-tb-col"><span class="sub-text text-white">CLASS</span></div>
                            <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">EDUCATION
                                    LEVEL</span></div>
                            <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">DATE
                                    SUBMITTED</span></div>
                            <div class="nk-tb-col text-uppercase text-center"><span class="sub-text text-white">APPROVAL
                                    STATUS</span></div>
                            <div class="nk-tb-col text-uppercase text-center"><span
                                    class="sub-text text-white">ACTIONS</span></div>
                        </div>
                        @foreach ($this->applications as $application)
                            <div class="nk-tb-item">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input wire:model="selected_applications" wire:change="toggleOneApplication"
                                            value="{{ $application->id }}" type="checkbox" class="custom-control-input"
                                            id="uid{{ $application->id }}">
                                        <label class="custom-control-label" for="uid{{ $application->id }}"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <a class="text-dark-teal"
                                        href="/admin/learner-enrolment-applications/{{ strtolower($type) }}/{{ strtolower($application->application_number) }}">
                                        <span class="d-block">{{ $application->application_number }}</span>
                                    </a>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span class="d-block text-uppercase">{{ $application->full_name }}</span>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span class="d-block text-uppercase">{{ $application->gender }}</span>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span
                                        class="d-block text-uppercase">{{ optional($application->education_grade)->name }}</span>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span
                                        class="d-block text-uppercase">{{ optional($application->school->school_type)->name }}</span>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span
                                        class="d-block">{{ $application->date_created ? $application->date_created->format('d M, Y h:ia') : $application->date_updated->format('d M, Y h:ia') }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span class="text-uppercase badge badge-primary"
                                        @if ($application->approval_status === 'pending') style="display:inline" @else style="display:none" @endif>Pending</span>
                                    <span class="text-uppercase badge badge-dark-teal"
                                        @if ($application->approval_status === 'approved') style="display:inline" @else style="display:none" @endif>Approved</span>
                                    <span class="text-uppercase badge badge-red"
                                        @if ($application->approval_status === 'rejected') style="display:inline" @else style="display:none" @endif>Rejected</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <a data-toggle="tooltip" data-placement="top" title="View Application"
                                        href="/admin/learner-pre-registration-applications/{{ strtolower($type) }}/{{ strtoupper($application->application_number) }}">
                                        <em class="icon ni ni-eye-fill"></em>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    @if ($this->applications->isEmpty())
                        <div class="p-5">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> No applications to display at the moment...
                            </div>
                        </div>
                    @endif
                    <div class="card-inner d-flex flex-row justify-content-between">
                        <nav>
                            <ul class="pagination">
                                <li
                                    class="page-item {{ $this->applications->currentPage() === 1 ? 'disabled' : '' }}">
                                    <a wire:click="setPage(1)"
                                        class="page-link {{ $this->applications->currentPage() === 1 ? '' : 'cursor' }}">
                                        <em class="icon ni ni-first"></em>
                                    </a>
                                </li>
                                <li
                                    class="page-item {{ $this->applications->currentPage() === 1 ? 'disabled' : '' }}">
                                    <a wire:click="setPage({{ $this->applications->currentPage() - 1 }})"
                                        class="page-link {{ $this->applications->currentPage() === 1 ? '' : 'cursor' }}">
                                        <em class="icon ni ni-chevron-left"></em>
                                    </a>
                                </li>
                                @for ($page = 1; $page <= $this->applications->lastPage(); $page++)
                                    <li
                                        class="page-item {{ $this->applications->currentPage() === $page ? 'active-dark-teal active' : '' }}">
                                        <a wire:click="setPage({{ $page }})"
                                            class="page-link cursor">{{ $page }}</a>
                                    </li>
                                @endfor
                                <li
                                    class="page-item {{ $this->applications->currentPage() === $this->applications->lastPage() ? 'disabled' : '' }}">
                                    <a wire:click="setPage({{ $this->applications->currentPage() + 1 }})"
                                        class="page-link {{ $this->applications->currentPage() === $this->applications->lastPage() ? '' : 'cursor' }}">
                                        <em class="icon ni ni-chevron-right"></em>
                                    </a>
                                </li>
                                <li
                                    class="page-item {{ $this->applications->currentPage() === $this->applications->lastPage() ? 'disabled' : '' }}">
                                    <a wire:click="setPage({{ $this->applications->lastPage() }})"
                                        class="page-link {{ $this->applications->currentPage() === $this->applications->lastPage() ? '' : 'cursor' }}">
                                        <em class="icon ni ni-last"></em>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <div class="d-flex flex-row ml-4">
                            <span class="align-self-center me-1">Show</span>
                            <div class="form-wrap align-self-center">
                                <select id="filterPerPage" class="form-select-sm">
                                    <option value="15">15</option>
                                    <option value="25">25</option>
                                    <option value="35">35</option>
                                    <option value="50">50</option>
                                    <option value="70">70</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>

                        <div class="d-flex ml-4">
                            <span class="align-self-center">
                                Showing
                                <span class="text-primary">{{ $this->applications->firstItem() }}</span> to
                                <span class="text-primary">{{ $this->applications->lastItem() }}</span> of
                                <span class="text-primary">{{ $this->applications->total() }}</span>
                                 {{$type}}Applications
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
