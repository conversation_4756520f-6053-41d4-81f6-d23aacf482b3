{{-- This partial renders duplicate learner details in the same format as application-learner-details --}}
@php
    $metadata = $duplicate['metadata'] ?? [];
    $person = $duplicate['person'] ?? [];
@endphp

<div class="row">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">School Name:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head">{{ $metadata['school_name'] ?? 'N/A' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Name:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head text-uppercase">{{ $metadata['learner_name'] ?? ($person['full_name'] ?? 'N/A') }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Sex:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head">{{ ($metadata['learner_gender'] ?? $person['gender'] ?? '') === 'M' ? 'MALE' : (($metadata['learner_gender'] ?? $person['gender'] ?? '') === 'F' ? 'FEMALE' : 'N/A') }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Date Of Birth:</span></h6>
    </div>
    <div class="col-lg-8">
        @php
            $birthDate = $metadata['learner_birth_date'] ?? $person['birth_date'] ?? null;
        @endphp
        <h6 class="style-head">{{ $birthDate ? \Carbon\Carbon::parse($birthDate)->format('d M, Y') : 'N/A' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Country:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head text-uppercase">{{ $person['country']['name'] ?? 'N/A' }}</h6>
    </div>
</div>
@if(!empty($metadata['learner_id_number']) || !empty($person['id_number']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">NIN:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $metadata['learner_id_number'] ?? $person['id_number'] ?? '' }}</h6>
        </div>
    </div>
@endif
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Class:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head text-uppercase">{{ $metadata['education_grade'] ?? 'N/A' }}</h6>
    </div>
</div>
@if(!empty($metadata['district_of_birth']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">District Of Birth:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $metadata['district_of_birth'] }}</h6>
        </div>
    </div>
@endif
@if(!empty($metadata['familiar_language']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Familiar Language:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $metadata['familiar_language'] }}</h6>
        </div>
    </div>
@endif

{{-- Additional metadata fields that might be available --}}
@if(!empty($metadata['exam_year']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Exam Year:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $metadata['exam_year'] }}</h6>
        </div>
    </div>
@endif
@if(!empty($metadata['index_number']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Index Number:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $metadata['index_number'] }}</h6>
        </div>
    </div>
@endif

