{{-- This partial renders duplicate parent details in the same format as application-parent-details --}}
@php
    $metadata = $duplicate['metadata'] ?? [];
@endphp

<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Names:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head text-uppercase">{{ $metadata['parent_name'] ?? 'N/A' }}</h6>
    </div>
</div>
@if(!empty($metadata['parent_id_number']) || !empty($metadata['parent_passport']) || !empty($metadata['parent_refugee_number']))
    <div class="row mt-1">
        <div class="col-lg-4">
            @if(!empty($metadata['parent_id_number']))
                <h6 class="style-head"><span class="text-gray">NIN:</span></h6>
            @endif
            @if(!empty($metadata['parent_passport']))
                <h6 class="style-head"><span class="text-gray">Passport No:</span></h6>
            @endif
            @if(!empty($metadata['parent_refugee_number']))
                <h6 class="style-head"><span class="text-gray">Refugee No:</span></h6>
            @endif
        </div>
        <div class="col-lg-8">
            @if(!empty($metadata['parent_id_number']))
                <h6 class="style-head text-uppercase">{{ $metadata['parent_id_number'] }}</h6>
            @endif
            @if(!empty($metadata['parent_passport']))
                <h6 class="style-head text-uppercase">{{ $metadata['parent_passport'] }}</h6>
            @endif
            @if(!empty($metadata['parent_refugee_number']))
                <h6 class="style-head text-uppercase">{{ $metadata['parent_refugee_number'] }}</h6>
            @endif
        </div>
    </div>
@endif
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Sex:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head">{{ ($metadata['parent_gender'] ?? '') === 'M' ? 'MALE' : (($metadata['parent_gender'] ?? '') === 'F' ? 'FEMALE' : 'N/A') }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Relationship:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="text-uppercase">{{ $metadata['parent_relationship'] ?? 'N/A' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Country:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6>{{ $metadata['parent_country'] ?? 'N/A' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Email:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6>{{ $metadata['parent_email'] ?? 'N/A' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Phone No.1:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6>{{ $metadata['parent_phone_1'] ?? 'N/A' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Phone No.2:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6>{{ $metadata['parent_phone_2'] ?? 'N/A' }}</h6>
    </div>
</div>
@if(!empty($metadata['parent_birth_date']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Date Of Birth:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head">{{ \Carbon\Carbon::parse($metadata['parent_birth_date'])->format('d M, Y') }}</h6>
        </div>
    </div>
@endif
@if(!empty($metadata['parent_photo']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Photo:</span></h6>
        </div>
        <div class="col-lg-8">
            <img class="py-3 rounded-0" src="{{ $metadata['parent_photo'] }}" alt="{{ $metadata['parent_name'] }}" style="max-width: 120px;">
        </div>
    </div>
@endif

