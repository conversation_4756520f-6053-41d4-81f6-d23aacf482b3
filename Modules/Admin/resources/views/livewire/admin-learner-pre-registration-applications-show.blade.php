@php use Carbon\Carbon; @endphp
<div class="w-100">
    <livewire:emis-returns.notification-banner />
    <div class="nk-block">
        <div class="card card-stretch">
            <div class="card-inner-group">
                <div class="card-body card-bordered border-dark-teal">
                    <div class="card-inner card-inner-xl" style="padding: 3%">
                        <h4>Learner Pre-registration Application Details</h4>
                        <p>Below are the application details</p>
                        <h6 class="style-head">
                            <span class="text-gray mr-2">Application ID:</span> #{{ $application['application_number'] }}
                        </h6>
                        <h6 class="style-head">
                            <span class="text-gray mr-2">Date Submitted:</span>
                            {{ $application['date_created'] ? Carbon::parse($application['date_created'])->format('d M, Y h:ia') : (isset($application['date_updated']) ? Carbon::parse($application['date_updated'])->format('d M, Y h:ia') : '') }}
                        </h6>

                        <h6 class="style-head">
                            <span class="text-gray mr-2">Approval Status:</span>
                            @if ($application['approval_status'] === 'pending')
                                <span class="text-uppercase badge badge-primary">Pending</span>
                            @elseif($application['approval_status'] === 'approved')
                                <span class="text-uppercase badge badge-dark-teal">Approved</span>
                            @elseif($application['approval_status'] === 'rejected')
                                <span class="text-uppercase badge badge-red">Rejected</span>
                            @endif
                        </h6>
                        @if ($application['date_approved'])
                            <h6 class="style-head">
                                <span class="text-gray mr-2">Approved By:</span>
                                {{ $application['deo']['full_name'] ?? ($application['moes_staff']['full_name'] ?? '') }}
                                |
                                @php
                                    $lg = $application['local_government'] ?? null;
                                    $deoTitle = null;
                                    if ($lg) {
                                        $name = strtoupper($lg['name'] ?? '');
                                        $type = $lg['local_gov_type'] ?? null;
                                        if (str_ends_with($name, ' CITY') || $type === 2) {
                                            $deoTitle = 'CEO';
                                        } elseif (str_ends_with($name, ' MUNICIPALITY') || $type === 3) {
                                            $deoTitle = 'MEO';
                                        } else {
                                            $deoTitle = 'DEO';
                                        }
                                    }
                                @endphp
                                {{ $deoTitle ?? 'MoES' }}
                            </h6>
                            <h6 class="style-head">
                                <span class="text-gray mr-2">Date
                                    Approved: </span>{{ $application['date_approved_formatted'] }}
                            </h6>
                        @endif
                        <div class="nk-divider divider md"></div>
                        <div class="preview-block">
                            <div class="row gy-4">
                                <div class="col-sm-6">
                                    <span class="preview-title-lg overline-title">Learner Details</span>
                                    @include('admin::livewire.partials.application-learner-details', [
                                        'application' => $application,
                                    ])
                                </div>
                                <div class="col-sm-6">
                                    <span class="preview-title-lg overline-title">Parent Details</span>
                                    @include('admin::livewire.partials.application-parent-details', [
                                        'application' => $application,
                                    ])
                                </div>
                            </div>
                        </div>

                        {{-- Potential Duplicates Section --}}
                        @if ($hasPendingDuplicates)
                            <div class="nk-divider divider md"></div>
                            <div class="preview-block">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="preview-title-lg overline-title text-warning">
                                        <em class="icon ni ni-alert-circle mr-2"></em>Potential Duplicate Found
                                    </span>
                                    @if ($this->getTotalPendingCount() > 1)
                                        <div class="badge badge-outline-warning">
                                            {{ $this->getCurrentPosition() }} of {{ $this->getTotalPendingCount() }}
                                        </div>
                                    @endif
                                </div>
                                
                                @php $currentDuplicate = $this->getCurrentDuplicate(); @endphp
                                
                                @if ($currentDuplicate)
                                    <div class="card card-bordered border-warning mt-3">
                                        <div class="card-inner">
                                            @if ($this->getTotalPendingCount() > 1)
                                                <div class="d-flex justify-content-end mb-3">
                                                    <div class="btn-group btn-group-sm">
                                                        <button wire:click="previousDuplicate" 
                                                                class="btn btn-outline-secondary" 
                                                                @if(!$this->hasPreviousDuplicate()) disabled @endif>
                                                            <em class="icon ni ni-chevron-left"></em>
                                                        </button>
                                                        <button wire:click="nextDuplicate" 
                                                                class="btn btn-outline-secondary"
                                                                @if(!$this->hasNextDuplicate()) disabled @endif>
                                                            <em class="icon ni ni-chevron-right"></em>
                                                        </button>
                                                    </div>
                                                </div>
                                            @endif
                                            
                                            <div class="row gy-3">
                                                <div class="col-md-6">
                                                    <div class="preview-block">
                                                        <span class="preview-title-lg overline-title text-danger">Existing Learner Details</span>
                                                        @include('admin::livewire.partials.duplicate-learner-details', [
                                                            'duplicate' => $currentDuplicate->toArray(),
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="preview-block">
                                                        <span class="preview-title-lg overline-title text-danger">Existing Parent Details</span>
                                                        @include('admin::livewire.partials.duplicate-parent-details', [
                                                            'duplicate' => $currentDuplicate->toArray(),
                                                        ])
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mt-4">
                                                <p class="text-soft">
                                                    <strong>Action Required:</strong> Please review the information above and confirm whether this is the same person or a different person.
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <button wire:click="resolveDuplicateAsNotDuplicate({{ $currentDuplicate->id }})" 
                                                                class="btn btn-success mr-3">
                                                            <em class="icon ni ni-check-circle mr-1"></em>Different Person
                                                        </button>
                                                        <button wire:click="resolveDuplicateAsDuplicate({{ $currentDuplicate->id }})" 
                                                                class="btn btn-danger">
                                                            <em class="icon ni ni-cross-circle mr-1"></em>Same Person (Duplicate)
                                                        </button>
                                                    </div>
                                                    
                                                    @if ($this->getTotalPendingCount() > 1)
                                                        <div class="btn-group">
                                                            @if ($this->hasNextDuplicate())
                                                                <button wire:click="nextDuplicate" 
                                                                        class="btn btn-outline-primary">
                                                                    <em class="icon ni ni-forward mr-1"></em>Next Duplicate
                                                                </button>
                                                            @else
                                                                <button wire:click="skipDuplicate({{ $currentDuplicate->id }})" 
                                                                        class="btn btn-outline-secondary">
                                                                    <em class="icon ni ni-forward mr-1"></em>Skip for Now
                                                                </button>
                                                            @endif
                                                        </div>
                                                    @else
                                                        <button wire:click="skipDuplicate({{ $currentDuplicate->id }})" 
                                                                class="btn btn-outline-secondary">
                                                            <em class="icon ni ni-forward mr-1"></em>Skip for Now
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @elseif (!$hasPendingDuplicates && $this->hasResolvedDuplicates())
                            <div class="nk-divider divider md"></div>
                            <div class="preview-block">
                                <div class="alert alert-success mt-3">
                                    <div class="alert-cta">
                                        <h4>All Duplicates Resolved!</h4>
                                        <p>All potential duplicates have been reviewed and resolved. You can now proceed with the application approval.</p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        {{-- Action Buttons Section --}}
                        @if (auth()->user()->can('enrolments-applications-approve') && $application['approval_status'] === 'pending')
                            <div class="form-group mt-5">
                                <p class="lead">
                                    By Clicking Approve Or Reject, I hereby confirm that I have verified the learner
                                    details and I take full responsibility of this action.
                                </p>
                                @if (!$this->canApproveApplication())
                                    <div class="alert alert-warning">
                                        <div class="alert-cta">
                                            <h6><em class="icon ni ni-alert-circle mr-2"></em>Action Required</h6>
                                            @if ($hasPendingDuplicates)
                                                <p>You must resolve all potential duplicates before approving this application.</p>
                                            @endif
                                            @if ($this->getDeduplicationStatusMessage())
                                                <p>{{ $this->getDeduplicationStatusMessage() }}</p>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <button wire:click="approveApplication" wire:loading.attr="disabled"
                                class="btn bg-dark-teal btn-lg mr-2" {{ !$this->canApproveApplication() ? 'disabled' : '' }}>
                                <em class="icon ni ni-check-circle-cut mr-1"></em>APPROVE
                                @if ($hasPendingDuplicates)
                                    <span class="ml-2 badge badge-warning">{{ $this->getPendingDuplicatesCount() }} duplicates pending</span>
                                @elseif (!($application['is_deduplicated'] ?? false))
                                    <span class="ml-2 badge badge-secondary">Deduplication pending</span>
                                @endif
                            </button>
                        @endif

                        {{-- Reject Buttons --}}
                        @if (auth()->user()->can('enrolments-applications-reject') && $application['approval_status'] === 'pending')
                            <button type="button" class="btn btn-danger btn-lg" data-toggle="modal"
                                data-target="#rejectModal">
                                <em class="icon ni ni-cross-circle mr-1"></em>REJECT
                            </button>
                        @endif

                        {{-- Display Reject Reason --}}
                        @if ($application['date_approved'] && $application['reject_reason'])
                            <div class="nk-divider divider md"></div>
                            <div class="nk-block">
                                <div class="nk-block-head nk-block-head-sm nk-block-between">
                                    <h5 class="title">Reject Reason</h5>
                                </div>
                                <div class="bq-note">
                                    <div class="bq-note-item">
                                        <div class="bq-note-text">
                                            <p>{{ $application['reject_reason'] }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 4 Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reject Learner Pre-registration Application</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form wire:submit.prevent="commitRejectReason">
                    <div class="modal-body">
                        <p class="text-danger font-weight-bold">
                            Are you sure you want to reject this application? If Yes, enter reason below and click
                            submit. If No, press cancel.
                        </p>
                        <div class="form-group">
                            <label class="form-label">Reject Reason <span class="text-danger">*</span></label>
                            <div class="form-control-wrap">
                                <textarea wire:model.defer="reason" placeholder="Text should not be more than 5000 characters" maxlength="5000"
                                    class="form-control no-resize" required></textarea>
                                @error('reason')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" class="btn btn-light" data-dismiss="modal">CANCEL</button>
                        <button type="submit" wire:loading.attr="disabled"
                            class="btn btn-dark-teal d-flex align-items-center">
                            <span wire:loading wire:target="commitRejectReason"
                                class="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span>
                            <span wire:loading wire:target="commitRejectReason">Rejecting Application...</span>
                            <span wire:loading.remove wire:target="commitRejectReason">Submit</span>
                            <em wire:loading.remove wire:target="commitRejectReason"
                                class="ni ni-arrow-right ml-2"></em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script>
    window.addEventListener('close-reject-modal', () => {
        $('#rejectModal').modal('hide');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });
</script>
