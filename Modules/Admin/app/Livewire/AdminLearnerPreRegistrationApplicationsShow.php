<?php

namespace Modules\Admin\Livewire;

use Exception;
use Livewire\Component;
use Modules\EmisReturns\Livewire\Traits\Learners\StoreLearnerDataTrait;
use Modules\Core\Traits\InteractsWithPerson;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;
use Modules\LearnerManagement\Models\PotentialDuplicateLearner;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AdminLearnerPreRegistrationApplicationsShow extends Component
{
    use StoreLearnerDataTrait, InteractsWithPerson;
    public $application;
    public $reason = '';
    public $loading = false;
    public $hasPendingDuplicates = false;
    public $currentDuplicateIndex = 0;
    protected $listeners = [
        'learner-rejected' => 'commitRejectReason',
        'duplicatesStatusChanged' => 'updateDuplicatesStatus'
    ];

    public function mount($status, $applicationNumber)
    {
        try {
            $application = LearnerEnrolmentApplication::query()
                ->where('application_number', Str::upper($applicationNumber))
                ->with([
                    'user_account',
                    'school.school_type',
                    'education_grade',
                    'country',
                    'parent_country',
                    'district_of_birth',
                    'post_primary_institution_course',
                    'institution_examined_course',
                    'international_calendar',
                    'international_curriculum',
                    'international_education_grade',
                    'international_education_level',
                    'familiar_language',
                    'deo.person',
                    'moes_staff',
                ])
                ->firstOrFail();
            $this->application = $application->toArray();
            $this->checkDuplicatesStatus();
        } catch (ModelNotFoundException $exception) {
            abort(500, 'We cannot find the application you are trying to view');
        }
    }

    public function approveApplication()
    {
        $this->loading = true;
        try {
            $application = LearnerEnrolmentApplication::where('application_number', Str::upper($this->application['application_number']))->firstOrFail();
            
            if (!$application->is_deduplicated) {
                $this->dispatch('notify', [
                    'status' => 'error',
                    'title' => 'Error:',
                    'message' => 'Cannot approve application. Deduplication checks are still pending.',
                ]);
                $this->loading = false;
                return;
            }

            DB::transaction(function () use ($application) {
                $this->saveLearnerEnrolment($application);
                $application->update([
                    'approved_by' => auth()->id(),
                    'date_approved' => now(),
                    'approval_status' => 'approved',
                ]);

                $this->application = $application->refresh();
            });

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Application approved successfully.',
            ]);
        } catch (ModelNotFoundException $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Application not found.',
            ]);
        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage(),
            ]);
        }
        $this->loading = false;
    }

    public function commitRejectReason()
    {
        $this->validate([
            'reason' => 'required|string|max:5000',
        ]);

        $this->loading = true;

        try {
            $application = LearnerEnrolmentApplication::where('application_number', Str::upper($this->application['application_number']))->firstOrFail();

            DB::transaction(function () use ($application) {
                $application->update([
                    'approved_by' => auth()->id(),
                    'date_approved' => now(),
                    'reject_reason' => $this->reason,
                    'approval_status' => 'rejected',
                ]);

                $this->application = $application->refresh();
            });

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Application rejected successfully.',
            ]);
             $this->dispatch('close-reject-modal');
        } catch (ModelNotFoundException $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Application not found.',
            ]);
        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage(),
            ]);
        }

        $this->loading = false;
    }

    public function checkDuplicatesStatus()
    {
        $duplicates = PotentialDuplicateLearner::where('pre_registration_id', $this->application['id'])
            ->whereIn('verification_status', ['pending', 'skipped'])
            ->count();

        $this->hasPendingDuplicates = $duplicates > 0;
        
        // Reset pagination if current index is out of bounds
        if ($this->currentDuplicateIndex >= $duplicates) {
            $this->resetPagination();
        }
    }

    public function updateDuplicatesStatus($hasPendingDuplicates)
    {
        $this->hasPendingDuplicates = $hasPendingDuplicates;
    }

    public function getPendingDuplicatesCount()
    {
        return PotentialDuplicateLearner::where('pre_registration_id', $this->application['id'])
            ->whereIn('verification_status', ['pending', 'skipped'])
            ->count();
    }

    public function resolveDuplicateAsNotDuplicate($duplicateId)
    {
        try {
            PotentialDuplicateLearner::where('id', $duplicateId)
                ->update([
                    'verification_status' => 'verified_not_duplicate',
                    'verified_by' => auth()->id(),
                    'verified_at' => now(),
                ]);

            $this->checkDuplicatesStatus();

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Duplicate resolved as not duplicate.',
            ]);
        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function resolveDuplicateAsDuplicate($duplicateId)
    {
        try {
            PotentialDuplicateLearner::where('id', $duplicateId)
                ->update([
                    'verification_status' => 'verified_duplicate',
                    'verified_by' => auth()->id(),
                    'verified_at' => now(),
                ]);

            $this->checkDuplicatesStatus();

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Duplicate resolved as duplicate.',
            ]);
        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function skipDuplicate($duplicateId)
    {
        try {
            PotentialDuplicateLearner::where('id', $duplicateId)
                ->update([
                    'verification_status' => 'skipped',
                    'verified_by' => auth()->id(),
                    'verified_at' => now(),
                ]);

            $this->checkDuplicatesStatus();
        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function getPendingDuplicates()
    {
        return PotentialDuplicateLearner::where('pre_registration_id', $this->application['id'])
            ->with(['person', 'verifiedBy'])
            ->whereIn('verification_status', ['pending', 'skipped'])
            ->orderBy('confidence_score', 'desc')
            ->get();
    }

    public function getCurrentDuplicate()
    {
        $pendingDuplicates = $this->getPendingDuplicates();
        
        if ($pendingDuplicates->isEmpty()) {
            return null;
        }

        // Ensure index is within bounds
        if ($this->currentDuplicateIndex >= $pendingDuplicates->count()) {
            $this->currentDuplicateIndex = 0;
        }

        return $pendingDuplicates->get($this->currentDuplicateIndex);
    }

    public function getTotalPendingCount()
    {
        return $this->getPendingDuplicates()->count();
    }

    public function getCurrentPosition()
    {
        return $this->currentDuplicateIndex + 1;
    }

    public function hasNextDuplicate()
    {
        return $this->currentDuplicateIndex < ($this->getTotalPendingCount() - 1);
    }

    public function hasPreviousDuplicate()
    {
        return $this->currentDuplicateIndex > 0;
    }

    public function nextDuplicate()
    {
        if ($this->hasNextDuplicate()) {
            $this->currentDuplicateIndex++;
        }
    }

    public function previousDuplicate()
    {
        if ($this->hasPreviousDuplicate()) {
            $this->currentDuplicateIndex--;
        }
    }

    public function resetPagination()
    {
        $this->currentDuplicateIndex = 0;
    }

    public function canApproveApplication()
    {
        return ($this->application['is_deduplicated'] ?? false) 
            && !$this->hasPendingDuplicates 
            && $this->application['approval_status'] === 'pending';
    }

    public function getDeduplicationStatusMessage()
    {
        if (!($this->application['is_deduplicated'] ?? false)) {
            return 'Deduplication checks are still pending. Please wait for the process to complete before approving.';
        }
        
        return '';
    }

    public function hasResolvedDuplicates()
    {
        return PotentialDuplicateLearner::where('pre_registration_id', $this->application['id'])
            ->whereIn('verification_status', ['verified_duplicate', 'verified_not_duplicate'])
            ->exists();
    }

    public function render()
    {
        return view('admin::livewire.admin-learner-pre-registration-applications-show');
    }
}
