<?php

namespace Modules\LearnerManagement\Livewire\LearnerPerformance;

use Livewire\Component;
use Modules\Core\Models\Institutions\LearnerEnrolment;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\Core\Models\Settings\SecondarySchoolSubject;
use Modules\Core\Models\Settings\SettingPrimarySchoolSubject;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\LearnerManagement\Models\PrimaryLearnerPerformance;
use Modules\LearnerManagement\Models\SettingPerformanceGrades;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\LearnerManagement\Models\SecondaryLearnerPerformance;



class LearnerPerformanceSection extends Component
{
    use InstitutionContext, WithPagination;

    public $institutionId;
    public $academicYearId;
    public $termId;
    public $institution;
    public $learners_data = [];
    public $primary_subjects = [];
    public $secondary_subjects = [];
    public $primary_performance_grades = [];
    public $secondary_performance_grades = [];
    public $alevel_secondary_performance_grades = [];
    public $selectedLearner;
    public $selected_learner;

    public $selectedLearnerName;
    public $selectedLearnerLIN;
    public $selectedLearnerGrade;
    public $performance_grade_id;
    public $primary_subject_id;
    public $subjectGrades = [];
    public $subject_performances = [];
    public bool $isLoading = false;
    public $primary_education_grades = [];
    public $secondary_education_grades = [];

    public $lin;
    public $learner_name;
    public $gender;
    public $education_grade_id;
    public $filtersApplied = false;

    public $currentPage = 1;
    public $perPage = 15;
    public $total = 0;
    public $from = 0;
    public $to = 0;
    public $lastPage = 1;
    protected $queryString = [
        'currentPage' => ['as' => 'page'],
    ];

    public  function mount()
    {
        $this->subjectGrades = $this->subjectGrades ?? [];
        $this->initializeContext();
        $this->baseLearnerQuery();
        $this->loadSubjects();
        $this->loadPerformanceGrades();
        $this->loadEducationGrades();
        $this->loadPaginatedLearners();
        $this->initializeSubjectGrades();
    }

    // Initialize the context with institution
    private function initializeContext(): void
    {
        $this->institution = $this->getInstitution();
        $this->institutionId = $this->institution->id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }

    // Initialize subject grades with a default empty entry
    public function initializeSubjectGrades()
    {
        $this->subjectGrades = [
            [
                'primary_subject_id' => null,
                'performance_grade_id' => null,
                'subject' => '',
                'grade' => '',
                'expanded' => true,
            ],
        ];
    }

    //query learners for the current institution and academic year
    private function baseLearnerQuery()
    {
        $this->isLoading = true;
        $schoolId = $this->institution->id;
        $this->academicYearId = $this->institution->academic_year_id ?? get_active_academic_year()->id;

        return LearnerEnrolment::with('learner.person', 'learner.person.country', 'learner.education_grade', 'primary_performances', 'secondary_performances')
            ->where('school_id', $schoolId)
            ->where('academic_year_id', $this->academicYearId);
        $this->isLoading = false;
    }



    //load subjects
    public function loadSubjects()
    {
        $this->primary_subjects = SettingPrimarySchoolSubject::select('id', 'name')->get();
        $this->secondary_subjects = SecondarySchoolSubject::select('id', 'name')->get();
    }


    //load performance grades
    public function loadPerformanceGrades()
    {
        $this->primary_performance_grades = SettingPerformanceGrades::where('is_for_a_level_yn', false)->get();
        $this->secondary_performance_grades = SettingPerformanceGrades::all();
        $this->alevel_secondary_performance_grades = SettingPerformanceGrades::where('is_for_a_level_yn', true)->get();
    }

    //load education grades
    public function loadEducationGrades()
    {
        $this->secondary_education_grades = SchoolEducationGrade::where('school_type_id', 3)->orderBy('name', 'asc')->get();
        $this->primary_education_grades = SchoolEducationGrade::where('school_type_id', 2)->orderBy('name', 'asc')->get();
    }

    //filter learners
    public function filterLearners($page = 1)
    {
        $this->isLoading = true;
        $this->currentPage = $page;
        $query = $this->baseLearnerQuery();

        if ($this->education_grade_id) {
            $query->whereHas(
                'learner',
                fn($q) =>
                $q->where('education_grade_id', $this->education_grade_id)
            );
        }

        if ($this->gender) {
            $query->whereHas(
                'learner.person',
                fn($q) =>
                $q->where('gender', $this->gender)
            );
        }

        if ($this->lin) {
            $query->whereHas(
                'learner',
                fn($q) =>
                $q->where('lin', 'iLike', '%' . $this->lin . '%')
            );
        }

        if ($this->learner_name) {
            $query->whereHas(
                'learner.person',
                fn($q) =>
                $q->where(DB::raw("concat_ws(' ', surname, first_name)"), 'iLike', '%' . $this->learner_name . '%')
            );
        }

        $this->total = $query->count();

        $this->learners_data = $query
            ->orderBy('date_created', 'desc')
            ->skip(($this->currentPage - 1) * $this->perPage)
            ->take($this->perPage)
            ->get();

        $this->filtersApplied = true;
        $this->isLoading = false;
    }

    //reset filters
    public function resetFilters()
    {
        $this->lin = '';
        $this->learner_name = '';
        $this->gender = '';
        $this->education_grade_id = null;
        $this->filtersApplied = false;
        $this->loadPaginatedLearners();
    }

    //load paginated learners
    public function loadPaginatedLearners($page = 1)
    {
        $this->isLoading = true;
        $this->currentPage = $page;

        if ($this->filtersApplied) {
            $this->filterLearners($page); // filtered paginated
        } else {
            $query = $this->baseLearnerQuery();

            $this->total = $query->count();

            $this->learners_data = $query
                ->orderBy('date_created', 'desc')
                ->skip(($this->currentPage - 1) * $this->perPage)
                ->take($this->perPage)
                ->get();
        }

        $this->isLoading = false;
    }

    // Check if the selected learner is in O-Level or A-Level
    public function isOLearner()
    {
        return in_array($this->selectedLearnerGrade, ['S1', 'S2', 'S3', 'S4']);
    }

    // Check if the selected learner is in A-Level
    public function isALearner()
    {
        return in_array($this->selectedLearnerGrade, ['S5', 'S6']);
    }

    // Select a learner, update and load their performance data
    public function selectLearner($id)
    {
        $learner = $this->learners_data->firstWhere('id', $id);

        if (!$learner) {
            $this->selected_learner = null;
            $this->selectedLearnerName = 'Unknown';
            $this->selectedLearnerLIN = 'N/A';
            $this->selectedLearnerGrade = 'N/A';
            return;
        }

        $this->selected_learner = $learner;
        $this->selectedLearnerName = $learner->learner->person->first_name . ' ' . $learner->learner->person->surname;
        $this->selectedLearnerLIN = $learner->learner->lin ?? 'N/A';
        $this->selectedLearnerGrade = $learner->education_grade->name ?? 'N/A';

        $primary_subjects_performances = PrimaryLearnerPerformance::where('learner_id', $this->selected_learner->id)->get();

        $secondary_subjects_performances = SecondaryLearnerPerformance::where('learner_id', $this->selected_learner->id)->get();

        if ($this->institution->school_type_id === 2) {
            $this->isLoading = true;
            $this->subjectGrades = $primary_subjects_performances->map(function ($perf) {
                return [
                    'primary_subject_id' => $perf->primary_subject_id,
                    'performance_grade_id' => $perf->performance_grade_id,
                    'subject' => optional($perf->primary_subject)->name ?? '',
                    'grade' => optional($perf->performance_grade)->name ?? '',
                    'expanded' => false,
                ];
            })->toArray();
            $this->isLoading = false;
        } elseif ($this->institution->school_type_id === 3) {
            $this->isLoading = true;
            $this->subjectGrades = $secondary_subjects_performances->map(function ($perf) {
                return [
                    'secondary_subject_id' => $perf->secondary_subject_id,
                    'performance_grade_id' => $perf->performance_grade_id,
                    'subject' => optional($perf->secondary_subject)->name ?? '',
                    'grade' => optional($perf->performance_grade)->name ?? '',
                    'expanded' => false,
                ];
            })->toArray();
            $this->isLoading = false;
        }
    }

    //delete subject grade
    public function deleteSubjectGrade($index)
    {
        try {
            $entry = $this->subjectGrades[$index] ?? null;

            if (!$entry) return;

            $this->isLoading = true;

            $commonWhere = [
                'learner_id' => $this->selected_learner->id,
                'school_id' => $this->institution->id,
                'education_grade_id' => $this->selected_learner->education_grade_id,
                'teaching_period_id' => $this->termId,
            ];

            $deleted = false;

            if ($this->institution->school_type_id === 2 && !empty($entry['primary_subject_id'])) {
                $deleted = PrimaryLearnerPerformance::where(array_merge($commonWhere, [
                    'primary_subject_id' => $entry['primary_subject_id'],
                ]))->forceDelete();
            }

            if ($this->institution->school_type_id === 3 && !empty($entry['secondary_subject_id'])) {
                $deleted = SecondaryLearnerPerformance::where(array_merge($commonWhere, [
                    'secondary_subject_id' => $entry['secondary_subject_id'],
                ]))->forceDelete();
            }


            unset($this->subjectGrades[$index]);
            $this->subjectGrades = array_values($this->subjectGrades);
        } catch (\Exception $e) {
            $this->isLoading = false;
        }

        $this->loadPerformanceGrades();
    }



    //update learner performance
    public function UpdateLearnerPerformance()
    {
        AdminLogActivity::addToLog('Learner Performance', '1', 'Update', 'User Updated Learner Performance', 'school');

        try {
            foreach ($this->subjectGrades as $entry) {
                $this->isLoading = true;

                if ($this->institution->school_type_id === 2) {
                    PrimaryLearnerPerformance::updateOrCreate(
                        [
                            'learner_id' => $this->selected_learner->id,
                            'school_id' => $this->institution->id,
                            'education_grade_id' => $this->selected_learner->education_grade_id,
                            'teaching_period_id' => $this->termId,
                            'primary_subject_id' => $entry['primary_subject_id'],
                        ],
                        [
                            'performance_grade_id' => $entry['performance_grade_id'],
                            'status' => true, 
                        ]
                    );
                } elseif ($this->institution->school_type_id === 3) {

                    SecondaryLearnerPerformance::updateOrCreate(
                        [
                            'learner_id' => $this->selected_learner->id,
                            'school_id' => $this->institution->id,
                            'education_grade_id' => $this->selected_learner->education_grade_id,
                            'teaching_period_id' => $this->termId,
                            'secondary_subject_id' => $entry['secondary_subject_id'],
                        ],
                        [
                            'performance_grade_id' => $entry['performance_grade_id'],
                            'status' => true, 
                        ]
                    );
                }

                $this->isLoading = false;
            }

            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Learner Performance Updated Successfully.'
            ]);
        } catch (\Exception $e) {
            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Error Updating Learner Performance. Please try again.' . $e->getMessage()
            ]);
        }
    }

    public function resetForm()
    {
        $this->selected_learner = null;
        $this->selectedLearnerName = '';
        $this->selectedLearnerLIN = '';
        $this->selectedLearnerGrade = '';
        $this->subjectGrades = [];
        $this->initializeSubjectGrades();
        $this->selectedLearner = null;
        $this->selectedLearnerGrade = null;
        $this->selectedLearnerLIN = null;
        $this->selectedLearnerName = null;
    }


    public function render()
    {
        return view('learnermanagement::livewire.learner-performance.learner-performance-section')

            ->extends('core::institution.layouts.design')
            ->section('content');
    }
}
