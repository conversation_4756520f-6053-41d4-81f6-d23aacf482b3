<?php

namespace Modules\LearnerManagement\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\LearnerManagement\Services\LearnerDuplicationDetectionService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class DetectLearnerDuplicatesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; 
    public $tries = 3; 

    public function __construct(
        public int $applicationId
    ) {}

    /**
     * Execute the job
     */
    public function handle(LearnerDuplicationDetectionService $duplicationService): void
    {
        Log::info("Starting learner duplicate detection for application {$this->applicationId}");
        
        try {
            // Fetch application data directly from database
            $application = DB::table('learner_enrolment_applications')
                ->where('id', $this->applicationId)
                ->first();
                
            if (!$application) {
                throw new \Exception("Application not found with ID: {$this->applicationId}");
            }
            
            $applicationArray = (array) $application;
            
            $duplicationService->detectAndStoreLearnerDuplicates($applicationArray);
            
            // Mark deduplication as completed
            DB::table('learner_enrolment_applications')
                ->where('id', $this->applicationId)
                ->update(['is_deduplicated' => true]);
            
            Log::info("Completed learner duplicate detection for application {$this->applicationId}");
            
        } catch (\Exception $e) {
            Log::error("Failed learner duplicate detection for application {$this->applicationId}: " . $e->getMessage());
            throw $e; 
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Learner duplicate detection job failed permanently for application {$this->applicationId}", [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

    }
}