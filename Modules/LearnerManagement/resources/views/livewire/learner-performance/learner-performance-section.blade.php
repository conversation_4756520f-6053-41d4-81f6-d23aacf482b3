<div>
@section('title', 'Learner Performance')

    <div class="nk-block-head nk-block-head-sm mb-3">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">Learner Performance</h3>
                <nav class="nk-block-des">
                    <ul class="breadcrumb breadcrumb-arrow">
                        <li class="breadcrumb-item">
                            <a class="text-primary">Dashboard</a>
                        </li>
                        <li class="breadcrumb-item active text-soft">
                            Learner Performance
                        </li>
                    </ul>
                </nav>
            </div>


            <!-- .nk-block-head-content -->
            <div class="nk-block-head-content">
                <div class="toggle-wrap nk-block-tools-toggle">
                    <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                    <div class="toggle-expand-content" data-content="pageMenu">
                        <ul class="nk-block-tools g-3">
                            <li>
                                <a href="{{ url('institution/learners/performance/overview') }}" target="_blank" rel="noopener" class="cursor btn bg-dark-teal">
                                    <span class="text-white">Performance Overview</span>
                                    <em class="icon ni ni-eye-fill text-white"></em>
                                </a>

                            </li>
                        </ul>
                    </div>
                </div><!-- .toggle-wrap -->
            </div><!-- .nk-block-head-content -->


        </div><!-- .nk-block-between -->
    </div><!-- .nk-block-head -->

    <!-- notification -->
    <livewire:emis-returns.notification-banner />
    <!-- notification -->

    <div class="nk-block" x-data="{
    applyFilters() {
    @this.call('filterLearners', this.filter);
    }
}

    ">
        <div class="card card-stretch card-bordered border-dark-teal">
            <div class="card-inner">

                <div class="card-inner position-relative card-tools-toggle">
                    <div class="card-title-group">
                        <div class="card-tools">
                            <form wire:submit.prevent="filterLearners">
                                <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">

                                    <div class="form-wrap" style="width: 200px !important">
                                        <div class="form-group ">
                                            <div class="form-control-wrap ">
                                                @if($institution->school_type_id === 2)
                                                <select wire:model="education_grade_id" class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer">
                                                    <option value="">All Classes</option>
                                                    @foreach($primary_education_grades as $grade)
                                                    <option value="{{$grade->id}}">{{$grade->name}}</option>
                                                    @endforeach
                                                </select>
                                                @elseif($institution->school_type_id === 3)
                                                <select wire:model="education_grade_id" class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer">
                                                    <option value="">All Classes</option>
                                                    @foreach($secondary_education_grades as $grade)
                                                    <option value="{{$grade->id}}">{{$grade->name}}</option>
                                                    @endforeach
                                                </select>
                                                @endif
                                                <span class="position-absolute" style="top: 50%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                    <em class="icon ni ni-chevron-down text-muted"></em>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-wrap" style="width: 200px !important">
                                        <div class="form-group ">
                                            <div class="form-control-wrap ">
                                                <select wire:model="gender" class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer">
                                                    <option value="">ALL GENDERS</option>
                                                    <option value="F">FEMALE</option>
                                                    <option value="M">MALE</option>
                                                </select>
                                                <span class="position-absolute" style="top: 50%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                    <em class="icon ni ni-chevron-down text-muted"></em>
                                                </span>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="form-wrap">
                                        <input style="width:320px;" wire:model="lin" class="form-control text-uppercase " placeholder="LIN">

                                        </input>
                                    </div>
                                    <div style="width: 320px !important" class="form-wrap">
                                        <div class="input-group">
                                            <input wire:model="learner_name" class="form-control text-uppercase" placeholder="Learner Name">
                                            <div class="input-group-append">
                                                @if($filtersApplied)
                                                <button wire:click.prevent="resetFilters" wire:input="filterLearners" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                    <em class="icon ni ni-cross"></em>
                                                </button>
                                                @endif
                                                <button class="btn rounded-right bg-dark-teal" type="submit">
                                                    <em class="icon ni ni-filter mr-1"></em>Apply
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div><!-- .card-tools -->
                    </div><!-- .card-title-group -->
                </div><!-- .card-inner -->
                <div class="card-inner p-0">
                    <div class="table-responsive">
                        <table class="nk-tb-list nk-tb-ulist is-compact">
                            <thead>
                                <tr class="nk-tb-item nk-tb-head bg-secondary">
                                    <th class="nk-tb-col px-2 text-white border-white cursor text-uppercase ">
                                        <span class="sub-text ucap text-white">LEARNER</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center ">
                                        <span class="sub-text ucap text-white">LEARNER LIN</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center ">
                                        <span class="sub-text ucap text-white">SEX</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center ">
                                        <span class="sub-text ucap text-white">CLASS</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center ">
                                        <span class="sub-text ucap text-white">NATIONALITY</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center ">
                                        <span class="sub-text ucap text-white">STATUS</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-right-0 text-uppercase  text-center">
                                        <span class="sub-text ucap text-white">Action</span>
                                    </th>
                                </tr>
                            </thead>
                            @if(count($learners_data) > 0)
                            <tbody wire:loading.remove wire:target="loadPaginatedLearners" class="border-bottom">
                                @foreach ($learners_data as $learner)
                                <tr class="nk-tb-item">
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <a
                                            :href="'/institution/learners/profile/' + '{{ $learner->learner->lin }}'"
                                            target="_blank"
                                            class="tb-lead cursor text-dark-teal">
                                            <div class="user-card">
                                                <div class="user-avatar">
                                                    <img
                                                        :src="'{{ $learner->person->photo_url }}'"
                                                        style="border-radius: 0"
                                                        :alt="'{{ $learner->person->initials }}'">
                                                </div>
                                                <div class="user-name text-uppercase">
                                                    <span class="text-dark-teal ">{{$learner->learner->person->surname}} {{$learner->learner->person->first_name}}</span>
                                                </div>
                                            </div>
                                        </a>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{$learner->learner->lin }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{$learner->person->gender}}</span>
                                    </td>

                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{$learner->education_grade->name}}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{$learner->learner->person->country->name}}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        @if($institution->school_type_id === 2)
                                        @if ($learner->learner && $learner->primary_performances && $learner->primary_performances->isNotEmpty())
                                        @php
                                        $status = $learner->primary_performances->first()->status;
                                        @endphp
                                        @if ($status === true)
                                        <span class="text-primary">
                                            <em class="icon ni ni-check-circle-fill"></em>
                                            <span>Updated</span>
                                        </span>
                                        @else
                                        <span class="text-dark-teal">
                                            <em class="icon ni ni-cross-circle-fill"></em>
                                            <span>Not Updated</span>
                                        </span>
                                        @endif
                                        @else
                                        <span class="text-dark-teal">
                                            <em class="icon ni ni-cross-circle-fill"></em>
                                            <span>Not Updated</span>
                                        </span>
                                        @endif
                                        @elseif($institution->school_type_id === 3)
                                        @if ($learner->learner && $learner->secondary_performances && $learner->secondary_performances->isNotEmpty())
                                        @php
                                        $status = $learner->secondary_performances->first()->status;
                                        @endphp
                                        @if ($status === true)
                                        <span class="text-primary">
                                            <em class="icon ni ni-check-circle-fill"></em>
                                            <span>Updated</span>
                                        </span>
                                        @else
                                        <span class="text-dark-teal">
                                            <em class="icon ni ni-cross-circle-fill"></em>
                                            <span>Not Updated</span>
                                        </span>
                                        @endif
                                        @else
                                        <span class="text-dark-teal">
                                            <em class="icon ni ni-cross-circle-fill"></em>
                                            <span>Not Updated</span>
                                        </span>
                                        @endif
                                        @endif
                                    </td>

                                    <td class="nk-tb-col px-2 align-middle text-center">
                                        <span role="button" tabindex="0"
                                            wire:click="selectLearner('{{ $learner->id }}')"
                                            data-toggle="modal"
                                            data-target="#updateLearnerPerformanceModal"
                                            title="Update Learner Performance"
                                            class="btn btn-xs btn-primary me-2">
                                            <em class="icon ni ni-edit-fill me-1"></em>Update
                                        </span>

                                        <span role="button" tabindex="0"
                                            wire:click="selectLearner('{{ $learner->id }}')"
                                            data-toggle="modal"
                                            data-target="#viewLearnerPerformanceModal"
                                            title="View Learner Performance"
                                            class="btn btn-xs btn-dark-teal me-2">
                                            <em class="icon ni ni-eye-fill me-1"></em>View
                                        </span>
                                    </td>

                                </tr>
                                @endforeach
                            </tbody>
                            @endif
                        </table>
                    </div>
                </div><!-- .card-inner -->
            </div>
            @if($learners_data->count() === 0)
            <div class="card-inner p-0">
                <div class="card-body">
                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                        <em class="icon ni ni-alert-circle"></em> There is no data to display at the moment.
                    </div>
                </div>
            </div>
            @else
            <div class="text-center my-3">
                <div wire:loading wire:target="loadPaginatedLearners">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <div class="mt-2">Loading Learners......</div>
                </div>
            </div>
            @endif

            <!-- pagination -->
            <div x-data="{
    currentPage: @entangle('currentPage'),
    perPage: @entangle('perPage'),
    total: @entangle('total'),
      init() {
        const urlPage = new URLSearchParams(window.location.search).get('page');
        if (urlPage && !isNaN(urlPage)) {
            this.currentPage = parseInt(urlPage);
            $wire.loadPaginatedLearners(this.currentPage);
        }
    },

    get totalPages() {
        return Math.ceil(this.total / this.perPage);
    },

    get from() {
        return (this.currentPage - 1) * this.perPage + 1;
    },

    get to() {
        return Math.min(this.currentPage * this.perPage, this.total);
    },

     changePage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            history.replaceState(null, null, `?page=${page}`);
            $wire.loadPaginatedLearners(page);
        }
    }
}" x-show="total > 0" class="card-inner d-flex flex-row  justify-content-between">
                <nav>
                    <ul class="pagination">
                        <!-- First -->
                        <li class="page-item" :class="{ 'disabled': currentPage === 1 }">
                            <a
                                class="page-link"
                                :class="{ 'disabled': currentPage === 1 }"
                                :tabindex="currentPage === 1 ? -1 : 0"
                                :aria-disabled="currentPage === 1"
                                @click.prevent="currentPage !== 1 && changePage(1)">
                                <em class="icon ni ni-chevrons-left"></em><span class="ms-1">First</span>
                            </a>
                        </li>

                        <!-- Previous -->
                        <li class="page-item" :class="{ 'disabled': currentPage === 1 }">
                            <a
                                class="page-link"
                                :class="{ 'disabled': currentPage === 1 }"
                                :tabindex="currentPage === 1 ? -1 : 0"
                                :aria-disabled="currentPage === 1"
                                @click.prevent="currentPage !== 1 && changePage(currentPage - 1)">
                                <em class="icon ni ni-chevron-left"></em><span class="ms-1">Previous</span>
                            </a>
                        </li>

                        <style>
                            .page-item.active .hover-active:hover {
                                background-color: #3546ab !important;
                                color: #fff !important;
                                cursor: pointer;
                                box-shadow: 0 0 0.25rem rgba(53, 70, 171, 0.4);
                            }
                        </style>


                        <!-- Pages -->
                        <template x-for="page in totalPages" :key="page">
                            <li class="page-item" :class="{ 'active': page === currentPage }">
                                <a
                                    class="page-link"
                                    href="#"
                                    @click.prevent="changePage(page)"
                                    :class="{ 'hover-active': page === currentPage }"
                                    x-text="page"></a>
                            </li>
                        </template>

                        <!-- Next -->
                        <li class="page-item" :class="{ 'disabled': currentPage === totalPages }">
                            <a
                                class="page-link"
                                :class="{ 'disabled': currentPage === totalPages }"
                                :tabindex="currentPage === totalPages ? -1 : 0"
                                :aria-disabled="currentPage === totalPages"
                                @click.prevent="currentPage !== totalPages && changePage(currentPage + 1)">
                                <span class="me-1">Next</span><em class="icon ni ni-chevron-right"></em>
                            </a>
                        </li>

                        <!-- Last -->
                        <li class="page-item" :class="{ 'disabled': currentPage === totalPages }">
                            <a
                                class="page-link"
                                :class="{ 'disabled': currentPage === totalPages }"
                                :tabindex="currentPage === totalPages ? -1 : 0"
                                :aria-disabled="currentPage === totalPages"
                                @click.prevent="currentPage !== totalPages && changePage(totalPages)">
                                <span class="me-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                            </a>
                        </li>
                    </ul>
                </nav>


                <div class="d-flex row-flex px-3 ml-4">
                    <span class="align-self-center  ">Show</span>
                    <div class="form-wrap" style="width: 90px !important; margin-left:10px;">
                        <div class="form-group ">
                            <div class="form-control-wrap  ">
                                <select wire:input="loadPaginatedLearners" wire:model="perPage" class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer">
                                    <option value="15">15</option>
                                    <option value="30">30</option>
                                    <option value="50">50</option>
                                    <option value="70">70</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="position-absolute" style="top: 50%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                    <em class="icon ni ni-chevron-down text-muted"></em>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex ml-4">
                    <span class="align-self-center">
                        Showing <span class="text-primary" x-text="from"></span> to
                        <span class="text-primary" x-text="to"></span> of
                        <span class="text-primary" x-text="total"></span>
                    </span>
                </div>
            </div>

            <!-- pagination -->
        </div><!-- .card -->
    </div><!-- .n-block -->



    <!-- update learner performance modal -->
    <div wire:ignore.self class=" modal fade zoom" tabindex="-1" id="updateLearnerPerformanceModal">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <a class="cursor close" wire:click="resetForm" data-dismiss="modal" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>

                <div class="  border-0 ">
                    <div class="modal-header">
                        <h6 class="text-uppercase modal-title font-bold">UPDATE LEARNER PERFORMANCE</h6>
                    </div>
                    <div class="card-inner text-dark text-uppercase">
                        <h6 class="title  fw-bold mb-1 ">Name: {{ $selectedLearnerName }}</h6>
                        <p class=" mb-1"><span>LIN:</span> {{ $selectedLearnerLIN }}</p>
                        <p class=" mb-1"><span>Class / Grade:</span> {{ $selectedLearnerGrade }}</p>
                    </div>
                </div>

                <div class="modal-body">

                    <div
                        x-data="{
    subjectGrades: $wire.entangle('subjectGrades'),

    addSubject() {
        if (!this.subjectGrades || !Array.isArray(this.subjectGrades)) {
            this.subjectGrades = [];
        }

        this.subjectGrades.push({
            primary_subject_id: '',
            secondary_subject_id:'',
            performance_grade_id: '',
            subject: '',
            grade: '',
            expanded: true
        });
        this.subjectGrades = [...this.subjectGrades];
    },

    removeSubject(index) {
        if (!this.subjectGrades || !Array.isArray(this.subjectGrades)) return;
         const entry = this.subjectGrades[index];
         const isSaved = !!(entry?.id || entry?.primary_subject_id || entry?.secondary_subject_id);

       if (isSaved) {
        $wire.deleteSubjectGrade(index);
      } else {
      
        this.subjectGrades.splice(index, 1);
      }
    }
}">
                        <div style="max-height: 500px; overflow-y: overlay; overflow-x: hidden;">
                            <form class="-margin-top:50px;" x-on:submit.prevent="
        $wire.set('subjectGrades', subjectGrades)
             .then(() => $wire.call('UpdateLearnerPerformance'))">
                                <template x-for="(entry, index) in subjectGrades" :key="index">
                                    <div class="border border-primary  p-3 rounded mb-3" x-data="{ open: true }">
                                        <div style="height:2px;" class="d-flex justify-content-between align-items-center p-2">
                                            <h6 style="font-size:13px;" class="mb-0  text-uppercase font-weight-bold ">
                                                <template x-if="entry.expanded">
                                                    <span x-cloak>
                                                        Subject Performance Update <span x-text="index + 1"></span>
                                                    </span>
                                                </template>

                                                <template x-if="!entry.expanded">
                                                    <span x-cloak x-text="entry.subject && entry.grade ? `${entry.subject} - ${entry.grade}` : `Subject Performance Update ${index + 1}`"></span>
                                                </template>
                                            </h6>

                                            <div class="d-flex items-center space-x-1">

                                                <span
                                                    @click.prevent="entry.expanded = !entry.expanded"
                                                    class="text-primary btn flex items-center justify-center w-10 h-10"
                                                    data-toggle="tooltip" data-bs-placement="top"
                                                    title="Expand/Collapse">
                                                    <span x-show="entry.expanded">
                                                        <em class="icon ni ni-chevron-up-circle text-2xl"></em>
                                                    </span>
                                                    <span x-show="!entry.expanded">
                                                        <em class="icon ni ni-chevron-down-circle text-2xl"></em>
                                                    </span>
                                                </span>


                                                <span
                                                    @click.prevent="removeSubject(index)"
                                                    class="btn text-danger flex items-center justify-center w-10 h-10"
                                                    data-toggle="tooltip" data-bs-placement="top"
                                                    title="Remove Subject Performance">
                                                    <em class="icon ni ni-cross-circle text-2xl"></em>
                                                </span>
                                            </div>


                                        </div>

                                        <div class="mt-2" x-show="entry.expanded" x-transition>
                                            <div class="row g-4">
                                                <div class="col-lg-6">
                                                    <div class="form-group ">
                                                        <div class="form-control-wrap">
                                                            <label class="form-label">Subjects <span class="text-danger">*</span></label>
                                                            @if($institution->school_type_id === 2)
                                                            <select required
                                                                :name="`subjectGrades.${index}.primary_subject_id`"
                                                                :id="`subjectGrades.${index}.primary_subject_id`"
                                                                x-model="entry.primary_subject_id"
                                                                class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer"
                                                                @change="entry.subject = $event.target.options[$event.target.selectedIndex].text">
                                                                <option value="">-- Select --</option>
                                                                @foreach($primary_subjects as $subject)
                                                                <option value="{{ $subject->id }}"
                                                                    :disabled="Array.isArray(subjectGrades) && subjectGrades.some((e, i) => e.primary_subject_id == '{{ $subject->id }}' && i !== index)"
                                                                    value="{{ $subject->id }}">
                                                                    {{ $subject->name }}
                                                                </option>

                                                                @endforeach
                                                            </select>
                                                            <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                                <em class="icon ni ni-chevron-down text-muted"></em>
                                                            </span>
                                                            @endif

                                                            @if($institution->school_type_id === 3)
                                                            <select required
                                                                :name="`subjectGrades.${index}.secondary_subject_id`"
                                                                :id="`subjectGrades.${index}.secondary_subject_id`"
                                                                x-model="entry.secondary_subject_id"
                                                                class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer"
                                                                @change="entry.subject = $event.target.options[$event.target.selectedIndex].text">
                                                                <option value="">-- Select --</option>
                                                                @foreach($secondary_subjects as $subject)
                                                                <option value="{{ $subject->id }}"
                                                                    :disabled="Array.isArray(subjectGrades) && subjectGrades.some((e, i) => e.secondary_subject_id == '{{ $subject->id }}' && i !== index)"
                                                                    value="{{ $subject->id }}">
                                                                    {{ $subject->name }}
                                                                </option>

                                                                @endforeach
                                                            </select>
                                                            <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                                <em class="icon ni ni-chevron-down text-muted"></em>
                                                            </span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div><!-- .col -->

                                                <div class="col-lg-6">
                                                    <div class="form-group position-relative">
                                                        <div class="form-control-wrap">
                                                            <label class="form-label">Grades <span class="text-danger">*</span></label>
                                                            @if($institution->school_type_id === 2)
                                                            <select required
                                                                :name="`subjectGrades.${index}.performance_grade_id`"
                                                                :id="`subjectGrades.${index}.performance_grade_id`"
                                                                x-model="entry.performance_grade_id"
                                                                class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer"
                                                                @change="entry.grade = $event.target.options[$event.target.selectedIndex].text">

                                                                <option value="">-- Select --</option>
                                                                @foreach($primary_performance_grades as $grade)
                                                                <option value="{{ $grade->id }}">{{ $grade->name }}</option>
                                                                @endforeach

                                                            </select>
                                                            <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                                <em class="icon ni ni-chevron-down text-muted"></em>
                                                            </span>

                                                            @elseif($institution->school_type_id === 3)

                                                            <select required
                                                                :name="`subjectGrades.${index}.performance_grade_id`"
                                                                :id="`subjectGrades.${index}.performance_grade_id`"
                                                                x-model="entry.performance_grade_id"
                                                                class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer"
                                                                @change="entry.grade = $event.target.options[$event.target.selectedIndex].text">

                                                                <option value="">-- Select --</option>
                                                                @foreach($secondary_performance_grades as $grade)
                                                                <option value="{{ $grade->id }}">{{ $grade->name }}</option>
                                                                @endforeach

                                                            </select>
                                                            <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                                <em class="icon ni ni-chevron-down text-muted"></em>
                                                            </span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div><!-- .col -->
                                            </div><!-- .row -->
                                        </div>
                                    </div>
                                </template>

                        </div>
                        <div class="row g-4">

                            <div class="col-lg-12">
                                <button @click.prevent="addSubject" style="margin-top:30px; width: 210px;" class=" p-2 h-50 btn btn-primary ">
                                    <em class="icon ni ni-plus mr-1"></em>
                                    Add Subject Performance</button>
                            </div><!-- .col -->
                        </div><!-- .row -->

                    </div>


                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button wire:click="resetForm" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                        <em class="icon ni ni-cross"></em><span>Cancel</span>
                    </button>
                    <button type="submit" class="btn bg-dark-teal d-flex " wire:target="UpdateLearnerPerformance" wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="UpdateLearnerPerformance" class="align-self-center">Submit</span>
                        <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="UpdateLearnerPerformance"></em>
                        <span wire:loading wire:target="UpdateLearnerPerformance" class="align-self-center ">
                            <span wire:loading wire:target="UpdateLearnerPerformance" class="align-self-center d-flex align-items-center">
                                <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                Saving...
                            </span>
                            <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="UpdateLearnerPerformance"></em>
                        </span>
                    </button>
                </div>

                </form>
            </div>
        </div>
    </div>


    <!-- update learner performance modal -->

    <!-- view learners subject modal -->
    <div wire:ignore.self class=" modal fade zoom" tabindex="-1" id="viewLearnerPerformanceModal">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <a class="cursor close" wire:click="resetForm" data-dismiss="modal" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>


                <div class="  border-0 ">
                    <div class="modal-header">
                        <h6 class="text-uppercase modal-title font-bold">VIEW LEARNER PERFORMANCE</h6>
                    </div>
                    <div class="card-inner text-dark text-uppercase">
                        <h6 class="title  fw-bold mb-1 ">Name: {{ $selectedLearnerName }}</h6>
                        <p class=" mb-1"><span>LIN:</span> {{ $selectedLearnerLIN }}</p>
                        <p class=" mb-1"><span>Class / Grade:</span> {{ $selectedLearnerGrade }}</p>
                    </div>
                </div>
                <class class="modal-content">

                    <div class="card-inner p-0 p-2 mb-3" style="max-height: 450px; overflow-y: overlay; overflow-x: hidden;">
                        <div class="table-responsive">

                            <table class="nk-tb-list nk-tb-ulist is-compact">
                                <thead>
                                    <tr class="nk-tb-item nk-tb-head bg-secondary">
                                        <th class="nk-tb-col px-2 text-white border-white cursor text-uppercase ">
                                            <span class="sub-text ucap text-white">NO</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase  ">
                                            <span class="sub-text ucap text-white">SUBJECT</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center ">
                                            <span class="sub-text ucap text-white">GRADE</span>
                                        </th>
                                    </tr>
                                </thead>

                                @if(count($subjectGrades) > 0)
                                <tbody wire:loading.remove wire:target="selectLearner" class="border-bottom">
                                    @foreach ($subjectGrades as $index => $entry)
                                    <tr class="nk-tb-item">
                                        <td class="nk-tb-col px-1  py-2 align-middle text-uppercase text-left">
                                            <span class="text-dark tb-lead">{{ $index + 1 }}<span>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase ml-5">
                                            <span class="text-dark tb-lead">{{ $entry['subject'] }}</span>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <span class="text-dark tb-lead">{{ $entry['grade'] }}</span>
                                        </td>

                                    </tr>
                                    @endforeach
                                </tbody>
                                @endif
                            </table>

                            @if(count($subjectGrades) === 0)
                            <div class="card-inner p-0">
                                <div class="card-body">
                                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                        <em class="icon ni ni-alert-circle"></em> There are no subjects grades to display at the moment.
                                    </div>
                                </div>
                            </div>
                            @else

                            <div class="text-center my-3">
                                <div wire:loading data-target="selectLearner">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    <div class="mt-2">Loading Learner Performance Grades......</div>
                                </div>
                            </div>
                            @endif

                        </div>
                    </div><!-- .card-inner -->

            </div>

        </div>
    </div>
    <!-- view learners subject modal -->


</div>