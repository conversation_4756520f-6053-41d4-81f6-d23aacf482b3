<div class="p-6">
    <div class="d-flex justify-content-between align-items-center ">
        <h3 class="text-xl font-bold ">Attendance Summary</h3>
        <a href="{{ route('learner-attendance') }}" wire:navigate class="btn btn-dark-teal">
            <em class="icon ni ni-arrow-left mr-1"></em>
            Back
        </a>
    </div>
    <p> Information about your learners attendance</p>
    @if (!$filtersApplied)
        <div class="alert alert-info">
            Showing attendance summary for <strong>today</strong> ({{ now()->format('M d, Y') }}).
        </div>
    @else
        <div class="alert alert-info">
            Showing results for:
            @if ($startDate) <strong>{{ \Carbon\Carbon::parse($startDate)->format('M d, Y') }}</strong> @endif
            @if ($endDate && $endDate !== $startDate) to <strong>{{ \Carbon\Carbon::parse($endDate)->format('M d, Y') }}</strong> @endif
        </div>
    @endif


    <!-- Filters -->
    <div class="mb-6 p-4 bg-white rounded shadow-md">
        <div class="row mb-3">
            <div class="col-md-2">
                <select wire:model="selected_grade_id" class="form-control">
                    <option value="">Select Class</option>
                    @forelse ($grades as $grade)
                        <option value="{{ $grade->id }}">{{ $grade->name }}</option>
                    @empty
                        <option disabled>No classes available</option>
                    @endforelse
                </select>
            </div>

            <div class="col-md-2">
                <select wire:model="selected_gender" class="form-control">
                    <option value="">Select Sex</option>
                    <option value="M">Male</option>
                    <option value="F">Female</option>
                </select>
            </div>

            <div class="col-md-2">
                <select wire:model="selected_status" class="form-control">
                    <option value="">Select Status</option>
                    <option value="present">Present</option>
                    <option value="absent">Absent</option>
                </select>
            </div>

            <!-- Start Date -->
            <div class="col-md-2">
                <input type="date" wire:model="startDate" class="form-control" max="{{ now()->toDateString() }}" placeholder="Start Date">
            </div>

            <!-- End Date -->
            <div class="col-md-2">
                <input type="date" wire:model="endDate" class="form-control" max="{{ now()->toDateString() }}" placeholder="End Date">
            </div>


            <div class="col-md-2">
                <form wire:submit.prevent="applyFilters">
                    <div class="input-group">
                        <!-- <input type="text" wire:model.debounce.500ms="searchTerm" class="form-control bg-primary-dim" placeholder="Search learner"> -->
                        <div class="input-group-append">
                            @if ($searchActive)
                                <button type="button" wire:click="resetSearch" class="btn bg-secondary text-white">
                                    <em class="icon ni ni-cross"></em>
                                </button>
                            @endif
                            <button type="submit" class="btn bg-dark-teal text-white">
                                <em class="icon ni ni-filter mr-1"></em> Apply
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Stats -->
    <div class="row">

        <div class="card-body p-0 mx-3">
            <table class="table table-bordered mb-0">
                <thead class="bg-secondary text-white">
                <tr>
                    <th class="col-md-2">Class</th>
                    <th class="col-md-2">Sex</th>
                    <th class="text-end col-md-2">Number of Learners</th>
                    @if ($selected_status === 'present')
                        <th class="text-end col-md-2">Present learners</th>
                    @elseif ($selected_status === 'absent')
                        <th class="text-end col-md-2">Absent learners</th>
                    @else
                        <th class="text-end col-md-2">Present learners</th>
                        <th class="text-end col-md-2">Absent learners</th>
                    @endif
                </tr>
                </thead>
                <tbody>
                @if ($filtersApplied)
                    @php
                        $grouped = collect($learnersBySexPerClass)->groupBy('class');
                    @endphp

                    @forelse ($grouped as $class => $records)
                        @php
                            $male = $records->firstWhere('gender', 'M');
                            $female = $records->firstWhere('gender', 'F');
                        @endphp

                        @if (!$selected_gender || $selected_gender === 'M')
                            <tr>
                                <td rowspan="{{ $selected_gender ? 1 : 2 }}">{{ $class }}</td>
                                <td>Male</td>
                                <td class="text-end">{{ $male?->count ?? 0 }}</td>

                                @if ($selected_status === 'present')
                                    <td class="text-end">{{ $male?->presentCount ?? 0 }}</td>
                                @elseif ($selected_status === 'absent')
                                    <td class="text-end">{{ $male?->absentCount ?? 0 }}</td>
                                @else
                                    <td class="text-end">{{ $male?->presentCount ?? 0 }}</td>
                                    <td class="text-end">{{ $male?->absentCount ?? 0 }}</td>
                                @endif
                            </tr>
                        @endif

                        @if (!$selected_gender || $selected_gender === 'F')
                            <tr @if($selected_gender) @else @endif>
                                @if ($selected_gender) <td>{{ $class }}</td> @endif
                                <td>Female</td>
                                <td class="text-end">{{ $female?->count ?? 0 }}</td>

                                @if ($selected_status === 'present')
                                    <td class="text-end">{{ $female?->presentCount ?? 0 }}</td>
                                @elseif ($selected_status === 'absent')
                                    <td class="text-end">{{ $female?->absentCount ?? 0 }}</td>
                                @else
                                    <td class="text-end">{{ $female?->presentCount ?? 0 }}</td>
                                    <td class="text-end">{{ $female?->absentCount ?? 0 }}</td>
                                @endif
                            </tr>
                        @endif
                    @empty
                        <tr>
                            <td colspan="5" class="text-center">No data matches the selected filters.</td>
                        </tr>
                    @endforelse

                @else
                    @foreach ($grades as $grade)
                        @php
                            $male = collect($learnersBySexPerClass)->firstWhere(fn($row) => $row->class === $grade->name && $row->gender === 'M');
                            $female = collect($learnersBySexPerClass)->firstWhere(fn($row) => $row->class === $grade->name && $row->gender === 'F');
                        @endphp

                        <tr>
                            <td rowspan="2">{{ $grade->name }}</td>
                            <td>Male</td>
                            <td class="text-end">{{ $male?->count ?? 0 }}</td>

                            @if ($selected_status === 'present')
                                <td class="text-end">{{ $male?->presentCount ?? 0 }}</td>
                            @elseif ($selected_status === 'absent')
                                <td class="text-end">{{ $male?->absentCount ?? 0 }}</td>
                            @else
                                <td class="text-end">{{ $male?->presentCount ?? 0 }}</td>
                                <td class="text-end">{{ $male?->absentCount ?? 0 }}</td>
                            @endif
                        </tr>
                        <tr>
                            <td>Female</td>
                            <td class="text-end">{{ $female?->count ?? 0 }}</td>

                            @if ($selected_status === 'present')
                                <td class="text-end">{{ $female?->presentCount ?? 0 }}</td>
                            @elseif ($selected_status === 'absent')
                                <td class="text-end">{{ $female?->absentCount ?? 0 }}</td>
                            @else
                                <td class="text-end">{{ $female?->presentCount ?? 0 }}</td>
                                <td class="text-end">{{ $female?->absentCount ?? 0 }}</td>
                            @endif
                        </tr>
                    @endforeach
                @endif
                @if (!$filtersApplied || collect($learnersBySexPerClass)->isNotEmpty())
                    <tr class="fw-bold bg-light">
                        <td colspan="2" class="text-end">Total Learners</td>
                        <td class="text-end">
                            {{ collect($learnersBySexPerClass)->sum('count') }}
                        </td>

                        @if ($selected_status === 'present')
                            <td class="text-end">
                                {{ collect($learnersBySexPerClass)->sum('presentCount') }}
                            </td>
                        @elseif ($selected_status === 'absent')
                            <td class="text-end">
                                {{ collect($learnersBySexPerClass)->sum('absentCount') }}
                            </td>
                        @else
                            <td class="text-end">
                                {{ collect($learnersBySexPerClass)->sum('presentCount') }}
                            </td>
                            <td class="text-end">
                                {{ collect($learnersBySexPerClass)->sum('absentCount') }}
                            </td>
                        @endif
                    </tr>
                @endif

                </tbody>



            </table>
        </div>
    </div>

</div>
