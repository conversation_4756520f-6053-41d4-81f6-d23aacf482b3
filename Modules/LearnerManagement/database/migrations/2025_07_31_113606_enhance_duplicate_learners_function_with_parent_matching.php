<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop existing function to replace with enhanced version
        DB::statement('DROP FUNCTION IF EXISTS find_duplicate_learners(TEXT, TEXT, DATE, TEXT, TEXT, TEXT, DATE, TEXT, TEXT, TEXT, TEXT, REAL, BIGINT)');
        
        DB::statement("
            CREATE OR REPLACE FUNCTION find_duplicate_learners(
                p_learner_first_name TEXT,
                p_learner_surname TEXT,
                p_learner_birth_date DATE,
                p_learner_gender TEXT,
                p_parent_first_name TEXT DEFAULT '',
                p_parent_surname TEXT DEFAULT '',
                p_parent_birth_date DATE DEFAULT NULL,
                p_learner_other_names TEXT DEFAULT '',
                p_learner_id_number TEXT DEFAULT '',
                p_parent_other_names TEXT DEFAULT '',
                p_parent_id_number TEXT DEFAULT '',
                p_similarity_threshold REAL DEFAULT 0.7,
                p_exclude_person_id BIGINT DEFAULT NULL
            )
            RETURNS TABLE(
                learner_id BIGINT,
                match_type TEXT,
                confidence_score REAL,
                learner_name TEXT,
                learner_birth_date DATE,
                learner_gender TEXT,
                learner_id_number TEXT,
                school_id BIGINT,
                parent_name TEXT,
                parent_birth_date DATE,
                parent_id_number TEXT,
                name_similarity REAL,
                parent_similarity REAL,
                date_created TIMESTAMP
            ) AS \$function\$
            BEGIN
                RETURN QUERY
                WITH learner_matches AS (
                    SELECT 
                        p.id as person_id,
                        -- Learner similarity score
                        GREATEST(
                            CASE 
                                WHEN COALESCE(p.id_number, '') != '' AND COALESCE(p.id_number, '') = COALESCE(p_learner_id_number, '') 
                                THEN 1.0::REAL 
                                ELSE 0.0::REAL 
                            END,
                            similarity(
                                standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                                COALESCE(p.standardized_name, '')
                            )::REAL
                        ) as learner_sim,
                        
                        (COALESCE(p.first_name, '') || ' ' || COALESCE(p.surname, ''))::TEXT as learner_full_name,
                        p.birth_date as learner_bd,
                        COALESCE(p.gender, '')::TEXT as learner_gen,
                        COALESCE(p.id_number, '')::TEXT as learner_id_num,
                        p.date_created as person_created
                        
                    FROM persons p
                    WHERE p.deleted_at IS NULL 
                    AND (p_exclude_person_id IS NULL OR p.id != p_exclude_person_id)
                    AND (
                        -- Exact ID matches
                        (COALESCE(p_learner_id_number, '') != '' AND COALESCE(p.id_number, '') = COALESCE(p_learner_id_number, ''))
                        
                        OR
                        
                        -- Name and birth date similarity
                        (
                            p.birth_date = p_learner_birth_date
                            AND COALESCE(p.gender, '') = COALESCE(p_learner_gender, '')
                            AND similarity(
                                standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                                COALESCE(p.standardized_name, '')
                            ) >= (p_similarity_threshold - 0.2)
                        )
                        
                        OR
                        
                        -- Trigram search with birth date
                        (
                            p.standardized_name IS NOT NULL
                            AND p.standardized_name != ''
                            AND p.standardized_name % standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names)
                            AND p.birth_date = p_learner_birth_date
                            AND similarity(
                                standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                                p.standardized_name
                            ) >= (p_similarity_threshold - 0.2)
                        )
                    )
                    AND similarity(
                        standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                        COALESCE(p.standardized_name, '')
                    ) >= (p_similarity_threshold - 0.2)
                ),
                parent_matches AS (
                    SELECT DISTINCT ON (lm.person_id) 
                        lm.person_id,
                        -- Parent information
                        COALESCE(parent_person.first_name || ' ' || parent_person.surname, '')::TEXT as parent_full_name,
                        parent_person.birth_date as parent_bd,
                        COALESCE(parent_person.id_number, '')::TEXT as parent_id_num,
                        
                        -- Parent similarity calculation
                        CASE 
                            WHEN p_parent_first_name = '' AND p_parent_surname = '' THEN 0.0::REAL
                            ELSE
                                GREATEST(
                                    -- ID number exact match
                                    CASE 
                                        WHEN COALESCE(p_parent_id_number, '') != '' 
                                             AND COALESCE(parent_person.id_number, '') = COALESCE(p_parent_id_number, '')
                                        THEN 1.0::REAL
                                        ELSE 0.0::REAL
                                    END,
                                    
                                    -- Name similarity with weighted scoring
                                    (
                                        COALESCE(similarity(
                                            standardize_names(p_parent_first_name, p_parent_surname, p_parent_other_names),
                                            COALESCE(parent_person.standardized_name, '')
                                        ), 0.0) * 0.6 +
                                        
                                        -- Gender match bonus
                                        CASE 
                                            WHEN COALESCE(parent_person.gender, '') = '' OR p_learner_gender = '' THEN 0.0
                                            WHEN parent_person.gender != p_learner_gender THEN 0.2  -- Different gender parent gets bonus
                                            ELSE 0.1  -- Same gender parent gets smaller bonus
                                        END +
                                        
                                        -- Birth date proximity bonus
                                        CASE 
                                            WHEN p_parent_birth_date IS NULL OR parent_person.birth_date IS NULL THEN 0.0
                                            WHEN p_parent_birth_date = parent_person.birth_date THEN 0.3
                                            WHEN ABS(EXTRACT(YEAR FROM p_parent_birth_date) - EXTRACT(YEAR FROM parent_person.birth_date)) <= 2 THEN 0.1
                                            ELSE 0.0
                                        END
                                    )::REAL
                                )
                        END as parent_sim
                        
                    FROM learner_matches lm
                    LEFT JOIN learner_parents lp ON lp.learner_id = lm.person_id 
                                                 AND lp.deleted_at IS NULL
                    LEFT JOIN persons parent_person ON lp.parent_id = parent_person.id 
                                                    AND parent_person.deleted_at IS NULL
                    WHERE lp.learner_id IS NOT NULL
                    ORDER BY lm.person_id,
                             -- Prefer parents with matching/complementary gender to learner
                             CASE 
                                 WHEN p_learner_gender != '' AND parent_person.gender != '' AND parent_person.gender != p_learner_gender THEN 1
                                 ELSE 2 
                             END,
                             -- Then prefer primary contact
                             CASE WHEN lp.is_next_of_kin THEN 1 ELSE 2 END,
                             -- Then prefer actual parents over guardians
                             CASE WHEN lp.is_parent_yn THEN 1 ELSE 2 END,
                             lp.id
                )
                
                SELECT 
                    lm.person_id,
                    
                    -- Enhanced match classification including parent similarity
                    CASE 
                        WHEN COALESCE(lm.learner_sim, 0) >= 1.0 THEN 'EXACT_ID_MATCH'::TEXT
                        WHEN COALESCE(lm.learner_sim, 0) >= 0.95 AND COALESCE(pm.parent_sim, 0) >= 0.8 THEN 'FAMILY_EXACT_MATCH'::TEXT  
                        WHEN COALESCE(lm.learner_sim, 0) >= 0.85 AND COALESCE(pm.parent_sim, 0) >= 0.7 THEN 'FAMILY_HIGH_CONFIDENCE'::TEXT
                        WHEN COALESCE(lm.learner_sim, 0) >= 0.85 THEN 'HIGH_CONFIDENCE'::TEXT
                        WHEN COALESCE(lm.learner_sim, 0) >= 0.7 AND COALESCE(pm.parent_sim, 0) >= 0.5 THEN 'FAMILY_MEDIUM_CONFIDENCE'::TEXT
                        ELSE 'MEDIUM_CONFIDENCE'::TEXT
                    END,
                    
                    -- Overall confidence score (weighted combination)
                    CASE 
                        WHEN COALESCE(pm.parent_sim, 0) > 0 THEN 
                            (COALESCE(lm.learner_sim, 0) * 0.7 + COALESCE(pm.parent_sim, 0) * 0.3)::REAL
                        ELSE 
                            COALESCE(lm.learner_sim, 0)::REAL
                    END,
                    
                    lm.learner_full_name,
                    lm.learner_bd,
                    lm.learner_gen,
                    lm.learner_id_num,
                    NULL::BIGINT, -- school_id placeholder
                    COALESCE(pm.parent_full_name, '')::TEXT,
                    pm.parent_bd,
                    COALESCE(pm.parent_id_num, '')::TEXT,
                    lm.learner_sim,
                    COALESCE(pm.parent_sim, 0.0),
                    lm.person_created
                    
                FROM learner_matches lm
                LEFT JOIN parent_matches pm ON lm.person_id = pm.person_id
                ORDER BY 
                    -- Overall confidence score
                    CASE 
                        WHEN COALESCE(pm.parent_sim, 0) > 0 THEN 
                            (COALESCE(lm.learner_sim, 0) * 0.7 + COALESCE(pm.parent_sim, 0) * 0.3)
                        ELSE 
                            COALESCE(lm.learner_sim, 0)
                    END DESC, 
                    lm.learner_sim DESC, 
                    lm.person_created ASC
                LIMIT 20;
            END;
            \$function\$ LANGUAGE plpgsql;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore the previous version of the function
        DB::statement('DROP FUNCTION IF EXISTS find_duplicate_learners(TEXT, TEXT, DATE, TEXT, TEXT, TEXT, DATE, TEXT, TEXT, TEXT, TEXT, REAL, BIGINT)');
        
        // Recreate the simpler version (copy from the previous migration)
        DB::statement("
            CREATE OR REPLACE FUNCTION find_duplicate_learners(
                p_learner_first_name TEXT,
                p_learner_surname TEXT,
                p_learner_birth_date DATE,
                p_learner_gender TEXT,
                p_parent_first_name TEXT DEFAULT '',
                p_parent_surname TEXT DEFAULT '',
                p_parent_birth_date DATE DEFAULT NULL,
                p_learner_other_names TEXT DEFAULT '',
                p_learner_id_number TEXT DEFAULT '',
                p_parent_other_names TEXT DEFAULT '',
                p_parent_id_number TEXT DEFAULT '',
                p_similarity_threshold REAL DEFAULT 0.7,
                p_exclude_person_id BIGINT DEFAULT NULL
            )
            RETURNS TABLE(
                learner_id BIGINT,
                match_type TEXT,
                confidence_score REAL,
                learner_name TEXT,
                learner_birth_date DATE,
                learner_gender TEXT,
                learner_id_number TEXT,
                school_id BIGINT,
                parent_name TEXT,
                parent_birth_date DATE,
                parent_id_number TEXT,
                name_similarity REAL,
                parent_similarity REAL,
                date_created TIMESTAMP
            ) AS \$function\$
            BEGIN
                RETURN QUERY
                SELECT 
                    p.id,
                    CASE 
                        WHEN COALESCE(p.id_number, '') != '' AND COALESCE(p.id_number, '') = COALESCE(p_learner_id_number, '')
                            THEN 'EXACT_ID_MATCH'::TEXT
                        WHEN similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            COALESCE(p.standardized_name, '')
                        ) >= 0.95 AND p.birth_date = p_learner_birth_date AND COALESCE(p.gender, '') = COALESCE(p_learner_gender, '')
                            THEN 'EXACT_LEARNER_MATCH'::TEXT
                        WHEN similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            COALESCE(p.standardized_name, '')
                        ) >= 0.85 AND p.birth_date = p_learner_birth_date
                            THEN 'HIGH_CONFIDENCE'::TEXT
                        ELSE 'MEDIUM_CONFIDENCE'::TEXT
                    END,
                    
                    GREATEST(
                        CASE 
                            WHEN COALESCE(p.id_number, '') != '' AND COALESCE(p.id_number, '') = COALESCE(p_learner_id_number, '') 
                            THEN 1.0::REAL 
                            ELSE 0.0::REAL 
                        END,
                        similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            COALESCE(p.standardized_name, '')
                        )::REAL
                    ),
                    
                    (COALESCE(p.first_name, '') || ' ' || COALESCE(p.surname, ''))::TEXT,
                    p.birth_date,
                    COALESCE(p.gender, '')::TEXT,
                    COALESCE(p.id_number, '')::TEXT,
                    NULL::BIGINT,
                    ''::TEXT,
                    NULL::DATE,
                    ''::TEXT,
                    similarity(
                        standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                        COALESCE(p.standardized_name, '')
                    )::REAL,
                    0.0::REAL,
                    p.date_created
                    
                FROM persons p
                WHERE p.deleted_at IS NULL 
                AND (p_exclude_person_id IS NULL OR p.id != p_exclude_person_id)
                AND (
                    (COALESCE(p_learner_id_number, '') != '' AND COALESCE(p.id_number, '') = COALESCE(p_learner_id_number, ''))
                    OR
                    (
                        p.birth_date = p_learner_birth_date
                        AND COALESCE(p.gender, '') = COALESCE(p_learner_gender, '')
                        AND similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            COALESCE(p.standardized_name, '')
                        ) >= (p_similarity_threshold - 0.2)
                    )
                    OR
                    (
                        p.standardized_name IS NOT NULL
                        AND p.standardized_name != ''
                        AND p.standardized_name % standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names)
                        AND p.birth_date = p_learner_birth_date
                        AND similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            p.standardized_name
                        ) >= (p_similarity_threshold - 0.2)
                    )
                )
                AND similarity(
                    standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                    COALESCE(p.standardized_name, '')
                ) >= (p_similarity_threshold - 0.2)
                ORDER BY 3 DESC, 12 DESC, p.date_created ASC
                LIMIT 20;
            END;
            \$function\$ LANGUAGE plpgsql;
        ");
    }
};