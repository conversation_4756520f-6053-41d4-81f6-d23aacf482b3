<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('learner_enrolment_applications', function (Blueprint $table) {
            $table->boolean('is_deduplicated')->default(false)->comment('TRUE if deduplication process has completed, FALSE if still pending');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('learner_enrolment_applications', function (Blueprint $table) {
            $table->dropColumn('is_deduplicated');
        });
    }
};
