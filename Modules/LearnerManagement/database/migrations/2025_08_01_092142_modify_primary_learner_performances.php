<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('primary_learner_performances', function (Blueprint $table) {
          
            $table->boolean('status')
            ->nullable()
            ->default(false)
            ->comment('1 if updated with grade performance. O if not');

        });
    }

    public function down(): void
    {
        Schema::table('primary_learner_performances', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
