<?php

namespace Modules\TeacherAccreditation\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Core\Models\User;
use Modules\TeacherAccreditation\Models\SettingMaritalStatus;

class TeacherAccreditationApplication extends Model
{
    protected $table = 'teacher_accreditation_applications';

    protected $fillable = [
        'nin',
        'email',
        'phone',
        'surname',
        'firstname',
        'other_names',
        'gender',
        'marital_status_id',
        'date_of_birth',
        'approval_status',
        'approved_by',
        'date_approved',
        'reject_reason',
        'application_number',
        'qualification_id',
    ];



    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function approverPerson()
    {
        return $this->belongsTo(\Modules\Core\Models\Person::class, 'approved_by', 'id');
    }

    public function qualification()
    {
        return $this->belongsTo(\Modules\TeacherAccreditation\Models\SettingTeacherAccreditationQualification::class, 'qualification_id');
    }

    public function maritalStatus()
    {
        return $this->belongsTo(SettingMaritalStatus::class, 'marital_status_id');
    }
    public function getTeacherRegistrationNumberAttribute()
    {
        return 'TR-' . preg_replace('/[^0-9]/', '', $this->application_number);
    }
}
