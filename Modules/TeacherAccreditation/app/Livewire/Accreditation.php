<?php

namespace Modules\TeacherAccreditation\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Modules\TeacherAccreditation\Models\SettingTeacherAccreditationQualification;
use Mo<PERSON>les\TeacherAccreditation\Models\TeacherAccreditationApplication;
use Modules\TeacherAccreditation\Models\TeacherAccreditationDocument;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Modules\TeacherAccreditation\Notifications\ApplicationSubmitted;

class Accreditation extends Component
{
    use WithFileUploads;
    protected $listeners = ['submitConfirmed' => 'submitApplication'];
    // Step control
    public $step = 1;

    // Step 1: Personal Info
    public $nin;
    public $surname;
    public $other_names;
    public $firstname;
    public $gender;
    public $marital_status_id;
    public $date_of_birth;

    // Step 2: Contact Info
    public $email;
    public $phone;

    // Step 3: Documents
    public $qualification_id;
    public $qualifications = [];
    public $requiredDocuments = [];
    public $optionalDocuments = [];
    public $uploads = [];
    public $selectedAlternative = '';
    public $documentMap = [];
    public $documentMapData = [
        // 1: Grade III Teacher
        1 => [
            'required' => [
                'Grade III Pass Slip',
                'Grade III Certificate',
                'National ID',
            ],
            'alternatives' => [
                'O-Level Pass Slip' => 'O-Level Certificate',
                'Grade II Certificate' => null,
            ],
            'where_applicable' => []
        ],
        // 2: Grade V Teacher (Primary)
        2 => [
            'required' => [
                'O-Level Pass Slip',
                'O-Level Certificate',
                'Grade III Pass Slip',
                'Grade III Certificate',
                'Grade V Transcript',
                'Grade V Certificate',
                'National ID',
            ],
            'alternatives' => [],
            'where_applicable' => [
                'Grade III Registration Certificate'
            ]
        ],
        // 3: Graduate Teacher (Primary)
        3 => [
            'required' => [
                'O-Level Pass Slip',
                'O-Level Certificate',
                'Grade III Pass Slip',
                'Grade III Certificate',
                'Grade V Transcript',
                'Grade V Certificate',
                'Degree Transcript',
                'Degree Certificate',
                'National ID',
            ],
            'alternatives' => [],
            'where_applicable' => [
                'Grade III Registration Certificate',
                'Grade V Registration Certificate'
            ]
        ],
        // 4: Grade V Teacher (Secondary)
        4 => [
            'required' => [
                'O-Level Pass Slip',
                'O-Level Certificate',
                'A-Level Pass Slip',
                'A-Level Certificate',
                'Grade V Transcript',
                'Grade V Certificate',
                'National ID',
            ],
            'alternatives' => [],
            'where_applicable' => []
        ],
        // 5: Graduate Teacher (Secondary)
        5 => [
            'required' => [
                'O-Level Pass Slip',
                'O-Level Certificate',
                'A-Level Pass Slip',
                'A-Level Certificate',
                'Grade V Transcript',
                'Grade V Certificate',
                'Degree Transcript',
                'Degree Certificate',
                'National ID',
            ],
            'alternatives' => [],
            'where_applicable' => [
                'Grade V Registration Certificate'
            ]
        ],
        // 6: Instructor (Grade V)
        6 => [
            'required' => [
                'A-Level Pass Slip',
                'A-Level Certificate',
                'Certificate in Technical Teacher Education',
                'Diploma in Vocational Training Instruction',
                'National ID',
            ],
            'alternatives' => [
                'O-Level Pass Slip' => 'O-Level Certificate',
                'Junior Certificate' => null,
            ],
            'where_applicable' => [
                'Diploma in Technical and Vocational Studies',
                'Craft II Certificate',
                'Craft III Certificate',
                'National Certificate'
            ]
        ],
        // 7: Instructor (Graduate)
        7 => [
            'required' => [
                'A-Level Pass Slip',
                'A-Level Certificate',
                'Degree Certificate',
                'Degree Transcript',
                'National ID',
            ],
            'alternatives' => [
                'O-Level Pass Slip' => 'O-Level Certificate',
                'Junior Certificate' => null,
            ],
            'where_applicable' => [
                'Craft II Certificate',
                'Craft III Certificate',
                'National Certificate',
                'Diploma Certificate',
                'Diploma Transcript',
                'Diploma in Instructor and Technical Education (DITTE)',
                'DITTE Transcript'
            ]
        ],
        // 8: Health Tutor Diploma (Grade V)
        8 => [
            'required' => [
                'O-Level Certificate',
                'Certificate of Registration for Professional Practice',
                'Health Tutor Diploma',
                'Health Tutor Certificate',
                'Health Tutor Transcript',
                'National ID',
            ],
            'alternatives' => [],
            'where_applicable' => [
                'Professional Certificate at Certificate Level',
                'Professional Certificate at Diploma Level'
            ]
        ],
        // 9: Health Tutor Degree (Graduate)
        9 => [
            'required' => [
                'O-Level Certificate',
                'A-Level Certificate',
                'Certificate of Registration for Professional Practice',
                'Degree Certificate of Health Tutorship',
                'Degree Transcript of Health Tutorship',
                'National ID',
            ],
            'alternatives' => [],
            'where_applicable' => [
                'Professional Certificate at Certificate Level',
                'Professional Certificate at Diploma Level'
            ]
        ],
    ];


    public function updatedQualificationId()
    {
        if (isset($this->documentMapData[$this->qualification_id])) {
            $this->documentMap = $this->documentMapData[$this->qualification_id];
            $this->requiredDocuments = $this->documentMap['required'] ?? [];
            $this->optionalDocuments = $this->documentMap['where_applicable'] ?? [];
        } else {
            $this->documentMap = [];
            $this->requiredDocuments = [];
            $this->optionalDocuments = [];
        }
        $this->uploads = [];
        $this->selectedAlternative = '';
    }

    public function updatedSelectedAlternative()
    {
        // Only clear alternative uploads, keep required and optional documents
        $requiredCount = count($this->requiredDocuments);

        // Store required documents temporarily
        $requiredUploads = [];
        for ($i = 0; $i < $requiredCount; $i++) {
            if (isset($this->uploads[$i])) {
                $requiredUploads[$i] = $this->uploads[$i];
            }
        }

        // Store optional documents temporarily
        $optionalUploads = [];
        foreach ($this->optionalDocuments as $index => $docType) {
            $uploadIndex = $this->getOptionalDocumentIndex($index);
            if (isset($this->uploads[$uploadIndex])) {
                $optionalUploads[$uploadIndex] = $this->uploads[$uploadIndex];
            }
        }

        // Clear all uploads
        $this->uploads = [];

        // Restore required documents
        foreach ($requiredUploads as $index => $upload) {
            $this->uploads[$index] = $upload;
        }

        // Restore optional documents
        foreach ($optionalUploads as $index => $upload) {
            $this->uploads[$index] = $upload;
        }

        // // Debug: Log what was preserved
        // Log::info('Alternative selection changed - preserved documents', [
        //     'required_preserved' => count($requiredUploads),
        //     'optional_preserved' => count($optionalUploads),
        //     'total_uploads_after' => count($this->uploads),
        //     'selected_alternative' => $this->selectedAlternative
        // ]);
    }

    // Helper method to get the correct index for alternative documents
    public function getAlternativeIndex($mainDoc)
    {
        if (!isset($this->documentMap['alternatives'])) {
            return null;
        }

        $alternativeIndex = count($this->requiredDocuments);

        foreach ($this->documentMap['alternatives'] as $doc => $secondaryDoc) {
            if ($doc === $mainDoc) {
                return $alternativeIndex;
            }

            if ($secondaryDoc) {
                $alternativeIndex += 2;
            } else {
                $alternativeIndex += 1;
            }
        }

        return null;
    }

    // Helper method to check if required documents are complete
    public function hasRequiredDocuments()
    {
        foreach ($this->requiredDocuments as $index => $docType) {
            if (!isset($this->uploads[$index]) || !is_object($this->uploads[$index])) {
                return false;
            }
        }
        return true;
    }

    // Helper method to get the correct index for optional documents
    public function getOptionalDocumentIndex($optionalIndex)
    {
        $baseIndex = count($this->requiredDocuments);

        // Add offset for alternative documents
        if (isset($this->documentMap['alternatives']) && !empty($this->documentMap['alternatives'])) {
            foreach ($this->documentMap['alternatives'] as $mainDoc => $secondaryDoc) {
                if ($secondaryDoc) {
                    $baseIndex += 2; // Two documents per alternative
                } else {
                    $baseIndex += 1; // One document per alternative
                }
            }
        }

        return $baseIndex + $optionalIndex;
    }

    // Debug method to check upload status
    public function debugUploads()
    {
        $debug = [
            'required_count' => count($this->requiredDocuments),
            'uploads_count' => count($this->uploads),
            'required_documents' => $this->requiredDocuments,
            'upload_keys' => array_keys($this->uploads),
            'selected_alternative' => $this->selectedAlternative,
        ];

        return $debug;
    }
    // Step 4: Review
    public $application_number;

    // For select options
    public $maritalStatuses = [
        ['id' => 1, 'name' => 'SINGLE'],
        ['id' => 2, 'name' => 'MARRIED'],
        ['id' => 3, 'name' => 'DIVORCED'],
        ['id' => 4, 'name' => 'WIDOWED'],
    ];

    public $genders = [
        'M' => 'MALE',
        'F' => 'FEMALE',
        'O' => 'OTHER',
    ];

    public function mount()
    {
        // Load qualifications from DB
        $this->qualifications = SettingTeacherAccreditationQualification::orderBy('name')->get(['id', 'name'])->toArray();
    }

    // Validation rules per step
    protected function rules()
    {
        // Step 1 rules
        $rulesStep1 = [
            'nin' => 'required|string|min:13|max:14',
            'surname' => 'required|string',
            'firstname' => 'required|string',
            'gender' => 'required|in:M,F,O',
            'marital_status_id' => 'required|integer',
            'date_of_birth' => 'required|date',
        ];

        // Step 2 rules
        $rulesStep2 = [
            'email' => [
                'required',
                'email',
                'regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/'
            ],
            'phone' => [
                'required',
                'string',
                'min:10',
                'max:15',
                'regex:/^\+[1-9]\d{1,14}$/',
            ],
        ];

        // Step 3 rules
        if ($this->step === 3) {
            $rules = [
                'qualification_id' => 'required|integer',
            ];

            // Validate required documents
            foreach ($this->requiredDocuments as $index => $doc) {
                $rules["uploads.$index"] = 'required|file|mimes:pdf,jpg,jpeg,png,gif,doc,docx,xls,xlsx,txt,rtf,odt,ods|max:10240';
            }

            return $rules;
        }

        // Return rules based on current step
        if ($this->step === 1) return $rulesStep1;
        if ($this->step === 2) return $rulesStep2;

        // Final submission (step 4): validate everything
        $rulesStep3 = [
            'qualification_id' => 'required|integer',
        ];

        // Validate required documents
        foreach ($this->requiredDocuments as $index => $doc) {
            $rulesStep3["uploads.$index"] = 'required|file|mimes:pdf,jpg,jpeg,png,gif,doc,docx,xls,xlsx,txt,rtf,odt,ods|max:10240';
        }

        // Validate either/or scenarios
        $this->validateEitherOrDocuments($rulesStep3);

        return array_merge($rulesStep1, $rulesStep2, $rulesStep3);
    }

    // Custom validation for either/or document scenarios
    protected function validateEitherOrDocuments()
    {
        // Only validate alternatives if they exist for this qualification
        if (!isset($this->documentMap['alternatives']) || empty($this->documentMap['alternatives'])) {
            return;
        }

        // Check if user has selected an alternative option
        if (empty($this->selectedAlternative)) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                'Please select one alternative document option.'
            );
        }

        // Validate that the selected alternative has complete files uploaded
        $alternativeIndex = count($this->requiredDocuments);
        $alternativeIndexOffset = 0;
        $hasSelectedAlternative = false;

        foreach ($this->documentMap['alternatives'] as $mainDoc => $secondaryDoc) {
            // Check if this is the selected alternative
            if ($this->selectedAlternative === $mainDoc) {
                $hasMain = isset($this->uploads[$alternativeIndex + $alternativeIndexOffset]) && is_object($this->uploads[$alternativeIndex + $alternativeIndexOffset]);

                if ($secondaryDoc) {
                    $hasSecondary = isset($this->uploads[$alternativeIndex + $alternativeIndexOffset + 1]) && is_object($this->uploads[$alternativeIndex + $alternativeIndexOffset + 1]);
                    $hasComplete = $hasMain && $hasSecondary;
                    if ($hasComplete) {
                        $hasSelectedAlternative = true;
                        break;
                    }
                } else {
                    if ($hasMain) {
                        $hasSelectedAlternative = true;
                        break;
                    }
                }
            }

            // Update offset for next iteration
            if ($secondaryDoc) {
                $alternativeIndexOffset += 2;
            } else {
                $alternativeIndexOffset += 1;
            }
        }

        if (!$hasSelectedAlternative) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                'Please upload all required documents for your selected alternative option.'
            );
        }
    }

    // Step navigation
    public function nextStep()
    {
        $this->validate($this->rules(), $this->messages(), $this->attributes());

        // Additional validation for alternatives if on step 3
        if ($this->step === 3) {
            $this->validateEitherOrDocuments();
        }

        $this->step++;
    }

    public function prevStep()
    {
        if ($this->step > 1) {
            $this->step--;
        }
    }

    // Add this method to your Accreditation class
    protected function messages()
    {
        return [
            // Step 1: Personal Info
            'nin.required' => 'The National Identification Number (NIN) is required.',
            'nin.min' => 'The NIN must be at least 13 characters.',
            'nin.max' => 'The NIN may not be greater than 14 characters.',
            'surname.required' => 'Surname is required.',
            'surname.string' => 'Surname must be a valid string.',
            'firstname.required' => 'First name is required.',
            'firstname.string' => 'First name must be a valid string.',
            'gender.required' => 'Please select your gender.',
            'gender.in' => 'Please select a valid gender.',
            'marital_status_id.required' => 'Please select your marital status.',
            'marital_status_id.integer' => 'Invalid marital status selection.',
            'date_of_birth.required' => 'Date of birth is required.',
            'date_of_birth.date' => 'Please enter a valid date of birth.',

            // Step 2: Contact Info
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.regex' => 'Please enter a valid email address format.',
            'phone.required' => 'Phone number is required.',
            'phone.string' => 'Phone number must be a valid string.',
            'phone.min' => 'Phone number must be at least 10 characters.',
            'phone.max' => 'Phone number may not be greater than 15 characters.',
            'phone.regex' => 'Please enter a valid phone number with country code (e.g., +256712345678).',

            // Step 3: Documents
            'qualification_id.required' => 'Please select the qualification you are applying for.',
            'qualification_id.integer' => 'Invalid qualification selection.',
            // Add similar messages for document uploads if needed
            'uploads.*.required' => ':attribute is required.',
            'uploads.*.file' => ':attribute must be a file.',
            'uploads.*.mimes' => ':attribute must be a PDF, image, or document file.',
            'uploads.*.max' => ':attribute must not be larger than 10MB.',
        ];
    }

    public function attributes()
    {
        $attributes = [];
        foreach ($this->requiredDocuments as $index => $docType) {
            $attributes["uploads.$index"] = $docType;
        }
        // Optionally, add for alternatives/optionals if you want
        return $attributes;
    }

    public function confirmPopup()
    {
        $this->dispatch('confirmSubmission');
    }
    public function submitApplication()
    {
        // Prevent duplicate applications for the same NIN and qualification
        $exists = TeacherAccreditationApplication::where('nin', $this->nin)
            ->where('qualification_id', $this->qualification_id)
            ->exists();
        if ($exists) {
            session()->flash('error', 'You have already submitted an application for this qualification.');
            return;
        }

        $this->validate($this->rules(), $this->messages(), $this->attributes());

        // Simulate application number generation
        $this->application_number = 'TA-' . strtoupper(substr($this->nin, -4)) . '-' . now()->format('YmdHis');

        \DB::beginTransaction();
        try {
            $genderMap = [
                'M' => 'Male',
                'F' => 'Female',
                'O' => 'Other',
            ];

            // Save the application
            $application = \Modules\TeacherAccreditation\Models\TeacherAccreditationApplication::create([
                'nin' => $this->nin,
                'email' => $this->email,
                'phone' => $this->phone,
                'surname' => strtoupper($this->surname),
                'other_names' => strtoupper($this->other_names),
                'firstname' => strtoupper($this->firstname),
                'qualification_id' => $this->qualification_id,
                'gender' => $genderMap[$this->gender] ?? $this->gender,
                'marital_status_id' => $this->marital_status_id,
                'date_of_birth' => $this->date_of_birth,
                'approval_status' => 'PENDING',
                'application_number' => $this->application_number,
            ]);

            // Save required documents
            foreach ($this->requiredDocuments as $index => $docType) {
                $file = $this->uploads[$index] ?? null;
                if (is_object($file)) {
                    $path = $file->store('teacher_accreditation_documents', 'public');
                    TeacherAccreditationDocument::create([
                        'teacher_accreditation_application_id' => $application->id,
                        'document_type' => $docType,
                        'document_url' => $path,
                    ]);
                }
            }

            // Save alternative documents if uploaded
            if (isset($this->documentMap['alternatives']) && !empty($this->documentMap['alternatives'])) {
                $alternativeIndex = count($this->requiredDocuments);
                $alternativeIndexOffset = 0;
                $selectedAlternativeIndex = 0;

                foreach ($this->documentMap['alternatives'] as $mainDoc => $secondaryDoc) {
                    // Check if this is the selected alternative option
                    $isSelected = $this->selectedAlternative === $mainDoc;

                    if ($isSelected) {
                        // Check if this alternative option has files uploaded
                        $hasMain = isset($this->uploads[$alternativeIndex + $alternativeIndexOffset]) && is_object($this->uploads[$alternativeIndex + $alternativeIndexOffset]);

                        if ($secondaryDoc) {
                            $hasSecondary = isset($this->uploads[$alternativeIndex + $alternativeIndexOffset + 1]) && is_object($this->uploads[$alternativeIndex + $alternativeIndexOffset + 1]);

                            // Save main document if uploaded
                            if ($hasMain) {
                                $path = $this->uploads[$alternativeIndex + $alternativeIndexOffset]->store('teacher_accreditation_documents', 'public');
                                TeacherAccreditationDocument::create([
                                    'teacher_accreditation_application_id' => $application->id,
                                    'document_type' => $mainDoc,
                                    'document_url' => $path,
                                ]);
                            }

                            // Save secondary document if uploaded
                            if ($hasSecondary) {
                                $path = $this->uploads[$alternativeIndex + $alternativeIndexOffset + 1]->store('teacher_accreditation_documents', 'public');
                                TeacherAccreditationDocument::create([
                                    'teacher_accreditation_application_id' => $application->id,
                                    'document_type' => $secondaryDoc,
                                    'document_url' => $path,
                                ]);
                            }
                        } else {
                            // Save single document if uploaded
                            if ($hasMain) {
                                $path = $this->uploads[$alternativeIndex + $alternativeIndexOffset]->store('teacher_accreditation_documents', 'public');
                                TeacherAccreditationDocument::create([
                                    'teacher_accreditation_application_id' => $application->id,
                                    'document_type' => $mainDoc,
                                    'document_url' => $path,
                                ]);
                            }
                        }
                    }

                    // Update offset for next iteration
                    if ($secondaryDoc) {
                        $alternativeIndexOffset += 2;
                    } else {
                        $alternativeIndexOffset += 1;
                    }
                }
            }

            // Save "where applicable" documents if uploaded
            if (!empty($this->optionalDocuments)) {
                foreach ($this->optionalDocuments as $index => $docType) {
                    $uploadIndex = $this->getOptionalDocumentIndex($index);
                    if (isset($this->uploads[$uploadIndex]) && is_object($this->uploads[$uploadIndex])) {
                        $path = $this->uploads[$uploadIndex]->store('teacher_accreditation_documents', 'public');
                        TeacherAccreditationDocument::create([
                            'teacher_accreditation_application_id' => $application->id,
                            'document_type' => $docType,
                            'document_url' => $path,
                        ]);
                    }
                }
            }

            DB::commit();

            // Debug: Log what documents were saved
            $savedDocuments = TeacherAccreditationDocument::where('teacher_accreditation_application_id', $application->id)->get();
            // Log::info('Saved documents for application ' . $application->id, [
            //     'total_documents' => $savedDocuments->count(),
            //     'documents' => $savedDocuments->pluck('document_type')->toArray(),
            //     'required_documents' => $this->requiredDocuments,
            //     'optional_documents' => $this->optionalDocuments,
            //     'selected_alternative' => $this->selectedAlternative,
            //     'uploads_count' => count($this->uploads),
            //     'upload_keys' => array_keys($this->uploads)
            // ]);

            Notification::route('mail', $this->email)
                ->notify(new ApplicationSubmitted($this->application_number));
            session()->flash('success', 'Application submitted successfully! Check your email for confirmation.');
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'There was an error submitting your application. Please try again.');
            throw $e;
        }
    }

    public function render()
    {
        return view('teacheraccreditation::livewire.accreditation', [
            'requiredDocuments' => $this->requiredDocuments,
        ])
            ->layout('core::layouts.design');
    }
}
