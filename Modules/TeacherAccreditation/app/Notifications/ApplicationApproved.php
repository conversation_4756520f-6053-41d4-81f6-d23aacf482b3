<?php

namespace Modules\TeacherAccreditation\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ApplicationApproved extends Notification
{
    use Queueable;

    public $applicationNumber;
    public $teacherRegNumber;

    public function __construct($applicationNumber, $teacherRegNumber)
    {
        $this->applicationNumber = $applicationNumber;
        $this->teacherRegNumber = $teacherRegNumber;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Teacher Accreditation Application Approved 🎉')
            ->markdown('teacheraccreditation::emails.application-approved', [
                'applicationNumber' => $this->applicationNumber,
                'teacherRegNumber' => $this->teacherRegNumber
            ]);
    }

    public function toArray($notifiable)
    {
        return [];
    }
}
