@section('title', 'Teacher Accreditation Requests')
<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">

                <!-- Page Header -->
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between align-items-center">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Teacher Accreditation Requests</h3>
                            <nav class="nk-block-des">
                                <ul class="breadcrumb breadcrumb-arrow">
                                    <li class="breadcrumb-item">
                                        <a href="{{ url('/') }}" class="text-primary">Dashboard</a>
                                    </li>
                                    <li class="breadcrumb-item active">
                                        New Accreditation Requests
                                    </li>
                                </ul>
                            </nav>
                            <div class="nk-block-des text-soft">
                                <p>You have a total of <strong>{{ $TotalRequests }}</strong> new teacher accreditation
                                    requests.</p>
                            </div>
                        </div>
                    </div>
                </div>

                @if ($message)
                    <div class="alert alert-success">{{ $message }}</div>
                @endif


                <!-- Filter Form -->
                <div class="nk-block">
                    <div class="card card-stretch card-bordered border-dark-teal">
                        <div class="card-inner-group">
                            <div class="card-inner border-bottom">
                                <div class="row g-3 align-items-end">
                                    <div class="col-md-2">
                                        <div class="form-control-wrap">
                                            <select wire:model.lazy="filter.qualification"
                                                class="form-control border-dark-teal"
                                                style="height: 40px; padding-top: 0.5rem; padding-bottom: 0.5rem; font-size: 14px;"
                                                id="filterQualification">
                                                <option value="" selected>All</option>
                                                @foreach ($availableQualifications as $qualification)
                                                    @php
                                                        $maxLength = 17;
                                                        $truncated =
                                                            strlen($qualification) > $maxLength
                                                                ? mb_substr($qualification, 0, $maxLength) . '...'
                                                                : $qualification;
                                                    @endphp
                                                    <option value="{{ $qualification }}">{{ $truncated }}</option>
                                                @endforeach
                                            </select>
                                            <label class="form-label-outlined emis-label"
                                                for="filterQualification">QUALIFICATION
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-control-wrap">
                                            <input type="text" id="filterFullName"
                                                class="form-control border-dark-teal" wire:model.lazy="filter.name"
                                                placeholder="SEARCH FULL NAME"
                                                style="font-size: 12px; border-radius:5px;">
                                            <label class="form-label-outlined emis-label" for="filterFullName">FULL NAME
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-control-wrap">
                                            <input type="text" id="filterNin" class="form-control border-dark-teal"
                                                wire:model.lazy="filter.nin" placeholder="SEARCH NIN"
                                                style="font-size: 12px; border-radius:5px;">
                                            <label class="form-label-outlined emis-label" for="filterNin">NIN
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="form-control-wrap">
                                            <input type="text" id="filterApplicationNumber"
                                                class="form-control border-dark-teal"
                                                wire:model.lazy="filter.application_number"
                                                placeholder="SEARCH APPLICATION NUMBER"
                                                style="font-size: 12px; border-radius:5px;">
                                            <label class="form-label-outlined emis-label"
                                                for="filterApplicationNumber">APPLICATION NUMBER
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="d-flex w-80 h-100 gap-0">
                                            @if ($this->hasFilters)
                                                <button type="button" wire:click="resetFilters"
                                                    class="btn btn-outline-secondary d-flex align-items-center justify-content-center gap-1 rounded-right-0 bg-secondary"
                                                    style="height: 43px; border-top-right-radius: 0; border-bottom-right-radius: 0;">
                                                    <em class="icon ni ni-cross text-white"></em>
                                                </button>
                                            @endif

                                            <button type="submit"
                                                class="btn btn-outline-dark-teal d-flex align-items-center justify-content-center gap-1 bg-dark-teal
                                            @if ($this->hasFilters) rounded-left-0 @endif"
                                                style="height: 43px; @if ($this->hasFilters) border-top-left-radius: 0; border-bottom-left-radius: 0; @endif">
                                                <em class="icon ni ni-filter text-white"></em>
                                                <span class="text-white">Apply</span>
                                            </button>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <!-- Table -->
                            <div class="card-inner position-relative card-tools-toggle">
                                @if ($requests->count())
                                    <div class="nk-tb-list nk-tb-ulist is-compact">

                                        <div class="nk-tb-item nk-tb-head bg-secondary">
                                            <div class="nk-tb-col"><span class="sub-text text-white">FULL NAME</span>
                                            </div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">SEX</span>
                                            </div>
                                            <div class="nk-tb-col"><span
                                                    class="sub-text text-white">QUALIFICATION</span></div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">DATE CREATED</span>
                                            </div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">STATUS</span></div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">ACTIONS</span>
                                            </div>
                                        </div>


                                        @foreach ($requests as $request)
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col text-dark">
                                                    <span>{{ $request['surname'] }} {{ $request['other_names'] }}</span>
                                                </div>
                                                <div class="nk-tb-col text-dark">
                                                    <span>{{ $request['gender'] }} </span>
                                                </div>
                                                <div class="nk-tb-col text-dark text-uppercase">
                                                    <span>{{ $request['qualification'] }}</span>
                                                </div>
                                                <div class="nk-tb-col text-dark">
                                                    <span>{{ $request['created_at'] }}</span>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <span style="background: #272829; color: white;"
                                                        class="text-uppercase badge
                            @if ($request['status'] === 'pending') badge-secondary
                            @elseif($request['status'] === 'recommended') badge-primary
                            @elseif($request['status'] === 'approved') badge-dark-teal
                            @elseif($request['status'] === 'rejected') badge-red
                            @elseif($request['status'] === 'endorsed') badge-amaranth
                            @else badge-secondary @endif">
                                                        {{ ucfirst($request['status']) }}
                                                    </span>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <a href="#"
                                                        wire:click.prevent="showDetails({{ $request['id'] }})"
                                                        class="badge badge-dark-teal cursor-pointer">
                                                        <em class="icon ni ni-eye"></em> <span>View</span>
                                                    </a>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="nk-tb-list nk-tb-ulist is-compact">
                                        <div class="nk-tb-item nk-tb-head bg-secondary">

                                            <div class="nk-tb-col"><span class="sub-text text-white">FULL NAME</span>
                                            </div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">SEX</span></div>
                                            <div class="nk-tb-col"><span
                                                    class="sub-text text-white">QUALIFICATION</span></div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">DATE
                                                    CREATED</span></div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">STATUS</span>
                                            </div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">ACTIONS</span>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="p-4">
                                        <div class="alert alert-secondary alert-icon m-0">
                                            <em class="icon ni ni-alert-circle"></em>
                                            No teacher accreditation requests to display...
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="mt-3 mb-3">
                                {{ $this->filteredRequests->links() }}
                            </div>


                        </div>
                    </div>
                </div>

                <!-- Modal -->
                @if ($selectedRequest)
                    <div class="modal fade show d-block" tabindex="-1" style="background:rgba(0,0,0,0.5);"
                        wire:key="modal-{{ $selectedRequest['id'] }}">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header bg-primary text-white">
                                    <h5 class="modal-title"><em class="icon ni ni-user-check me-1"></em>Application
                                        Number:
                                        <span style="font-weight: 900">{{ $selectedRequest['application_number'] }}
                                        </span>
                                    </h5>
                                    <span
                                        class="fw-light small">{{ \Carbon\Carbon::parse($selectedRequest['created_at'])->diffForHumans() }}
                                    </span>
                                    <button type="button"
                                        wire:click="$set('selectedRequest', null)" aria-label="Close"></button>
                                </div>
                                <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <p class="mb-1"><em
                                                    class="icon ni ni-hash me-1 text-primary"></em><strong>NIN:</strong>
                                                <span class="text-dark">{{ $selectedRequest['nin'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-user me-1 text-primary"></em><strong>Full
                                                    Name:</strong> <span
                                                    class="text-dark">{{ $selectedRequest['surname'] }}
                                                    {{ $selectedRequest['firstname'] }}
                                                    {{ $selectedRequest['other_names'] }}</span></p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-mail me-1 text-primary"></em><strong>Email:</strong>
                                                <span class="text-dark">{{ $selectedRequest['email'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-call me-1 text-primary"></em><strong>Phone:</strong>
                                                <span class="text-dark">{{ $selectedRequest['phone'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-award me-1 text-primary"></em><strong>Qualification:</strong>
                                                <span class="text-dark">{{ $selectedRequest['qualification'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-user me-1 text-primary"></em><strong>Sex:</strong>
                                                <span class="text-dark">{{ $selectedRequest['gender'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="ni ni-unlink me-1 text-primary"></em><strong>Marital
                                                    Status:</strong> <span
                                                    class="text-dark">{{ $selectedRequest['marital_status'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-calendar me-1 text-primary"></em><strong>Date of
                                                    Birth:</strong> <span
                                                    class="text-dark">{{ $selectedRequest['date_of_birth'] }}</span>
                                            </p>
                                        </div>
                                        <div class="col-md-6">

                                            <div class="mb-1">
                                                <em
                                                    class="icon ni ni-archive me-1 text-primary"></em><strong>Documents:</strong>
                                                <table class="table table-sm table-borderless mb-0"
                                                    style="min-width: 320px;">
                                                    <tbody>
                                                        @foreach ($selectedRequest['documents'] as $document)
                                                            <tr>
                                                                <td style="vertical-align: middle; width: 1.5rem;">
                                                                    <em class="icon ni ni-file-text text-info"></em>
                                                                </td>
                                                                <td style="vertical-align: middle;">
                                                                    <span class="text-truncate"
                                                                        style="max-width: 180px; display: inline-block;"
                                                                        title="{{ $document['type'] }}">{{ $document['type'] }}</span>
                                                                </td>
                                                                <td
                                                                    style="vertical-align: middle; width: 2.5rem; text-align: right;">
                                                                    <a href="{{ $document['url'] }}" target="_blank"
                                                                        class="btn btn-sm btn-light-primary px-2 py-0"
                                                                        title="View Document">
                                                                        <em class="icon ni ni-eye"></em>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <label for="comment" class="form-label"><em
                                                    class="icon ni ni-info me-1 text-primary"></em><strong>Comment/Justification
                                                    <span class="text-danger">*</span></strong></label>
                                            <textarea id="comment" class="form-control border-primary" rows="3" wire:model.defer="comment"
                                                placeholder="Enter comment or justification (required for rejection)"></textarea>
                                        </div>
                                    </div>
                                </div>

                                @if (session()->has('error'))
                                    <div class="alert alert-danger">{{ session('error') }}</div>
                                @endif

                                <div class="modal-footer bg-light">
                                    <button type="button" class="btn btn-secondary"
                                        wire:click="$set('selectedRequest', null)"><em
                                            class="icon ni ni-cross me-1"></em>Close</button>
                                    <button type="button" class="btn btn-primary"
                                        wire:click="approve({{ $selectedRequest['id'] }})">
                                        <em class="icon ni ni-check-circle me-1"></em> Approve
                                    </button>
                                    <button type="button" class="btn btn-danger"
                                        wire:click="reject({{ $selectedRequest['id'] }})">
                                        <em class="icon ni ni-cross-circle me-1"></em> Reject
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

            </div>
        </div>
    </div>
</div>
