<div class="nk-content nk-content-lg nk-content-fluid">
    <div class="container-xl wide-lg">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="kyc-app wide-md m-auto">
                    <div class="nk-block-head nk-block-head-lg wide-xs mx-auto">
                        <div class="nk-block-head-content text-center">
                            <h2 class="nk-block-title fw-normal"
                                style="max-width: 520px; margin: 0 auto; word-break: keep-all; white-space: normal; font-size: 2.1rem; line-height: 1.2;">
                                EMIS TEACHER ACCREDITATION APPLICATION</h2>
                            <div class="nk-block-des">
                                <p>Teachers are required to submit their information for verification.</p>
                            </div>
                        </div>
                    </div>
                    <div class="nk-block">
                        <div class="card card-bordered border-dark-teal">
                            <div class="py-2 px-4 d-flex flex-row flex-wrap border-bottom">
                                <button type="button"
                                    class="flex-fill rounded-0 btn border-top-0 border-left-0 border-right-0 shadow-none {{ $step === 1 ? 'text-primary border-primary' : '' }}"
                                    @if ($step !== 1) disabled @endif>
                                    Step 1: PERSONAL INFO
                                </button>
                                <button type="button"
                                    class="flex-fill rounded-0 btn border-top-0 border-left-0 border-right-0 shadow-none {{ $step === 2 ? 'text-primary border-primary' : '' }}"
                                    @if ($step < 2) disabled @endif>
                                    Step 2: CONTACT INFO
                                </button>
                                <button type="button"
                                    class="flex-fill rounded-0 btn border-top-0 border-left-0 border-right-0 shadow-none {{ $step === 3 ? 'text-primary border-primary' : '' }}"
                                    @if ($step < 3) disabled @endif>
                                    Step 3: DOCUMENTS
                                </button>
                                <button type="button"
                                    class="flex-fill rounded-0 btn border-top-0 border-left-0 border-right-0 shadow-none {{ $step === 4 ? 'text-primary border-primary' : '' }}"
                                    @if ($step < 4) disabled @endif>
                                    Step 4: REVIEW
                                </button>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-primary" role="progressbar"
                                    style="width: {{ ($step - 1) * 33.33 }}%"></div>
                            </div>
                            <div class="nk-kycfm p-4">
                                {{-- Step 1: Personal Info --}}
                                @if ($step === 1)
                                    <div class="nk-kycfm-note mb-3">
                                        <em class="icon ni ni-info-fill"></em>
                                        <p>Please check the details to see if they match your information.</p>
                                    </div>
                                    <form wire:submit.prevent="nextStep">
                                        <div class="row g-3 align-center">
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="nin">NIN <span
                                                            class="text-danger">*</span></label>
                                                    <span class="form-note">Enter your National Identification Number
                                                        (NIN).</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-7">
                                                <div class="form-group">
                                                    <input type="text" id="nin"
                                                        class="form-control bg-primary-dim text-uppercase"
                                                        wire:model.defer="nin" placeholder="eg. CM001122334455"
                                                        maxlength="14" required>
                                                    @error('nin')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="surname">Surname <span
                                                            class="text-danger">*</span></label>
                                                    <span class="form-note">Enter your surname as it appears on your
                                                        ID.</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-7">
                                                <div class="form-group">
                                                    <input type="text" id="surname"
                                                        class="form-control bg-primary-dim text-uppercase"
                                                        wire:model.defer="surname" placeholder="ENTER SURNAME" required>
                                                    @error('surname')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="firstname">First Name <span
                                                            class="text-danger">*</span></label>
                                                    <span class="form-note">Enter your firstname as it appears on your
                                                        ID.</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-7">
                                                <div class="form-group">
                                                    <input type="text" id="firstname"
                                                        class="form-control bg-primary-dim text-uppercase"
                                                        wire:model.defer="firstname" placeholder="ENTER FIRST NAME"
                                                        required>
                                                    @error('firstname')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="other_names">Other Names</label>
                                                    <span class="form-note">Enter any other names you have.</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-7">
                                                <div class="form-group">
                                                    <input type="text" id="other_names"
                                                        class="form-control bg-primary-dim text-uppercase"
                                                        wire:model.defer="other_names" placeholder="ENTER OTHER NAMES">
                                                    @error('other_names')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="gender">Sex <span
                                                            class="text-danger">*</span></label>
                                                    <span class="form-note">Select your sex.</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-7">
                                                <div class="form-group">
                                                    <select id="gender" class="form-control bg-primary-dim"
                                                        wire:model.defer="gender" required>
                                                        <option value="">Select Sex</option>
                                                        @foreach ($genders as $key => $label)
                                                            <option value="{{ $key }}">{{ $label }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('gender')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="date_of_birth">Date of Birth <span
                                                            class="text-danger">*</span></label>
                                                    <span class="form-note">Enter your date of birth.</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-7">
                                                <div class="form-group">
                                                    <input type="date" id="date_of_birth"
                                                        class="form-control bg-primary-dim"
                                                        wire:model.defer="date_of_birth" required>
                                                    @error('date_of_birth')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="marital_status_id">Marital Status
                                                        <span class="text-danger">*</span></label>
                                                    <span class="form-note">Select your marital status.</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-7">
                                                <div class="form-group">
                                                    <select id="marital_status_id" class="form-control bg-primary-dim"
                                                        wire:model.defer="marital_status_id" required>
                                                        <option value="">Select Marital Status</option>
                                                        @foreach ($maritalStatuses as $status)
                                                            <option value="{{ $status['id'] }}">{{ $status['name'] }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('marital_status_id')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="nk-kycfm-action pt-5 d-flex flex-row justify-content-end">
                                            <button type="submit" class="btn btn-primary d-flex">
                                                <span class="align-self-center">Next</span>
                                                <em class="ni ni-arrow-right ml-2"></em>
                                            </button>
                                        </div>
                                    </form>
                                @endif

                                {{-- Step 2: Contact Info --}}
                                @if ($step === 2)
                                    <div class="nk-kycfm-note mb-3">
                                        <em class="icon ni ni-info-fill"></em>
                                        <p>Check details below before you submit.</p>
                                    </div>
                                    <form wire:submit.prevent="nextStep">
                                        <div class="row g-3 align-center">
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="email">Email Address <span
                                                            class="text-danger">*</span></label>
                                                    <span class="form-note">Enter your email address.</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-7">
                                                <div class="form-group">
                                                    <input type="email" id="email"
                                                        class="form-control bg-primary-dim" wire:model.defer="email"
                                                        placeholder="ENTER EMAIL ADDRESS" required>
                                                    @error('email')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="phone">Phone Number <span
                                                            class="text-danger">*</span></label>
                                                    <span class="form-note">Enter your phone number with country code
                                                        (e.g., 256712345678).</span>
                                                </div>
                                            </div>

                                            <div class="col-lg-7" x-data="{
                                                phoneInput: null,
                                                isValid: false,
                                                isTouched: false,
                                                errorMessage: '',
                                                showValidMessage: false,
                                                validationTimer: null,
                                                init() {
                                                    this.waitForIntlTelInput();
                                                },
                                                waitForIntlTelInput() {
                                                    if (typeof window.intlTelInput === 'function') {
                                                        this.initializePhoneInput();
                                                    } else {
                                                        setTimeout(() => this.waitForIntlTelInput(), 100);
                                                    }
                                                },
                                                initializePhoneInput() {
                                                    this.$nextTick(() => {
                                                        const input = this.$refs.phoneInput;
                                                        
                                                        this.phoneInput = window.intlTelInput(input, {
                                                            initialCountry: 'ug',
                                                            preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi'],
                                                            separateDialCode: true,
                                                            autoPlaceholder: 'aggressive',
                                                            formatOnDisplay: true,
                                                            utilsScript: 'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js'
                                                        });

                                                        if ('{{ $phone }}') {
                                                            this.phoneInput.setNumber('{{ $phone }}');
                                                            this.validateNumber();
                                                        }

                                                        input.addEventListener('focus', () => {
                                                            if (!this.isTouched) this.isTouched = true;
                                                        });
                                                        input.addEventListener('input', () => {
                                                            if (!this.isTouched) this.isTouched = true;
                                                            this.validateNumber();
                                                            this.showValidMessage = false; // Hide immediately when typing
                                                            this.updateLivewire();
                                                        });
                                                        input.addEventListener('blur', () => {
                                                            this.validateNumber();
                                                            this.updateLivewire();
                                                            this.showValidMessageWithDelay();
                                                        });
                                                    });
                                                },
                                                validateNumber() {
                                                    const number = this.phoneInput.getNumber();
                                                    this.isValid = this.phoneInput.isValidNumber();
                                                    
                                                    if (number && !this.isValid) {
                                                        const errorCode = this.phoneInput.getValidationError();
                                                        this.errorMessage = this.getErrorMessage(errorCode);
                                                    } else {
                                                        this.errorMessage = '';
                                                    }
                                                    
                                                    return this.isValid;
                                                },
                                                getErrorMessage(errorCode) {
                                                    const messages = {
                                                        0: 'Invalid phone number',
                                                        1: 'Invalid country code',
                                                        2: 'Too short',
                                                        3: 'Too long',
                                                        4: 'Invalid number'
                                                    };
                                                    return messages[errorCode] || 'Invalid phone number';
                                                },
                                                updateLivewire() {
                                                    const number = this.phoneInput.getNumber();
                                                    if (this.isValid && number) {
                                                        @this.set('phone', number);
                                                    } else {
                                                        @this.set('phone', '');
                                                    }
                                                },
                                                showValidMessageWithDelay() {
                                                    // Clear any existing timer
                                                    if (this.validationTimer) {
                                                        clearTimeout(this.validationTimer);
                                                    }
                                                    
                                                    // Show valid message after 500ms delay if still valid
                                                    this.validationTimer = setTimeout(() => {
                                                        if (this.isValid && this.phoneInput.getNumber()) {
                                                            this.showValidMessage = true;
                                                        }
                                                    }, 500);
                                                },
                                                getInputClasses() {
                                                    let classes = 'form-control bg-primary-dim';
                                                    if (this.isTouched) {
                                                        classes += this.isValid ? ' is-valid' : ' is-invalid';
                                                    }
                                                    return classes;
                                                }
                                            }" wire:ignore class="form-group mb-3">
                                                <div class="form-control-group">
                                                    <input x-ref="phoneInput" 
                                                           type="tel" 
                                                           id="phone"
                                                           name="phone" 
                                                           :class="getInputClasses()"
                                                           placeholder="Enter phone number" 
                                                           autocomplete="tel" 
                                                           required>
                                                    
                                                    <div x-show="isTouched && !isValid" 
                                                         x-text="errorMessage"
                                                         class="invalid-feedback d-block"></div>
                                                    
                                                    <div x-show="isTouched && isValid && showValidMessage" 
                                                         class="valid-feedback d-block">
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="nk-kycfm-action pt-5 d-flex flex-row justify-content-between">
                                            <button type="button" class="btn btn-secondary d-flex"
                                                wire:click="prevStep">
                                                <em class="ni ni-arrow-left mr-2"></em>
                                                <span class="align-self-center">Back</span>
                                            </button>
                                            <button type="submit" class="btn btn-primary d-flex">
                                                <span class="align-self-center">Next</span>
                                                <em class="ni ni-arrow-right ml-2"></em>
                                            </button>
                                        </div>
                                    </form>
                                @endif

                                {{-- Step 3: Documents --}}
                                @if ($step === 3)
                                    <div class="nk-kycfm-note mb-3">
                                        <em class="icon ni ni-info-fill"></em>
                                        <p>Upload all required documents for your selected qualification.</p>
                                    </div>
                                    <form wire:submit.prevent="nextStep">
                                        <div class="row g-3 align-center">
                                            <div class="col-lg-5">
                                                <div class="form-group">
                                                    <label class="form-label" for="qualification_id">Qualification
                                                        Applying For <span class="text-danger">*</span></label>
                                                    <span class="form-note">Select the qualification you are applying
                                                        to be accredited for.</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-7">
                                                <div class="form-group">
                                                    <select id="qualification_id" class="form-control bg-primary-dim"
                                                        wire:model.live="qualification_id" required>
                                                        <option value="">-- Select Qualification --</option>
                                                        @foreach ($qualifications as $q)
                                                            <option value="{{ $q['id'] }}">{{ $q['name'] }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('qualification_id')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        @if ($qualification_id)
                                            {{-- Qualification Instructions --}}
                                            <div class="alert alert-info mt-4">
                                                <h6 class="alert-heading">📋 Required Documents for
                                                    {{ collect($qualifications)->firstWhere('id', $qualification_id)['name'] ?? 'Selected Qualification' }}
                                                </h6>
                                                <div class="mt-2">
                                                    @if ($qualification_id == 1)
                                                        <p><strong>Grade III Teacher Requirements:</strong></p>
                                                        <ul class="mb-0">
                                                            <li>✅ <strong>Required:</strong> Grade III Pass Slip, Grade
                                                                III Certificate, National ID</li>
                                                            <li>📄 <strong>Optional:</strong> O-Level Pass
                                                                Slip/Certificate OR Grade II Certificate (choose one)
                                                            </li>
                                                        </ul>
                                                    @elseif($qualification_id == 2)
                                                        <p><strong>Grade V Teacher (Primary) Requirements:</strong></p>
                                                        <ul class="mb-0">
                                                            <li>✅ <strong>Required:</strong> Grade III Pass Slip, Grade
                                                                III Certificate, Grade V Transcript, Grade V
                                                                Certificate, National ID</li>
                                                            <li>📄 <strong>Where applicable:</strong> O-Level Pass
                                                                Slip/Certificate, Grade III Registration Certificate
                                                            </li>
                                                        </ul>
                                                    @elseif($qualification_id == 3)
                                                        <p><strong>Graduate Teacher (Primary) Requirements:</strong></p>
                                                        <ul class="mb-0">
                                                            <li>✅ <strong>Required:</strong> Grade III Pass Slip, Grade
                                                                III Certificate, Grade V Transcript, Grade V
                                                                Certificate, Degree Transcript, Degree Certificate,
                                                                National ID</li>
                                                            <li>📄 <strong>Where applicable:</strong> O-Level Pass
                                                                Slip/Certificate, Grade III Registration Certificate,
                                                                Grade V Registration Certificate</li>
                                                        </ul>
                                                    @elseif($qualification_id == 4)
                                                        <p><strong>Grade V Teacher (Secondary) Requirements:</strong>
                                                        </p>
                                                        <ul class="mb-0">
                                                            <li>✅ <strong>Required:</strong> O-Level Pass Slip, O-Level
                                                                Certificate, A-Level Pass Slip, A-Level Certificate,
                                                                Grade V Transcript, Grade V Certificate, National ID
                                                            </li>
                                                        </ul>
                                                    @elseif($qualification_id == 5)
                                                        <p><strong>Graduate Teacher (Secondary) Requirements:</strong>
                                                        </p>
                                                        <ul class="mb-0">
                                                            <li>✅ <strong>Required:</strong> O-Level Pass Slip, O-Level
                                                                Certificate, A-Level Pass Slip, A-Level Certificate,
                                                                Grade V Transcript, Grade V Certificate, Degree
                                                                Transcript, Degree Certificate, National ID</li>
                                                            <li>📄 <strong>Where applicable:</strong> Grade V
                                                                Registration Certificate</li>
                                                        </ul>
                                                    @elseif($qualification_id == 6)
                                                        <p><strong>Instructor (Grade V) Requirements:</strong></p>
                                                        <ul class="mb-0">
                                                            <li>✅ <strong>Required:</strong> A-Level Pass Slip, A-Level
                                                                Certificate, Certificate in Technical Teacher Education,
                                                                Diploma in Vocational Training Instruction, National ID
                                                            </li>
                                                            <li>🔄 <strong>Alternative:</strong> O-Level Pass
                                                                Slip/Certificate OR Junior Certificate (choose one)</li>
                                                            <li>📄 <strong>Where applicable:</strong> Craft II/III
                                                                Certificate, National Certificate, Diploma in Technical
                                                                and Vocational Studies</li>
                                                        </ul>
                                                    @elseif($qualification_id == 7)
                                                        <p><strong>Instructor (Graduate) Requirements:</strong></p>
                                                        <ul class="mb-0">
                                                            <li>✅ <strong>Required:</strong> A-Level Pass Slip, A-Level
                                                                Certificate, Degree Certificate, Degree Transcript,
                                                                National ID</li>
                                                            <li>🔄 <strong>Alternative:</strong> O-Level Pass
                                                                Slip/Certificate OR Junior Certificate (choose one)</li>
                                                            <li>📄 <strong>Where applicable:</strong> Craft II/III
                                                                Certificate, National Certificate, Diploma Certificate,
                                                                Diploma Transcript, Diploma in Instructor and Technical
                                                                Education (DITTE), DITTE Transcript</li>
                                                        </ul>
                                                    @elseif($qualification_id == 8)
                                                        <p><strong>Health Tutor Diploma (Grade V) Requirements:</strong>
                                                        </p>
                                                        <ul class="mb-0">
                                                            <li>✅ <strong>Required:</strong> O-Level Certificate,
                                                                Certificate of Registration for Professional Practice,
                                                                Health Tutor Diploma, Health Tutor Certificate, Health
                                                                Tutor Transcript, National ID</li>
                                                            <li>📄 <strong>Where applicable:</strong> Professional
                                                                Certificate, Professional Diploma</li>
                                                        </ul>
                                                    @elseif($qualification_id == 9)
                                                        <p><strong>Health Tutor Degree (Graduate) Requirements:</strong>
                                                        </p>
                                                        <ul class="mb-0">
                                                            <li>✅ <strong>Required:</strong> O-Level Certificate,
                                                                A-Level Certificate, Certificate of Registration for
                                                                Professional Practice, Degree Certificate of Health
                                                                Tutorship, Degree Transcript of Health Tutorship,
                                                                National ID</li>
                                                            <li>📄 <strong>Where applicable:</strong> Professional
                                                                Certificate, Professional Diploma</li>
                                                        </ul>
                                                    @endif
                                                </div>
                                            </div>

                                            {{-- Required Documents Section --}}
                                            <div class="card card-bordered mt-4">
                                                <div class="card-header">
                                                    <h6 class="card-title mb-0">✅ Required Documents</h6>
                                                </div>
                                                <div class="card-body">
                                                    @foreach ($requiredDocuments as $index => $docType)
                                                        <div class="row g-3 align-center mb-3">
                                                            <div class="col-lg-5">
                                                                <div class="form-group">
                                                                    <label class="form-label">{{ $docType }}
                                                                        <span class="text-danger">*</span></label>
                                                                </div>
                                                            </div>
                                                            <div class="col-lg-7">
                                                                <div class="form-group">
                                                                    <input type="file"
                                                                        class="form-control bg-primary-dim"
                                                                        wire:model.live="uploads.{{ $index }}"
                                                                        accept="application/pdf,image/*,.doc,.docx,.xls,.xlsx,.txt,.rtf,.odt,.ods">
                                                                    @error("uploads.$index")
                                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                                    @enderror
                                                                    @if (isset($uploads[$index]) && is_object($uploads[$index]))
                                                                        <div class="mt-1 text-primary"><i
                                                                                class="ni ni-check-circle"></i>
                                                                            {{ $uploads[$index]->getClientOriginalName() }}
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>

                                            {{-- Alternative Documents Section --}}
                                            @if (isset($documentMap['alternatives']) && !empty($documentMap['alternatives']))
                                                <div class="card card-bordered mt-4">
                                                    <div class="card-header">
                                                        <h6 class="card-title mb-0">🔄 Alternative Documents (Choose
                                                            One)</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="alert alert-info mb-3">
                                                            <i class="ni ni-info-fill"></i>
                                                            <strong>Choose ONE of the following:</strong> Select only
                                                            ONE option that matches the documents you have. You cannot
                                                            select multiple options.
                                                        </div>

                                                        @php $alternativeIndex = count($requiredDocuments); @endphp
                                                        @foreach ($documentMap['alternatives'] as $mainDoc => $secondaryDoc)
                                                            <div class="border rounded p-3 mb-3 {{ $selectedAlternative === $mainDoc ? 'border-primary bg-primary-light' : 'bg-light' }}">
                                                                <div class="d-flex align-items-center mb-2">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                            name="alternative_choice"
                                                                            id="option_{{ $loop->index }}"
                                                                            value="{{ $mainDoc }}"
                                                                            wire:model.live="selectedAlternative"
                                                                            required>
                                                                        <label class="form-check-label fw-bold"
                                                                            for="option_{{ $loop->index }}">
                                                                            Option {{ $loop->index + 1 }}: {{ $mainDoc }}@if ($secondaryDoc) & {{ $secondaryDoc }}@endif
                                                                        </label>
                                                                    </div>
                                                                </div>

                                                                @if($selectedAlternative === $mainDoc)
                                                                    <div class="row g-3 align-center">
                                                                        <div class="col-lg-5">
                                                                            <div class="form-group">
                                                                                <label class="form-label">{{ $mainDoc }}</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-lg-7">
                                                                            <div class="form-group">
                                                                                <input type="file"
                                                                                    class="form-control bg-primary-dim"
                                                                                    wire:model.live="uploads.{{ $alternativeIndex }}"
                                                                                    accept="application/pdf,image/*,.doc,.docx,.xls,.xlsx,.txt,.rtf,.odt,.ods">
                                                                                @if (isset($uploads[$alternativeIndex]) && is_object($uploads[$alternativeIndex]))
                                                                                    <div class="mt-1 text-primary"><i class="ni ni-check-circle"></i>
                                                                                        {{ $uploads[$alternativeIndex]->getClientOriginalName() }}
                                                                                    </div>
                                                                                @endif
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    @if ($secondaryDoc)
                                                                        <div class="row g-3 align-center">
                                                                            <div class="col-lg-5">
                                                                                <div class="form-group">
                                                                                    <label class="form-label">{{ $secondaryDoc }}</label>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-lg-7">
                                                                                <div class="form-group">
                                                                                    <input type="file"
                                                                                        class="form-control bg-primary-dim"
                                                                                        wire:model.live="uploads.{{ $alternativeIndex + 1 }}"
                                                                                        accept="application/pdf,image/*,.doc,.docx,.xls,.xlsx,.txt,.rtf,.odt,.ods">
                                                                                    @if (isset($uploads[$alternativeIndex + 1]) && is_object($uploads[$alternativeIndex + 1]))
                                                                                        <div class="mt-1 text-primary"><i class="ni ni-check-circle"></i>
                                                                                            {{ $uploads[$alternativeIndex + 1]->getClientOriginalName() }}
                                                                                        </div>
                                                                                    @endif
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    @endif
                                                                @endif

                                                                @php
                                                                    if ($secondaryDoc) {
                                                                        $alternativeIndex += 2;
                                                                    } else {
                                                                        $alternativeIndex += 1;
                                                                    }
                                                                @endphp
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>

                                                {{-- Where Applicable Documents --}}
                                                @if (!empty($optionalDocuments))
                                                    <div class="card card-bordered mt-4">
                                                        <div class="card-header">
                                                            <h6 class="card-title mb-0">📄 Additional Documents (Where
                                                                Applicable)</h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="alert alert-info mb-3">
                                                                <i class="ni ni-info-fill"></i>
                                                                <strong>Note:</strong> Upload these documents only if
                                                                you have them. They are not required but may strengthen
                                                                your application.
                                                            </div>

                                                            @foreach ($optionalDocuments as $index => $document)
                                                                @php $uploadIndex = $this->getOptionalDocumentIndex($index); @endphp
                                                                <div class="row g-3 align-center mb-3">
                                                                    <div class="col-lg-5">
                                                                        <div class="form-group">
                                                                            <label
                                                                                class="form-label">{{ $document }}
                                                                                <small class="text-muted">(Where
                                                                                    applicable)</small></label>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-lg-7">
                                                                        <div class="form-group">
                                                                            <input type="file"
                                                                                class="form-control bg-primary-dim"
                                                                                wire:model.live="uploads.{{ $uploadIndex }}"
                                                                                accept="application/pdf,image/*,.doc,.docx,.xls,.xlsx,.txt,.rtf,.odt,.ods">
                                                                            @if (isset($uploads[$uploadIndex]) && is_object($uploads[$uploadIndex]))
                                                                                <div class="mt-1 text-primary"><i
                                                                                        class="ni ni-check-circle"></i>
                                                                                    {{ $uploads[$uploadIndex]->getClientOriginalName() }}
                                                                                </div>
                                                                            @endif
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                @endif
                                            @elseif(in_array($qualification_id, [6, 7]))
                                                {{-- Instructor qualifications: O-Level OR Junior Certificate --}}
                                                <div class="card card-bordered mt-4">
                                                    <div class="card-header">
                                                        <h6 class="card-title mb-0">🔄 Alternative Documents (Choose
                                                            One)</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="alert alert-info mb-3">
                                                            <i class="ni ni-info-fill"></i>
                                                            <strong>Choose one of the following:</strong> Either O-Level
                                                            documents OR Junior Certificate (whichever you have).
                                                        </div>

                                                        {{-- Option 1: O-Level Documents --}}
                                                        <div class="border rounded p-3 mb-3"
                                                            style="background-color: #f8f9fa;">
                                                            <div class="d-flex align-items-center mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input" type="radio"
                                                                        name="alternative_choice" id="o_level_option"
                                                                        value="o_level"
                                                                        wire:model.live="selectedAlternative">
                                                                    <label class="form-check-label fw-bold"
                                                                        for="o_level_option">
                                                                        Option 1: O-Level Pass Slip & Certificate
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row g-3 align-center">
                                                                <div class="col-lg-5">
                                                                    <div class="form-group">
                                                                        <label class="form-label">O-Level Pass
                                                                            Slip</label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-lg-7">
                                                                    <div class="form-group">
                                                                        <input type="file"
                                                                            class="form-control bg-primary-dim"
                                                                            wire:model.live="uploads.{{ count($requiredDocuments) }}"
                                                                            accept="application/pdf,image/*,.doc,.docx,.xls,.xlsx,.txt,.rtf,.odt,.ods"
                                                                            placeholder="Upload O-Level Pass Slip">
                                                                        @if (isset($uploads[count($requiredDocuments)]) && is_object($uploads[count($requiredDocuments)]))
                                                                            <div class="mt-1 text-primary"><i
                                                                                    class="ni ni-check-circle"></i>
                                                                                {{ $uploads[count($requiredDocuments)]->getClientOriginalName() }}
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="row g-3 align-center">
                                                                <div class="col-lg-5">
                                                                    <div class="form-group">
                                                                        <label class="form-label">O-Level
                                                                            Certificate</label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-lg-7">
                                                                    <div class="form-group">
                                                                        <input type="file"
                                                                            class="form-control bg-primary-dim"
                                                                            wire:model.live="uploads.{{ count($requiredDocuments) + 1 }}"
                                                                            accept="application/pdf,image/*,.doc,.docx,.xls,.xlsx,.txt,.rtf,.odt,.ods"
                                                                            placeholder="Upload O-Level Certificate">
                                                                        @if (isset($uploads[count($requiredDocuments) + 1]) && is_object($uploads[count($requiredDocuments) + 1]))
                                                                            <div class="mt-1 text-primary"><i
                                                                                    class="ni ni-check-circle"></i>
                                                                                {{ $uploads[count($requiredDocuments) + 1]->getClientOriginalName() }}
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {{-- Option 2: Junior Certificate --}}
                                                        <div class="border rounded p-3 mb-3"
                                                            style="background-color: #f8f9fa;">
                                                            <div class="d-flex align-items-center mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input" type="radio"
                                                                        name="alternative_choice" id="junior_option"
                                                                        value="junior"
                                                                        wire:model.live="selectedAlternative">
                                                                    <label class="form-check-label fw-bold"
                                                                        for="junior_option">
                                                                        Option 2: Junior Certificate
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row g-3 align-center">
                                                                <div class="col-lg-5">
                                                                    <div class="form-group">
                                                                        <label class="form-label">Junior
                                                                            Certificate</label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-lg-7">
                                                                    <div class="form-group">
                                                                        <input type="file"
                                                                            class="form-control bg-primary-dim"
                                                                            wire:model.live="uploads.{{ count($requiredDocuments) + 2 }}"
                                                                            accept="application/pdf,image/*,.doc,.docx,.xls,.xlsx,.txt,.rtf,.odt,.ods">
                                                                        @if (isset($uploads[count($requiredDocuments) + 2]) && is_object($uploads[count($requiredDocuments) + 2]))
                                                                            <div class="mt-1 text-primary"><i
                                                                                    class="ni ni-check-circle"></i>
                                                                                {{ $uploads[count($requiredDocuments) + 2]->getClientOriginalName() }}
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @elseif(count($optionalDocuments) > 0)
                                                {{-- Other optional documents --}}
                                                <div class="card card-bordered mt-4">
                                                    <div class="card-header">
                                                        <h6 class="card-title mb-0">📄 Additional Documents (Where
                                                            Applicable)</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="alert alert-info mb-3">
                                                            <i class="ni ni-info-fill"></i>
                                                            <strong>Note:</strong> Upload these documents only if you
                                                            have them. They are not required but may strengthen your
                                                            application.
                                                        </div>
                                                        @foreach ($optionalDocuments as $index => $docType)
                                                            @php $optionalIndex = count($requiredDocuments) + $index; @endphp
                                                            <div class="row g-3 align-center mb-3">
                                                                <div class="col-lg-5">
                                                                    <div class="form-group">
                                                                        <label class="form-label">{{ $docType }}
                                                                            <small class="text-muted">(Where
                                                                                applicable)</small></label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-lg-7">
                                                                    <div class="form-group">
                                                                        <input type="file"
                                                                            class="form-control bg-primary-dim"
                                                                            wire:model.live="uploads.{{ $optionalIndex }}"
                                                                            accept="application/pdf,image/*,.doc,.docx,.xls,.xlsx,.txt,.rtf,.odt,.ods">
                                                                        @if (isset($uploads[$optionalIndex]) && is_object($uploads[$optionalIndex]))
                                                                            <div class="mt-1 text-primary"><i
                                                                                    class="ni ni-check-circle"></i>
                                                                                {{ $uploads[$optionalIndex]->getClientOriginalName() }}
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endif
                                        @else
                                            <div class="alert alert-info mt-3">Please select a qualification to see
                                                required documents.</div>
                                        @endif
                                        <div class="nk-kycfm-action pt-5 d-flex flex-row justify-content-between">
                                            <button type="button" class="btn btn-secondary d-flex"
                                                wire:click="prevStep">
                                                <em class="ni ni-arrow-left mr-2"></em>
                                                <span class="align-self-center">Back</span>
                                            </button>
                                            <button type="submit" class="btn btn-primary d-flex">
                                                <span class="align-self-center">Next</span>
                                                <em class="ni ni-arrow-right ml-2"></em>
                                            </button>
                                        </div>
                                    </form>
                                @endif

                                {{-- Step 4: Review & Submit --}}
                                @if ($step === 4)
                                    @if ($application_number)
                                        <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                            <div class="display-1 dark-teal mb-3" style="font-size: 5rem;">&#10003;
                                            </div>
                                            <h3 class="dark-teal mb-2">Application Submitted!</h3>
                                            <div class="mb-2">Your application is <strong>pending review</strong>.
                                            </div>
                                            <div class="text-muted">You’ll receive an email confirmation soon.</div>
                                        </div>
                                    @else
                                        <div>
                                            <h5>Review Your Application</h5>
                                            <ul class="list-group mb-3">
                                                <li class="list-group-item"><strong>NIN:</strong> {{ $nin }}
                                                </li>
                                                <li class="list-group-item"><strong>Surname:</strong>
                                                    {{ $surname }}</li>
                                                <li class="list-group-item"><strong>Firtst Name:</strong>
                                                    {{ $firstname }}</li>
                                                <li class="list-group-item"><strong>Other Names:</strong>
                                                    {{ $other_names }}</li>
                                                <li class="list-group-item"><strong>Gender:</strong>
                                                    {{ $genders[$gender] ?? $gender }}</li>
                                                <li class="list-group-item"><strong>Marital Status:</strong>
                                                    @php $status = collect($maritalStatuses)->firstWhere('id', $marital_status_id); @endphp
                                                    {{ $status['name'] ?? $marital_status_id }}
                                                </li>
                                                <li class="list-group-item"><strong>Date of Birth:</strong>
                                                    {{ $date_of_birth }}</li>
                                                <li class="list-group-item"><strong>Email:</strong>
                                                    {{ $email }}</li>
                                                <li class="list-group-item"><strong>Phone:</strong>
                                                    {{ $phone }}</li>
                                                <li class="list-group-item"><strong>Qualification Applying
                                                        For:</strong>
                                                    @php $qualification = collect($qualifications)->firstWhere('id', $qualification_id); @endphp
                                                    {{ $qualification['name'] ?? 'Not Selected' }}
                                                </li>
                                                <li class="list-group-item"><strong>Documents:</strong>
                                                    <ul>
                                                        @if ($qualification_id)
                                                            {{-- Required Documents --}}
                                                            @foreach ($requiredDocuments as $index => $docType)
                                                                <li>
                                                                    <strong>{{ $docType }}:</strong>
                                                                    @if (isset($uploads[$index]) && is_object($uploads[$index]))
                                                                        {{ $uploads[$index]->getClientOriginalName() }}
                                                                    @else
                                                                        <span class="text-danger">Not uploaded</span>
                                                                    @endif
                                                                </li>
                                                            @endforeach

                                                            {{-- Alternative Documents (if applicable) --}}
                                                            @if (isset($documentMap['alternatives']) && !empty($documentMap['alternatives']) && !empty($selectedAlternative))
                                                                @php $alternativeIndex = $this->getAlternativeIndex($selectedAlternative); @endphp
                                                                @if ($alternativeIndex !== null)
                                                                    @foreach ($documentMap['alternatives'] as $mainDoc => $secondaryDoc)
                                                                        @if ($selectedAlternative === $mainDoc)
                                                                            <li>
                                                                                <strong>{{ $mainDoc }}:</strong>
                                                                                @if (isset($uploads[$alternativeIndex]) && is_object($uploads[$alternativeIndex]))
                                                                                    {{ $uploads[$alternativeIndex]->getClientOriginalName() }}
                                                                                @else
                                                                                    <span class="text-danger">Not
                                                                                        uploaded</span>
                                                                                @endif
                                                                            </li>
                                                                            @if ($secondaryDoc)
                                                                                <li>
                                                                                    <strong>{{ $secondaryDoc }}:</strong>
                                                                                    @if (isset($uploads[$alternativeIndex + 1]) && is_object($uploads[$alternativeIndex + 1]))
                                                                                        {{ $uploads[$alternativeIndex + 1]->getClientOriginalName() }}
                                                                                    @else
                                                                                        <span class="text-danger">Not
                                                                                            uploaded</span>
                                                                                    @endif
                                                                                </li>
                                                                            @endif
                                                                            @break
                                                                        @endif
                                                                    @endforeach
                                                                @endif
                                                            @endif

                                                            {{-- Optional Documents (only show submitted ones) --}}
                                                            @if (count($optionalDocuments) > 0)
                                                                @foreach ($optionalDocuments as $index => $docType)
                                                                    @php $uploadIndex = $this->getOptionalDocumentIndex($index); @endphp
                                                                    @if (isset($uploads[$uploadIndex]) && is_object($uploads[$uploadIndex]))
                                                                        <li>
                                                                            <em>{{ $docType }} (Optional):</em>
                                                                            {{ $uploads[$uploadIndex]->getClientOriginalName() }}
                                                                        </li>
                                                                    @endif
                                                                @endforeach
                                                            @endif
                                                        @endif
                                                    </ul>
                                                </li>
                                            </ul>
                                            @if (session()->has('success'))
                                                <div class="alert alert-success">{{ session('success') }}</div>
                                            @endif
                                            @if (session()->has('error'))
                                                <div class="alert alert-danger">{{ session('error') }}</div>
                                            @endif
                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-secondary"
                                                    wire:click="prevStep">Back</button>
                                               

                                            <div x-data="{
                                                confirm() {
                                                    Swal.fire({
                                                        title: 'Submit Application?',
                                                        text: 'Are you sure all details are correct?',
                                                        icon: 'question',
                                                        showCancelButton: true,
                                                        confirmButtonText: 'Yes, Submit',
                                                        cancelButtonText: 'Cancel'
                                                    }).then((result) => {
                                                        if (result.isConfirmed) {
                                                            $wire.submitApplication();
                                                        }
                                                    });
                                                }
                                            }">
                                                <button type="button" class="btn badge-dark-teal" @click="confirm">
                                                    <em class="icon ni ni-send"></em>
                                                    <span>Submit Application</span>
                                                </button>
                                            </div>
                                            </div>
                                            @if ($application_number)
                                                <div class="alert alert-info mt-3">
                                                    <strong>Your Application Number:</strong>
                                                    {{ $application_number }}<br>
                                                </div>
                                            @endif
                                        </div>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
