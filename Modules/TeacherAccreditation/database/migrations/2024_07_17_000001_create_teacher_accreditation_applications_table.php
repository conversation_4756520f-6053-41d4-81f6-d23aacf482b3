<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('teacher_accreditation_applications', function (Blueprint $table) {
            $table->bigIncrements('id'); // PK
            $table->string('nin');
            $table->string('email');
            $table->string('phone');
            $table->string('surname');
            $table->string('firstname');
            $table->string('other_names');
            $table->enum('gender', ['Male', 'Female', 'Other']);
            $table->unsignedBigInteger('marital_status_id');
            $table->date('date_of_birth');
            $table->enum('approval_status', ['PENDING', 'APPROVED', 'REJECTED']);
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('date_approved')->nullable();
            $table->text('reject_reason')->nullable();
            $table->string('application_number');
            $table->unsignedBigInteger('qualification_id')->nullable();
            $table->timestamps(); // date_created, date_updated

            
            $table->foreign('qualification_id')
                ->references('id')->on('setting_teacher_accreditation_qualifications')
                ->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::dropIfExists('teacher_accreditation_applications');
    }
}; 