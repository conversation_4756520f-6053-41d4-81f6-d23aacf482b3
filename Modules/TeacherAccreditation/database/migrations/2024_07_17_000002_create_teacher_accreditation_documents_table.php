<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('teacher_accreditation_documents', function (Blueprint $table) {
            $table->bigIncrements('id'); // Primary key
            $table->unsignedBigInteger('teacher_accreditation_application_id'); // Foreign key
            $table->enum('document_type', [
                'O-Level Pass Slip',
                'O-Level Certificate',
                'A-Level Pass Slip',
                'A-Level Certificate',
                'Grade II Certificate',
                'Grade III Pass Slip',
                'Grade III Certificate',
                'Grade III Registration Certificate',
                'Grade V Transcript',
                'Grade V Certificate',
                'Grade V Registration Certificate',
                'Degree Transcript',
                'Degree Certificate',
                'Diploma in Technical and Vocational Studies',
                'Craft II Certificate',
                'Craft III Certificate',
                'National Certificate',
                'Certificate in Technical Teacher Education',
                'Diploma in Vocational Training Instruction',
                'Diploma in Instructor and Technical Education (DITTE)',
                'Professional Certificate',
                'Professional Diploma',
                'Certificate of Registration for Professional Practice',
                'Health Tutor Diploma',
                'Health Tutor Certificate',
                'Health Tutor Transcript',
                'Degree Certificate of Health Tutorship',
                'Degree Transcript of Health Tutorship',
                'NIRA Application Form',
                'National ID',
                'Passport',
                'Workplace ID',
                'University/College ID',
                'Driving Permit',
                'Equated Academic/Professional Document',
                'Junior Certificate',
                'Diploma Certificate',
                'Diploma Transcript',
                'DITTE Transcript',
                'Professional Certificate at Certificate Level',
                'Professional Certificate at Diploma Level',
                'Other'
            ]);
            $table->string('document_url');
            $table->timestamps();

            $table->foreign('teacher_accreditation_application_id', 'teacher_accreditation_application_id_FK')
                ->references('id')->on('teacher_accreditation_applications')
                ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('teacher_accreditation_documents');
    }
}; 