<div wire:key="survey-container">
    <h3 class="text-center text-uppercase">
        EMIS BASELINE INFORMATION FORM
    </h3>

    <!-- Main Content -->
    <div class="card card-stretch card-bordered border-dark-teal">
        <div class="card-aside-wrap">
            <!-- Side Menu -->
            <div
                class="card-aside card-aside-left user-aside toggle-slide toggle-slide-left toggle-break-lg {{ $sideMenu ? 'content-active' : '' }}"
                data-content="userAside"
                data-toggle-screen="lg"
                data-toggle-overlay="true">
                <div class="card-inner-group">
                    <div class="card-inner p-0">
                        <ul class="link-list-menu">
                            @foreach($sections as $section)
                                <li
                                    id="menuSectionItem{{ $section['id']}}"
                                    wire:click="changeSectionById('{{ $section['id'] }}')"
                                    x-on:click="history.pushState({}, '', `{{ route('emisreturns.survey-container', ['survey_id' => $survey_id, 'action'=>'view','section_id'=>$section['id'] ]) }}`)"
                                    class="cursor mb-2"
                                >
                                    <a class="{{ $currentSection['id'] === $section['id'] ? 'bg-dark-teal text-white' : '' }}">
                                        <em class="icon ni ni-grid-add-fill-c {{ $currentSection['id'] === $section['id'] ? 'text-white' : '' }}"></em>
                                        <span class="text-uppercase">{{ $section['name'] }}</span>
                                    </a>
                                </li>
                                {{-- {{ dd($currentSection == $section['logical']) }} --}}
                                {{-- <li id="menuSectionItem{{ $section['id'] }}" class="cursor">
                                    <a href="{{ route('blog.by.slug', ['survey_id' => $survey_id, 'section_id'=>$section['id'] ]) }}"
                                        class="{{ $currentSection === $section['logical'] ? 'bg-dark-teal text-white' : '' }}" wire:navigate>
                                        <span class="text-uppercase">{{ $section['name'] }}</span>
                                    </a>
                                </li> --}}
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            @if ($sideMenu)
                <div wire:click="toggleSideMenu()" class="toggle-overlay" data-target="userAside"></div>
            @else
                <div class="progress-bar-container" wire:loading>
                    <div class="progress-bar">
                        <div class="progress-bar-value"></div>
                    </div>
                </div>
            @endif
            <!-- Main Content Area -->
            <div class="card-inner card-inner-lg">
                <div class="card card-preview">
                    <div class="nk-block-head nk-block-head-lg">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title text-uppercase">{{ $title }}</h4>
                                <div class="nk-block-des">
                                    @if(!empty($currentSection['description']))
                                        <div class="mb-2">{{ $currentSection['description'] }}</div>
                                    @else
                                        <p>Update all details that are marked
                                            <span class="text-muted text-uppercase font-italic">NOT SET</span>
                                        </p>
                                    @endif
                                </div>
                            </div>
                            <!-- Toggle Button for Mobile View -->
                            <div class="nk-block-head-content align-self-start d-lg-none">
                                {{-- <a wire:click="toggleSideMenu()" data-target="userAside" class="toggle btn btn-icon btn-trigger mt-n1 cursor"> --}}
                                <a wire:click="toggleSideMenu()" class="toggle btn btn-icon btn-trigger mt-n1 cursor">
                                    <em class="icon ni ni-menu-alt-r"></em>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="card-inner card-inner-lg">
                    @if(isset($currentSection['component']))
                        @livewire($currentSection['component'], [
                        'schoolType' => $schoolType,
                        'survey_id' => $survey_id,
                        'survey' => $survey
                        ], key($currentSection['id']))
                    @else
                        <div class="alert alert-info">Please select a section from the menu.</div>
                    @endif
                    </div>

                    <!-- Navigation Buttons -->
                    @php

                        //$currentIndex = collect($sections)->pluck('logical')->search($currentSection);
                        $currentIndex = collect($sections)->search($currentSection);
                        //dd($currentIndex);
                        //dd(collect($sections));
                    @endphp

                    <div class="mt-4 mb-3 d-flex flex-row justify-content-end">
                        @if($currentIndex > 0)
                            <button wire:click="changeSectionById('{{ $sections[$currentIndex - 1]['id'] }}')" class="btn btn-outline-dark-teal me-2">
                                <em class="ni ni-arrow-left mr-1"></em>
                                <span class="align-self-center">BACK</span>
                            </button>
                        @else
                            <div></div>
                        @endif

                        <div>
                            @if($currentIndex < count($sections) - 1)
                                <button wire:click="changeSectionById('{{ $sections[$currentIndex + 1]['id'] }}')" class="btn btn-primary me-2">
                                    <span class="align-self-center">NEXT</span>
                                    <em class="ni ni-arrow-right ml-1"></em>
                                </button>
                            @else
                                <button wire:click="submitSurvey()" class="btn btn-secondary me-2">
                                    <span class="align-self-center">SUBMIT</span>
                                    <em class="ni ni-arrow-right ml-1"></em>
                                </button>
                            @endif
                        </div>
                    </div>
                    <!-- End Navigation Buttons -->

                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        // Alpine.js component for curriculum selection
        function curriculumSelector() {
            return {
                selectedCurriculum: '',
                init() {
                    // Initialize with the current Livewire value
                    this.selectedCurriculum = this.$wire.get('form_learner.inter_sch_curriculum_id') || '';
                },
                handleCurriculumChange() {
                    // Update Livewire property
                    this.$wire.set('form_learner.inter_sch_curriculum_id', this.selectedCurriculum);

                    // Call the public Livewire method to load grades
                    this.$wire.call('changeCurriculum', this.selectedCurriculum);
                }
            };
        }

        // Alpine.js component for grade selection with education level update
        function gradeSelector() {
            return {
                selectedGrade: '',
                init() {
                    // Initialize with the current Livewire value if any
                    this.selectedGrade = this.$wire.get('form_learner.inter_sch_education_grade_id') || '';
                },
                handleGradeChange() {
                    // Update Livewire property
                    this.$wire.set('form_learner.inter_sch_education_grade_id', this.selectedGrade);

                    // Call method to update education level
                    this.$wire.call('updateEducationLevel', this.selectedGrade);
                }
            };
        }

        //For Levels
        function educationGradeWatcher(boundValue) {
            return {
                educationGradeId: boundValue,

                init() {
                    this.$watch('educationGradeId', (value) => {
                        const isAlevel = ['16', '17'].includes(value);
                        this.$wire.call('setAlevelStatus', isAlevel);
                    });

                    setTimeout(() => {
                        const isAlevel = ['16', '17'].includes(this.educationGradeId);
                        this.$wire.call('setAlevelStatus', isAlevel);
                    }, 100);
                }
            };
        }

        // For Country
        function countryWatcher(boundCountryId) {
            return {
                countryId: boundCountryId,

                init() {
                    // Initialize Select2
                    $('#learnerCountryId').select2({
                        minimumResultsForSearch: 0,
                        containerCssClass: 'bg-primary-dim',
                    });

                    // Watch for changes in countryId and update Uganda status
                    this.$watch('countryId', (value) => {
                        const isUganda = value == '221';
                        this.$wire.call('setUgandaStatus', isUganda);
                    });

                    // Set initial state
                    setTimeout(() => {
                        const isUganda = this.countryId == '221';
                        this.$wire.call('setUgandaStatus', isUganda);
                    }, 100);
                }
            };
        }

        // For Subjects
        function subjectFunctions() {
            return {
                selected: {
                    principal: [],
                    subsidiary: [],
                },

                init() {
                    this.selected.principal = [...this.$wire.form_learner.principal_subjects];
                    this.selected.subsidiary = [...this.$wire.form_learner.subsidiary_subject];
                },

                toggle(type, id) {
                    const list = this.selected[type];
                    const index = list.indexOf(id);
                    index === -1 ? list.push(id) : list.splice(index, 1);
                    this.$wire.form_learner[`${type}_subjects`] = list;
                },

                isChecked(type, id) {
                    return this.selected[type].includes(id);
                },

                isDisabled(type, id, max) {
                    const list = this.selected[type];
                    return list.length >= max && !list.includes(id);
                }
            };
        }
    </script>
@endpush
