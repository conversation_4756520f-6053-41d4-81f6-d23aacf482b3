<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class NiraProxyAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $token = config('nira.services.token');
        if (config('nira.services.use_proxy')) {
           // $request->headers->set('Authorization', 'Bearer ' . $token);
        }else{
             $authToken = $request->bearerToken();
            if ($token !== $authToken) {
                return response()->json(['error' => 'NIRA authentication token '], 401);
            }
        }
        return $next($request);
    }
}
