<?php

namespace App\Providers;

use Modules\Core\Models\Institutions\EmisNumberApplication;
use Modules\Core\Models\Institutions\Learner;
use Modules\Core\Models\Institutions\School;
use Modules\Core\Models\Institutions\SchoolContact;
use Modules\Core\Models\Institutions\SchoolEmployee;
use Modules\Core\Models\Institutions\SchoolLicenseCertificate;
use Modules\Core\Models\Institutions\SchoolRegistrationApproval;
use Modules\Core\Models\Person;
use Modules\Core\Models\SchoolExpectedEnrolment;
use Modules\Core\Models\Settings\LearnerTransferReason;
use Modules\Core\Models\Settings\LicensingAndRegistrationRequirement;
use Modules\Core\Models\Settings\SettingMeCategory;
use Modules\Core\Models\Settings\SettingMeCompletionStatus;
use Modules\Core\Models\Settings\SettingMeCurrency;
use Modules\Core\Models\Settings\SettingMeFunder;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Database\Schema\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Events\BeforeImport;
use Maatwebsite\Excel\Reader;
use Modules\Core\Observers\Institutions\EmisNumberApplicationObserver;
use Modules\Core\Observers\Institutions\LearnerObserver;
use Modules\Core\Observers\Institutions\SchoolContactObserver;
use Modules\Core\Observers\Institutions\SchoolEmployeeObserver;
use Modules\Core\Observers\Institutions\SchoolLicenseCertificateObserver;
use Modules\Core\Observers\Institutions\SchoolObserver;
use Modules\Core\Observers\Institutions\SchoolRegistrationApprovalObserver;
use Modules\Core\Observers\PersonObserver;
use Opcodes\LogViewer\Facades\LogViewer;
use PhpOffice\PhpSpreadsheet\Settings;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\Facades\App;

class AppServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * This is used by Laravel authentication to redirect users after login.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Register any application services.
     */
    public function register(): void
    {
        Reader::listen(BeforeImport::class, function () {
            $options = LIBXML_COMPACT | LIBXML_PARSEHUGE;
            Settings::setLibXmlLoaderOptions($options);
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Builder::defaultStringLength(191);

        // School
        School::observe(SchoolObserver::class);

        //Learner
        Learner::observe(LearnerObserver::class);

        //Person
        Person::observe(PersonObserver::class);

        //School Employee
        SchoolEmployee::observe(SchoolEmployeeObserver::class);

        // School contact person
        SchoolContact::observe(SchoolContactObserver::class);

        // School Approvals
        SchoolRegistrationApproval::observe(SchoolRegistrationApprovalObserver::class);

        //EMIS Number Applications
        EmisNumberApplication::observe(EmisNumberApplicationObserver::class);

        //School License
        SchoolLicenseCertificate::observe(SchoolLicenseCertificateObserver::class);

     
        //
        Blade::if('learnerSummaryForm', function () {
            $user = auth()->user();
            $school = null;
            // echo var_dump($user->school);exit;
            if ($user->hasRole('institution-admin')) {
                $school = $user->school;
            } elseif ($user->hasRole('institution-user')) {
                $school = $user->school_contact->school;
            }
            if (! $school) {
                return false;
            }
            $row = SchoolExpectedEnrolment::where('school_id', $school->id)->first();

            return ! isset($row); //session()->get('ere', false);
            // return true;//auth()->user() && $user->capability == 3;

        });

        //Log viewer
        LogViewer::auth(function ($request) {
            return $request->user()
                && in_array($request->user()->email, [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                ]);
        });

        $this->bootAuth();
        $this->bootRoute();
    }

    public function bootAuth(): void
    {
        // Passport::cookie(Str::slug(env('APP_NAME', 'laravel'), '_').'_token');
    }

    public function bootRoute(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        Route::bind('learnerTransferReason', function ($id) {
            return LearnerTransferReason::withoutGlobalScope('visible')->findOrFail($id);
        });
        Route::bind('requirement', function ($id) {
            return LicensingAndRegistrationRequirement::withoutGlobalScope('visible')->findOrFail($id);
        });
        Route::bind('currency', function ($id) {
            return SettingMeCurrency::withoutGlobalScope('visible')->findOrFail($id);
        });
        Route::bind('category', function ($id) {
            return SettingMeCategory::withoutGlobalScope('visible')->findOrFail($id);
        });
        Route::bind('completion_status', function ($id) {
            return SettingMeCompletionStatus::withoutGlobalScope('visible')->findOrFail($id);
        });
        Route::bind('funder', function ($id) {
            return SettingMeFunder::withoutGlobalScope('visible')->findOrFail($id);
        });

    }
}
