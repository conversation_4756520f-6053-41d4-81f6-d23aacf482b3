<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixSpatieRolesLaravel11Bindings extends Command
{
    protected $signature = 'fix:spatie-laravel11-bindings';
    protected $description = 'Fix model_type references in model_has_roles and model_has_permissions';

    public function handle()
    {
        $replacements = [
            'Modules\Core\Models\User' => 'App\Models\User',
            'Modules\Core\Models\Settings\SettingAdminModule' => 'App\Models\Settings\SettingAdminModule',
        ];

        foreach ($replacements as $oldModel => $newModel) {
            $countPermissions = DB::table('model_has_permissions')
                ->where('model_type', $oldModel)
                ->update(['model_type' => $newModel]);

            $countRoles = DB::table('model_has_roles')
                ->where('model_type', $oldModel)
                ->update(['model_type' => $newModel]);

            $this->info("Updated {$countPermissions} permission records and {$countRoles} role records from '{$oldModel}' to '{$newModel}'.");
        }

        $this->info('All model_type bindings updated.');
    }
}
