.nk-menu-item + .nk-menu-heading {
    padding-top: 1.5rem
}

.nk-auth-body {
    padding: 2.5rem;
}

.roboto{
    font-family: "Roboto", sans-serif, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif !important;
}

.lead-nav-links {
    font-size: 1.0rem !important;
}

/*.lead-text {*/
/*    font-size: 0.974rem;*/
/*}*/

.lead-text + .sub-text {
    font-size: 16px;
}

.lead-text + .sub-text + .sub-text {
    font-size: 16px;
}

.profile-ud {
    font-size: large;
}

.wider .profile-ud-label {
    width: 200px;
}

/*.timeline-title {*/
/*    font-size: 18px;*/
/*}*/
.timeline-des {
    font-size: large;
}

/*.timeline .time {*/
/*    font-size: 17px;*/
/*}*/

.user-info .lead-text, .user-info .sub-text {
    font-size: 15px;
}

/*settings metadat*/
.nav-tabs .nav-item:last-child {
    padding-right: 10px;
}

.page-title {
    font-size: x-large;
}

.card-inner-lg {
    padding: 1.2rem;
}

.parallax {
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-perspective: 200px;
    perspective: 300px;
}

@media (min-width: 576px) {
    .link-list-menu a {
        padding: 0.5rem 1.5rem;
    }
}




.certificate_licence{
    display:block;
    width:100%;
    text-align:center;
    padding:25px 55px;
    padding-bottom:70px;
    border:3px solid #000;
}
/*.certificate_licence div{
    display:block;
    width:100%;
}*/
.cert_no{
    text-align:right !important;
    margin-bottom:-35px;
    font-size:150%;
    font-weight:bold;
}
.cert_no sup{

}
.license_letter{
    display:block;
    width:100%;
    margin-left:auto;
    margin-right:auto;
    margin-top:15px;
    padding:35px 25px;
    border:1px solid #000;
}
.letter_head_left{
    font-size:11px;
}
.letter_head_left p{
    display:block;
    float:left;
    width:100%;
    margin-bottom:2px;
}
.letter_head_left p > span{
    display:block;
    float:left;
    font-weight:bold;
}
.letter_head_left p > span.lh_det{
    float:right;
}
.min_address p{
    margin-bottom:3px !important;
    font-size:14px;
}
.min_title{
    margin-bottom:15px;
}
.cert_title{
    margin-bottom:8px;
}

dl {
    margin-top: 0;
    margin-bottom: 10px;
}
dt,
dd {
    line-height: 1.42857143;
}
dt {
    font-weight: bold;
}
dt:after {
    content: ':';
}
dd {
    margin-left: 0;
}
@media (min-width: 768px) {
    .dl-horizontal dt {
        float: left;
        width: 160px;
        overflow: hidden;
        clear: left;
        text-align: right;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .dl-horizontal dd {
        margin-left: 180px;
    }
}

.actions ul {
    display: block !important;
    text-align: right;
}
.actions ul li[aria-hidden="false"] {
    display: inline-block !important;
}

.actions ul li a {
    border-color: #6506f8;
    background: #6506f8;
}

.actions ul li a[href='#next']:after{
    font-family: "Nioicon" !important;
    padding-left:7px;
    content: "";
}

.actions ul li a[href='#previous']:before{
    font-family: "Nioicon" !important;
    content: "";
    padding-right:7px;
}

.actions ul li a[href='#finish']{
    border-color: #0e850e !important;
    background: #0e850e !important;
}

.lr.school_details dl.dl-horizontal > dd{
    text-transform: uppercase !important;
}

.pagination .page-item.active-dark-teal .page-link,
.pagination .page-item.active .page-link,
.pagination .page-link.bg-dark-teal {
    background-color: #00879B !important; /* dark teal */
    color: #fff !important;
    border-color: #00879B !important;
}

.pagination .page-link {
    color: #00879B;
    border: 1px solid #dee2e6;
    background: #fff;
    transition: background 0.2s, color 0.2s;
}

.pagination .page-link.cursor {
    cursor: pointer;
}

.pagination .page-item.disabled .page-link {
    color: #bdbdbd;
    background: #f8f9fa;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* Show select design */
.form-select-sm {
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #00879B;
    background: #fff;
    transition: border-color 0.2s, box-shadow 0.2s;
}
.form-select-sm:focus {
    outline: none;
    border-color: #00879B;
    box-shadow: 0 0 0 2px rgba(0,77,77,0.15);
}
